// Primary icon system using Lucide React
import {
  Activity,
  AlertCircle,
  Archive,
  ArrowLeft,
  ArrowRight,
  BarChart3,
  Battery,
  Bell,
  Briefcase,
  Calendar,
  Camera,
  Check,
  ChevronDown, // This is the correct name in lucide-react
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Cloud,
  CloudRain,
  Copy,
  CreditCard,
  Crown,
  Diamond,
  DollarSign,
  Download,
  Edit,
  ExternalLink,
  Eye,
  EyeOff,
  File,
  FileText,
  Filter,
  Flame,
  FolderOpen,
  Gamepad2,
  Globe,
  Headphones,
  Heart,
  Home,
  Image,
  Info,
  Leaf,
  Lock,
  Mail,
  MapPin,
  Megaphone,
  Menu,
  Mic,
  Moon,
  MoreHorizontal,
  MoreVertical,
  Music,
  Palette,
  Phone,
  PieChart,
  Plus,
  Radio,
  RefreshCw,
  Save,
  Search,
  Settings,
  Share,
  Shield,
  Snowflake,
  Star,
  Sun,
  Thermometer,
  Trash2,
  TrendingDown,
  TrendingUp,
  Unlock,
  Upload,
  User,
  Video,
  Wifi,
  WifiOff,
  X,
  Zap
} from 'lucide-react';

// Secondary icons using Tabler Icons for specialized use
import {
  IconApi,
  IconAtom,
  IconBrain,
  IconBrandDiscord,
  IconBrandFigma,
  IconBrandGithub,
  IconBrandLinkedin,
  IconBrandSlack,
  IconBrandTwitter,
  IconBrandVscode,
  IconBulb,
  IconChartBar,
  IconChartDots,
  IconChartLine,
  IconCloudComputing,
  IconCode,
  IconCpu,
  IconDatabase,
  IconFlask,
  IconGitBranch,
  IconMicroscope,
  IconPackage,
  IconReportAnalytics,
  IconRocket,
  IconServer,
  IconTarget,
  IconTerminal,
  IconTool
} from '@tabler/icons-react';

// Icon component with consistent sizing
interface IconProps {
  size?: number;
  className?: string;
  strokeWidth?: number;
}

export const Icons = {
  // === BASIC UI ICONS (Lucide) ===
  search: (props: IconProps = {}) => <Search size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  user: (props: IconProps = {}) => <User size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  settings: (props: IconProps = {}) => <Settings size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  home: (props: IconProps = {}) => <Home size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  menu: (props: IconProps = {}) => <Menu size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  bell: (props: IconProps = {}) => <Bell size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === ACTIONS (Lucide) ===
  add: (props: IconProps = {}) => <Plus size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  close: (props: IconProps = {}) => <X size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  check: (props: IconProps = {}) => <Check size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  edit: (props: IconProps = {}) => <Edit size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  delete: (props: IconProps = {}) => <Trash2 size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  save: (props: IconProps = {}) => <Save size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  copy: (props: IconProps = {}) => <Copy size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  refresh: (props: IconProps = {}) => <RefreshCw size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === NAVIGATION (Lucide) ===
  chevronDown: (props: IconProps = {}) => <ChevronDown size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  chevronLeft: (props: IconProps = {}) => <ChevronLeft size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  chevronRight: (props: IconProps = {}) => <ChevronRight size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  chevronUp: (props: IconProps = {}) => <ChevronUp size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  arrowLeft: (props: IconProps = {}) => <ArrowLeft size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  arrowRight: (props: IconProps = {}) => <ArrowRight size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  moreHorizontal: (props: IconProps = {}) => <MoreHorizontal size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  moreVertical: (props: IconProps = {}) => <MoreVertical size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === STATUS (Lucide with color variants) ===
  success: (props: IconProps = {}) => <Check size={props.size || 20} className={`text-green-500 ${props.className || ''}`} strokeWidth={props.strokeWidth} />,
  error: (props: IconProps = {}) => <AlertCircle size={props.size || 20} className={`text-red-500 ${props.className || ''}`} strokeWidth={props.strokeWidth} />,
  warning: (props: IconProps = {}) => <AlertCircle size={props.size || 20} className={`text-yellow-500 ${props.className || ''}`} strokeWidth={props.strokeWidth} />,
  info: (props: IconProps = {}) => <Info size={props.size || 20} className={`text-blue-500 ${props.className || ''}`} strokeWidth={props.strokeWidth} />,
  
  // === FILES & MEDIA (Lucide) ===
  file: (props: IconProps = {}) => <File size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  fileText: (props: IconProps = {}) => <FileText size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  folder: (props: IconProps = {}) => <FolderOpen size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  image: (props: IconProps = {}) => <Image size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  video: (props: IconProps = {}) => <Video size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  music: (props: IconProps = {}) => <Music size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  archive: (props: IconProps = {}) => <Archive size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  download: (props: IconProps = {}) => <Download size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  upload: (props: IconProps = {}) => <Upload size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === COMMUNICATION (Lucide) ===
  mail: (props: IconProps = {}) => <Mail size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  phone: (props: IconProps = {}) => <Phone size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  share: (props: IconProps = {}) => <Share size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  externalLink: (props: IconProps = {}) => <ExternalLink size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === GENERAL (Lucide) ===
  calendar: (props: IconProps = {}) => <Calendar size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  location: (props: IconProps = {}) => <MapPin size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  star: (props: IconProps = {}) => <Star size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  heart: (props: IconProps = {}) => <Heart size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  filter: (props: IconProps = {}) => <Filter size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  eye: (props: IconProps = {}) => <Eye size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  eyeOff: (props: IconProps = {}) => <EyeOff size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  lock: (props: IconProps = {}) => <Lock size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  unlock: (props: IconProps = {}) => <Unlock size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === SYSTEM & TECH (Lucide) ===
  zap: (props: IconProps = {}) => <Zap size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  shield: (props: IconProps = {}) => <Shield size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  globe: (props: IconProps = {}) => <Globe size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  wifi: (props: IconProps = {}) => <Wifi size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  wifiOff: (props: IconProps = {}) => <WifiOff size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  battery: (props: IconProps = {}) => <Battery size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === WEATHER & ENVIRONMENT (Lucide) ===
  sun: (props: IconProps = {}) => <Sun size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  moon: (props: IconProps = {}) => <Moon size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  cloud: (props: IconProps = {}) => <Cloud size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  cloudRain: (props: IconProps = {}) => <CloudRain size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  thermometer: (props: IconProps = {}) => <Thermometer size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === ANALYTICS & CHARTS (Lucide) ===
  activity: (props: IconProps = {}) => <Activity size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  barChart: (props: IconProps = {}) => <BarChart3 size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  pieChart: (props: IconProps = {}) => <PieChart size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  trendingUp: (props: IconProps = {}) => <TrendingUp size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  trendingDown: (props: IconProps = {}) => <TrendingDown size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === BRAND ICONS (Tabler) ===
  github: (props: IconProps = {}) => <IconBrandGithub size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  twitter: (props: IconProps = {}) => <IconBrandTwitter size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  linkedin: (props: IconProps = {}) => <IconBrandLinkedin size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  discord: (props: IconProps = {}) => <IconBrandDiscord size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  slack: (props: IconProps = {}) => <IconBrandSlack size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  figma: (props: IconProps = {}) => <IconBrandFigma size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  vscode: (props: IconProps = {}) => <IconBrandVscode size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  
  // === TECHNICAL SPECIALIZED (Tabler) ===
  database: (props: IconProps = {}) => <IconDatabase size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  api: (props: IconProps = {}) => <IconApi size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  server: (props: IconProps = {}) => <IconServer size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  terminal: (props: IconProps = {}) => <IconTerminal size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  code: (props: IconProps = {}) => <IconCode size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  cpu: (props: IconProps = {}) => <IconCpu size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  gitBranch: (props: IconProps = {}) => <IconGitBranch size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  package: (props: IconProps = {}) => <IconPackage size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  cloudComputing: (props: IconProps = {}) => <IconCloudComputing size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  
  // === SCIENCE & INNOVATION (Tabler) ===
  brain: (props: IconProps = {}) => <IconBrain size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  atom: (props: IconProps = {}) => <IconAtom size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  microscope: (props: IconProps = {}) => <IconMicroscope size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  flask: (props: IconProps = {}) => <IconFlask size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  bulb: (props: IconProps = {}) => <IconBulb size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  rocket: (props: IconProps = {}) => <IconRocket size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  target: (props: IconProps = {}) => <IconTarget size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  tool: (props: IconProps = {}) => <IconTool size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
  
  // === ADVANCED ANALYTICS (Tabler) ===
  chartDots: (props: IconProps = {}) => <IconChartDots size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  chartLine: (props: IconProps = {}) => <IconChartLine size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  reportAnalytics: (props: IconProps = {}) => <IconReportAnalytics size={props.size || 20} className={props.className} stroke={props.strokeWidth} />,
  analyticsBar: (props: IconProps = {}) => <IconChartBar size={props.size || 20} className={props.className} strokeWidth={props.strokeWidth} />,
};

export type IconName = keyof typeof Icons;

// Named exports for backward compatibility
export const ActivityIcon = Icons.activity;
export const BookTextIcon = Icons.fileText;
export const ChartLineIcon = Icons.chartLine;
export const CpuIcon = Icons.cpu;

// Usage examples:
// <Icons.search />
// <Icons.user size={24} />
// <Icons.settings className="text-gray-500" strokeWidth={1.5} />
// <Icons.github size={32} className="text-gray-700" />
// <Icons.success size={16} />  // Will be green automatically
// <Icons.database size={20} className="text-blue-600" />
