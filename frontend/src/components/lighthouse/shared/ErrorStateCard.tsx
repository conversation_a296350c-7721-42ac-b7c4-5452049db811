import React from 'react';
import { motion } from 'framer-motion';
import { cn, typography, layout, animations, errorStates } from '~/lib/ui-utils';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { AlertTriangleIcon, RefreshCw, Bug, XIcon } from './icons';

interface ErrorStateCardProps {
  title?: string;
  message?: string;
  severity?: 'critical' | 'warning' | 'info';
  showRetry?: boolean;
  showDismiss?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  animated?: boolean;
  details?: string;
  errorCode?: string;
}

export function ErrorStateCard({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  severity = 'critical',
  showRetry = true,
  showDismiss = false,
  onRetry,
  onDismiss,
  className,
  animated = true,
  details,
  errorCode,
}: ErrorStateCardProps) {
  const severityConfig = errorStates[severity];
  
  const getIcon = () => {
    switch (severity) {
      case 'critical':
        return <Bug size={24} className={severityConfig.icon} />;
      case 'warning':
        return <AlertTriangleIcon size={24} className={severityConfig.icon} />;
      case 'info':
        return <AlertTriangleIcon size={24} className={severityConfig.icon} />;
      default:
        return <AlertTriangleIcon size={24} className={severityConfig.icon} />;
    }
  };

  const cardContent = (
    <Card className={cn(
      'w-full max-w-md',
      severityConfig.background,
      animated && animations.liftOnHover,
      className
    )}>
      <CardHeader className="pb-3">
        <CardTitle className={cn(
          typography.h4,
          'flex items-center gap-3',
          severityConfig.text
        )}>
          {getIcon()}
          <span>{title}</span>
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className={cn(
                'ml-auto h-6 w-6 p-0',
                'hover:bg-red-100 dark:hover:bg-red-900'
              )}
            >
              <XIcon size={14} />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className={cn(layout.spaceY.md)}>
        <p className={cn(typography.body, severityConfig.text)}>
          {message}
        </p>
        
        {details && (
          <details className="mt-3">
            <summary className={cn(
              typography.bodySmall,
              'cursor-pointer text-muted-foreground hover:text-foreground'
            )}>
              Show details
            </summary>
            <div className={cn(
              'mt-2 p-3 rounded-md bg-muted/50',
              typography.bodySmall,
              'font-mono text-muted-foreground'
            )}>
              {details}
            </div>
          </details>
        )}
        
        {errorCode && (
          <div className={cn(
            'text-xs text-muted-foreground',
            'font-mono bg-muted/50 px-2 py-1 rounded'
          )}>
            Error Code: {errorCode}
          </div>
        )}
        
        {(showRetry || showDismiss) && (
          <div className={cn(layout.flexStart, 'gap-2 pt-2')}>
            {showRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className={cn(
                  'transition-all duration-200',
                  animations.scaleOnHover,
                  'border-current hover:bg-current hover:text-white'
                )}
              >
                <RefreshCw size={14} className="mr-2" />
                Try Again
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (animated) {
    return (
      <AnimatedBorderWrapper
        isLoading={false}
        variant="pulse"
        borderColor={severity === 'critical' ? '#ef4444' : severity === 'warning' ? '#f97316' : '#3b82f6'}
        className={cn('rounded-lg', severityConfig.shadow)}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          {cardContent}
        </motion.div>
      </AnimatedBorderWrapper>
    );
  }

  return cardContent;
}

// Hook for managing error states
export function useErrorState() {
  const [error, setError] = React.useState<{
    title?: string;
    message?: string;
    severity?: 'critical' | 'warning' | 'info';
    details?: string;
    errorCode?: string;
  } | null>(null);

  const showError = React.useCallback((errorConfig: {
    title?: string;
    message?: string;
    severity?: 'critical' | 'warning' | 'info';
    details?: string;
    errorCode?: string;
  }) => {
    setError(errorConfig);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  const retryWithClear = React.useCallback((retryFn?: () => void) => {
    clearError();
    retryFn?.();
  }, [clearError]);

  return {
    error,
    showError,
    clearError,
    retryWithClear,
    hasError: !!error,
  };
}
