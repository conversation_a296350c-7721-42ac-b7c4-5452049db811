// Lighthouse-specific icon exports
// This file provides a clean interface for icons used in lighthouse components

// Import from the actual icon files in the icons directory
export { ActivityIcon } from '../../ui/icons/activity';
export { SparklesIcon } from '../../ui/icons/sparkles';
export { ClockIcon } from '../../ui/icons/clock';
export { UserIcon } from '../../ui/icons/user';
export { HomeIcon } from '../../ui/icons/home';
export { BookTextIcon } from '../../ui/icons/book-text';
export { TelescopeIcon } from '../../ui/icons/telescope';
export { CpuIcon } from '../../ui/icons/cpu';
export { MessageCircleIcon } from '../../ui/icons/message-circle';
export { FileTextIcon } from '../../ui/icons/file-text';
export { ChartLineIcon } from '../../ui/icons/chart-line';
export { ArrowRightIcon } from '../../ui/icons/arrow-right';
export { LoaderPinwheelIcon } from '../../ui/icons/loader-pinwheel';
export { HistoryIcon } from '../../ui/icons/history';
export { LinkIcon } from '../../ui/icons/link';
export { SettingsIcon } from '../../ui/icons/settings';

// Export the new icons we created
export { SendIcon } from '../../ui/icons/send';
export { MicIcon } from '../../ui/icons/mic';
export { PaperclipIcon } from '../../ui/icons/paperclip';
export { MoreHorizontalIcon } from '../../ui/icons/more-horizontal';
export { CheckCircleIcon } from '../../ui/icons/check-circle';
export { AlertTriangleIcon } from '../../ui/icons/alert-triangle';

// For any missing icons, we'll import from existing icon files or create simple ones
export { RefreshCWIcon as RefreshCw } from '../../ui/icons/refresh-cw';

// Create simple fallback components for missing icons
import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

export const Bug: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'm8 2 1.88 1.88' }),
  React.createElement('path', { d: 'M14.12 3.88 16 2' }),
  React.createElement('path', { d: 'M9 7.13v-1a3.003 3.003 0 1 1 6 0v1' }),
  React.createElement('path', { d: 'M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6' }),
  React.createElement('path', { d: 'M12 20v-9' }),
  React.createElement('path', { d: 'M6.53 9C4.6 8.8 3 7.1 3 5' }),
  React.createElement('path', { d: 'M6 13H2' }),
  React.createElement('path', { d: 'M3 21c0-2.1 1.7-3.9 3.8-4' }),
  React.createElement('path', { d: 'M20.97 5c0 2.1-1.6 3.8-3.5 4' }),
  React.createElement('path', { d: 'M22 13h-4' }),
  React.createElement('path', { d: 'M17.2 17c2.1.1 3.8 1.9 3.8 4' })
  )
);

export const XIcon: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M18 6 6 18' }),
  React.createElement('path', { d: 'm6 6 12 12' })
  )
);
