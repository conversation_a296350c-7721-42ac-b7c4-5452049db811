import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, typography, layout, animations } from '~/lib/ui-utils';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { SuccessFlipWrapper } from '~/components/ui/SuccessFlipWrapper';
import {
  CpuIcon,
  SparklesIcon,
  ActivityIcon,
  LoaderPinwheelIcon,
  ChartLineIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertTriangleIcon
} from './icons';

interface ThinkingIndicatorProps {
  isThinking: boolean;
  message?: string;
  variant?: 'dots' | 'pulse' | 'wave';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ThinkingIndicator({
  isThinking,
  message = 'AI is thinking...',
  variant = 'dots',
  size = 'md',
  className
}: ThinkingIndicatorProps) {
  const sizeClasses = {
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3'
  };

  const containerSizes = {
    sm: 'gap-1',
    md: 'gap-2',
    lg: 'gap-3'
  };

  const dotVariants = {
    initial: { y: 0, opacity: 0.3 },
    animate: { 
      y: [-4, 0, -4],
      opacity: [0.3, 1, 0.3],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const pulseVariants = {
    initial: { scale: 1, opacity: 0.5 },
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const waveVariants = {
    initial: { pathLength: 0, opacity: 0 },
    animate: {
      pathLength: [0, 1, 0],
      opacity: [0, 1, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <AnimatePresence>
      {isThinking && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className={cn(
            'flex items-center gap-3 p-3 rounded-lg bg-muted/50',
            className
          )}
        >
          <div className={cn('flex items-center', containerSizes[size])}>
            {variant === 'dots' && (
              <>
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className={cn(sizeClasses[size], 'bg-primary rounded-full')}
                    variants={dotVariants}
                    initial="initial"
                    animate="animate"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </>
            )}
            
            {variant === 'pulse' && (
              <motion.div
                className="relative"
                variants={pulseVariants}
                initial="initial"
                animate="animate"
              >
                <CpuIcon size={size === 'sm' ? 16 : size === 'md' ? 20 : 24} className="text-primary" />
              </motion.div>
            )}
            
            {variant === 'wave' && (
              <svg width="24" height="16" viewBox="0 0 24 16" className="text-primary">
                <motion.path
                  d="M2 8 Q6 2 10 8 T18 8 T22 8"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  variants={waveVariants}
                  initial="initial"
                  animate="animate"
                />
              </svg>
            )}
          </div>
          
          <span className={cn(typography.bodySmall, 'text-muted-foreground')}>
            {message}
          </span>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface IntelligenceProgressProps {
  value: number;
  label?: string;
  showPercentage?: boolean;
  animated?: boolean;
  variant?: 'linear' | 'circular' | 'brain';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function IntelligenceProgress({
  value,
  label = 'Learning Progress',
  showPercentage = true,
  animated = true,
  variant = 'linear',
  size = 'md',
  className
}: IntelligenceProgressProps) {
  const progressVariants = {
    initial: { width: 0 },
    animate: { 
      width: `${value}%`,
      transition: { duration: 1.5, ease: "easeOut" }
    }
  };

  const circularVariants = {
    initial: { pathLength: 0 },
    animate: { 
      pathLength: value / 100,
      transition: { duration: 1.5, ease: "easeOut" }
    }
  };

  if (variant === 'circular') {
    const radius = size === 'sm' ? 20 : size === 'md' ? 30 : 40;
    const circumference = 2 * Math.PI * radius;
    
    return (
      <div className={cn('flex flex-col items-center gap-2', className)}>
        <div className="relative">
          <svg width={radius * 2 + 10} height={radius * 2 + 10} className="transform -rotate-90">
            <circle
              cx={radius + 5}
              cy={radius + 5}
              r={radius}
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-muted"
            />
            <motion.circle
              cx={radius + 5}
              cy={radius + 5}
              r={radius}
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-primary"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={circumference * (1 - value / 100)}
              variants={animated ? circularVariants : undefined}
              initial={animated ? "initial" : undefined}
              animate={animated ? "animate" : undefined}
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <SparklesIcon size={size === 'sm' ? 12 : size === 'md' ? 16 : 20} className="text-primary" />
          </div>
        </div>
        {label && (
          <div className="text-center">
            <p className={cn(typography.bodySmall, 'text-muted-foreground')}>{label}</p>
            {showPercentage && (
              <p className={cn(typography.label, 'text-primary')}>{value}%</p>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className={cn(layout.flexBetween)}>
          <span className={cn(typography.bodySmall, 'text-muted-foreground flex items-center gap-1')}>
            <ActivityIcon size={12} className="text-primary" />
            {label}
          </span>
          {showPercentage && (
            <span className={cn(typography.label, 'text-primary')}>{value}%</span>
          )}
        </div>
      )}
      <div className="relative h-2 bg-muted rounded-full overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full"
          variants={animated ? progressVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
          style={!animated ? { width: `${value}%` } : undefined}
        />
        {animated && (
          <motion.div
            className="absolute top-0 left-0 h-full w-4 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            animate={{
              x: [-16, `calc(${value}% + 16px)`],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 1,
              ease: "easeInOut"
            }}
          />
        )}
      </div>
    </div>
  );
}

interface AIStatusBadgeProps {
  status: 'thinking' | 'processing' | 'learning' | 'idle' | 'error';
  label?: string;
  animated?: boolean;
  className?: string;
}

export function AIStatusBadge({
  status,
  label,
  animated = true,
  className
}: AIStatusBadgeProps) {
  const statusConfig = {
    thinking: {
      color: 'text-blue-500',
      bg: 'bg-blue-50 dark:bg-blue-950',
      border: 'border-blue-200 dark:border-blue-800',
      icon: CpuIcon,
      defaultLabel: 'Thinking'
    },
    processing: {
      color: 'text-yellow-500',
      bg: 'bg-yellow-50 dark:bg-yellow-950',
      border: 'border-yellow-200 dark:border-yellow-800',
      icon: LoaderPinwheelIcon,
      defaultLabel: 'Processing'
    },
    learning: {
      color: 'text-green-500',
      bg: 'bg-green-50 dark:bg-green-950',
      border: 'border-green-200 dark:border-green-800',
      icon: ChartLineIcon,
      defaultLabel: 'Learning'
    },
    idle: {
      color: 'text-gray-500',
      bg: 'bg-gray-50 dark:bg-gray-950',
      border: 'border-gray-200 dark:border-gray-800',
      icon: ClockIcon,
      defaultLabel: 'Idle'
    },
    error: {
      color: 'text-red-500',
      bg: 'bg-red-50 dark:bg-red-950',
      border: 'border-red-200 dark:border-red-800',
      icon: SparklesIcon,
      defaultLabel: 'Error'
    }
  };

  const config = statusConfig[status];
  const Icon = config.icon;
  const displayLabel = label || config.defaultLabel;

  return (
    <motion.div
      className={cn(
        'inline-flex items-center gap-2 px-3 py-1 rounded-full border',
        config.bg,
        config.border,
        className
      )}
      initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
      animate={animated ? { scale: 1, opacity: 1 } : undefined}
      transition={animated ? { duration: 0.3 } : undefined}
    >
      <motion.div
        animate={animated && status !== 'idle' ? {
          rotate: status === 'processing' ? 360 : 0,
          scale: status === 'thinking' ? [1, 1.1, 1] : 1
        } : undefined}
        transition={animated ? {
          rotate: { duration: 2, repeat: Infinity, ease: "linear" },
          scale: { duration: 1.5, repeat: Infinity, ease: "easeInOut" }
        } : undefined}
      >
        <Icon size={14} className={config.color} />
      </motion.div>
      <span className={cn(typography.bodySmall, config.color, 'font-medium')}>
        {displayLabel}
      </span>
    </motion.div>
  );
}
