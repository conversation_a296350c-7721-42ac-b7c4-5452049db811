import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { MetricCard, StatusCard } from '~/components/ui/enhanced-card';
import { LoadingOverlay, ProgressBar, ModuleSkeleton } from '~/components/ui/loading-states';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { SuccessFlipWrapper } from '~/components/ui/SuccessFlipWrapper';
import { ThinkingIndicator, IntelligenceProgress, AIStatusBadge } from '../../shared/AIComponents';
import { cn, typography, layout, animations, states } from '~/lib/ui-utils';
import {
  CpuIcon,
  ChartLineIcon,
  FileTextIcon,
  SparklesIcon,
  ClockIcon,
  ActivityIcon,
  HomeIcon,
  ArrowRightIcon,
} from '../../shared/icons';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { QuickActions } from './QuickActions';
import { ActivityFeed } from './ActivityFeed';
import { SmartRecommendations } from './SmartRecommendations';

export function Dashboard() {
  const {
    currentProject,
    projectContext,
    insights,
    activeAgents,
    knowledgeSources,
    learningEvents,
    navigateToModule,
  } = useLighthouseStore();

  if (!currentProject || !projectContext) {
    return (
      <motion.div
        className={cn(layout.flexColCenter, 'h-full p-6')}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <AnimatedBorderWrapper
          isLoading={false}
          variant="gradient"
          borderColor="#3b82f6"
          className="rounded-xl"
        >
          <StatusCard
            status="info"
            title="Welcome to Lighthouse"
            description="Create a project to start building contextual intelligence with AI-powered insights and autonomous agents."
            icon={<HomeIcon size={20} />}
            action={
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button className={cn(
                  'bg-gradient-to-r from-primary to-primary/80',
                  'hover:from-primary/90 hover:to-primary/70',
                  'shadow-lg hover:shadow-xl transition-all duration-300'
                )}>
                  <SparklesIcon size={16} className="mr-2" />
                  Create Your First Project
                </Button>
              </motion.div>
            }
            className="max-w-md"
          />
        </AnimatedBorderWrapper>

        {/* AI Status Indicator */}
        <motion.div
          className="mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <AIStatusBadge status="idle" label="Ready to assist" animated />
        </motion.div>
      </motion.div>
    );
  }

  // Calculate metrics
  const recentInsights = insights.filter(
    i => i.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;
  
  const learningVelocity = learningEvents.filter(
    e => e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const knowledgeDepth = currentProject.intelligence.domainExpertise.concepts.length;

  return (
    <LoadingOverlay loading={false} className="p-6">
      <div className={cn(layout.spaceY.lg)}>
        {/* Project Overview Metrics */}
        <motion.div
          className="grid gap-6 md:grid-cols-2 lg:grid-cols-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, staggerChildren: 0.1 }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <AnimatedBorderWrapper
              isLoading={false}
              variant="pulse"
              borderColor="#3b82f6"
              className="rounded-lg"
            >
              <MetricCard
                title="Learning Progress"
                value={`${currentProject.intelligence.learningLevel}%`}
                change={{
                  value: `+${learningVelocity} today`,
                  type: learningVelocity > 0 ? 'increase' : 'neutral'
                }}
                icon={<CpuIcon size={16} />}
                trend={
                  <IntelligenceProgress
                    value={currentProject.intelligence.learningLevel}
                    label=""
                    showPercentage={false}
                    size="sm"
                    animated
                  />
                }
                className={cn(animations.liftOnHover)}
              />
            </AnimatedBorderWrapper>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <SuccessFlipWrapper
              isSuccess={knowledgeDepth > 10}
              disabled={knowledgeDepth <= 10}
            >
              <MetricCard
                title="Knowledge Depth"
                value={knowledgeDepth}
                subtitle={`${knowledgeSources.length} sources`}
                icon={<FileTextIcon size={16} />}
                className={cn(animations.liftOnHover)}
              />
            </SuccessFlipWrapper>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <AnimatedBorderWrapper
              isLoading={activeAgents.length > 0}
              variant="rotate"
              borderColor="#10b981"
              className="rounded-lg"
            >
              <MetricCard
                title="Active Intelligence"
                value={activeAgents.length}
                subtitle={`${projectContext.runningAgents.length} in project`}
                icon={<SparklesIcon size={16} />}
                className={cn(animations.liftOnHover)}
              />
            </AnimatedBorderWrapper>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            <SuccessFlipWrapper
              isSuccess={recentInsights > 0}
              disabled={recentInsights === 0}
            >
              <MetricCard
                title="Recent Insights"
                value={recentInsights}
                subtitle={`${insights.length} total`}
                change={{
                  value: 'this week',
                  type: 'neutral'
                }}
                icon={<ChartLineIcon size={16} />}
                className={cn(animations.liftOnHover)}
              />
            </SuccessFlipWrapper>
          </motion.div>
        </motion.div>

        {/* Main Content Grid */}
        <div className={cn(layout.gridResponsive, 'gap-6')}>
          {/* Left Column - Project Status & Quick Actions */}
          <div className={cn(layout.spaceY.lg)}>
            {/* Project Goal Card */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <AnimatedBorderWrapper
                isLoading={false}
                variant="pulse"
                borderColor="#8b5cf6"
                className="rounded-lg"
              >
                <Card className={cn(animations.liftOnHover, 'transition-all duration-200')}>
                  <CardHeader>
                    <CardTitle className={cn(typography.h4, 'flex items-center gap-2')}>
                      <ActivityIcon size={20} className="text-primary" />
                      Project Goal
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className={cn(typography.body, 'mb-4')}>{currentProject.goal}</p>
                    <div className={cn(layout.spaceY.sm)}>
                      <div className={cn(layout.flexBetween, typography.bodySmall)}>
                        <span className="text-muted-foreground">Status</span>
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.3, delay: 0.5 }}
                        >
                          <Badge
                            variant="outline"
                            className={cn(
                              'capitalize transition-all duration-300',
                              currentProject.status === 'active' && 'border-green-200 text-green-700 bg-green-50 shadow-sm'
                            )}
                          >
                            {currentProject.status}
                          </Badge>
                        </motion.div>
                      </div>
                      <div className={cn(layout.flexBetween, typography.bodySmall)}>
                        <span className="text-muted-foreground">Domain</span>
                        <span className={cn(typography.label, 'capitalize')}>{currentProject.domain}</span>
                      </div>
                      <div className={cn(layout.flexBetween, typography.bodySmall)}>
                        <span className="text-muted-foreground">Created</span>
                        <span className={typography.label}>
                          {new Date(currentProject.created).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </AnimatedBorderWrapper>
            </motion.div>

            {/* Quick Actions */}
            <Suspense fallback={<div className="h-32 animate-pulse bg-muted rounded-lg" />}>
              <QuickActions />
            </Suspense>
          </div>

          {/* Center Column - Activity Feed */}
          <div>
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted rounded-lg" />}>
              <ActivityFeed />
            </Suspense>
          </div>

          {/* Right Column - AI Recommendations */}
          <div className={cn(layout.spaceY.lg)}>
            <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
              <SmartRecommendations />
            </Suspense>

            {/* Domain Expertise */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <AnimatedBorderWrapper
                isLoading={false}
                variant="gradient"
                borderColor="#f59e0b"
                className="rounded-lg"
              >
                <Card className={cn(animations.liftOnHover, 'transition-all duration-200')}>
                  <CardHeader>
                    <CardTitle className={cn(typography.h4, 'flex items-center gap-2')}>
                      <ChartLineIcon size={20} className="text-primary" />
                      Domain Expertise
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={cn(layout.spaceY.md)}>
                      <div>
                        <div className={cn(layout.flexBetween, 'mb-2')}>
                          <span className={cn(typography.label)}>
                            {currentProject.intelligence.domainExpertise.primaryDomain}
                          </span>
                          <motion.span
                            className={cn(typography.caption, 'text-primary font-semibold')}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                          >
                            Level {Math.floor(currentProject.intelligence.domainExpertise.expertiseLevel / 20)}
                          </motion.span>
                        </div>
                        <IntelligenceProgress
                          value={currentProject.intelligence.domainExpertise.expertiseLevel}
                          label=""
                          showPercentage={false}
                          size="sm"
                          animated
                        />
                      </div>

                      {currentProject.intelligence.domainExpertise.relatedDomains.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: 0.7 }}
                        >
                          <p className={cn(typography.caption, 'mb-2')}>Related Domains</p>
                          <div className="flex flex-wrap gap-2">
                            {currentProject.intelligence.domainExpertise.relatedDomains.map((domain, index) => (
                              <motion.div
                                key={domain}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <Badge
                                  variant="secondary"
                                  className={cn(
                                    'transition-all duration-300 hover:bg-secondary/80 cursor-pointer',
                                    'hover:shadow-md'
                                  )}
                                >
                                  {domain}
                                </Badge>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.9 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className={cn(
                            'w-full transition-all duration-300',
                            'bg-gradient-to-r from-primary/5 to-primary/10',
                            'border-primary/20 hover:border-primary/40',
                            'hover:from-primary/10 hover:to-primary/20',
                            'hover:shadow-lg hover:shadow-primary/20',
                            states.focusRing
                          )}
                          onClick={() => navigateToModule('knowledge')}
                        >
                          Explore Knowledge Graph
                          <ArrowRightIcon size={16} className="ml-2" />
                        </Button>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </AnimatedBorderWrapper>
            </motion.div>
          </div>
        </div>
      </div>
    </LoadingOverlay>
  );
}