import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Separator } from '~/components/ui/separator';
import {
  MessageCircleIcon,
  SendIcon,
  CpuIcon,
  UserIcon,
  ClockIcon,
  SparklesIcon,
  FileTextIcon,
  LinkIcon,
  ActivityIcon,
  SettingsIcon,
  MicIcon,
  PaperclipIcon,
  MoreHorizontalIcon,
  HistoryIcon,
} from '../../shared/icons';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { format } from 'date-fns';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  context?: {
    sources?: string[];
    insights?: string[];
    projectRelevance?: number;
  };
  isThinking?: boolean;
}

export function ContextualChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    currentProject,
    knowledgeCollections,
    projectIntelligence,
    addChatMessage,
    updateProjectIntelligence,
  } = useLighthouseStore();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize with contextual welcome message
  useEffect(() => {
    if (currentProject && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        content: `Hello! I'm your contextual AI assistant for the "${currentProject.name}" project. I have access to your project's knowledge base, current research, and can help you with tasks related to this project. What would you like to work on?`,
        sender: 'assistant',
        timestamp: new Date(),
        context: {
          projectRelevance: 100,
          insights: [
            'I understand your project context',
            'I can access your knowledge collections',
            'I can help with research and analysis'
          ]
        }
      };
      setMessages([welcomeMessage]);
    }
  }, [currentProject, messages.length]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !currentProject) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Add thinking message
    const thinkingMessage: ChatMessage = {
      id: `thinking-${Date.now()}`,
      content: 'Analyzing your question within project context...',
      sender: 'assistant',
      timestamp: new Date(),
      isThinking: true,
    };

    setMessages(prev => [...prev, thinkingMessage]);

    // Simulate AI processing with contextual intelligence
    setTimeout(() => {
      const contextualSources = knowledgeCollections
        .slice(0, 3)
        .map(collection => collection.name);

      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        content: generateContextualResponse(inputValue, currentProject.name),
        sender: 'assistant',
        timestamp: new Date(),
        context: {
          sources: contextualSources,
          insights: [
            'Drawing from project knowledge base',
            'Considering current project phase',
            'Leveraging domain expertise'
          ],
          projectRelevance: 85,
        },
      };

      // Remove thinking message and add real response
      setMessages(prev => [
        ...prev.slice(0, -1),
        assistantMessage
      ]);

      // Update project intelligence
      updateProjectIntelligence(currentProject.id, {
        chatInteractions: (projectIntelligence?.chatInteractions || 0) + 1,
        lastInteraction: new Date(),
      });

      setIsLoading(false);
    }, 2000);

    // Store message in project context
    addChatMessage(currentProject.id, {
      userMessage: inputValue,
      timestamp: new Date(),
      context: 'contextual-chat',
    });
  };

  const generateContextualResponse = (input: string, projectName: string) => {
    // This would be replaced with actual AI processing
    const responses = [
      `Based on your "${projectName}" project context, I can help you with that. Let me analyze the relevant information from your knowledge base and provide a comprehensive answer.`,
      `I've reviewed your project's knowledge collections and current research. Here's what I found that's relevant to your question about "${input.slice(0, 50)}...".`,
      `Drawing from your project's domain expertise and accumulated insights, I can provide you with a contextual response that considers your specific use case.`,
    ];
    
    return responses[Math.floor(Math.random() * responses.length)] + 
           "\n\nThis response is grounded in your project's knowledge and tailored to your specific context. Would you like me to dive deeper into any particular aspect?";
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!currentProject) {
    return (
      <motion.div
        className="flex items-center justify-center h-full p-6"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <AnimatedBorderWrapper
          isLoading={false}
          variant="gradient"
          borderColor="#6b7280"
          className="rounded-xl"
        >
          <Card className="max-w-md">
            <CardContent className="text-center py-12">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
                className="mb-6"
              >
                <MessageCircleIcon size={48} className="text-muted-foreground mx-auto" />
              </motion.div>

              <motion.h3
                className={cn(typography.h3, "mb-2")}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                No Project Selected
              </motion.h3>

              <motion.p
                className="text-muted-foreground"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
              >
                Select or create a project to start contextual conversations.
              </motion.p>

              <motion.div
                className="mt-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
              >
                <AIStatusBadge status="idle" label="Waiting for project" animated />
              </motion.div>
            </CardContent>
          </Card>
        </AnimatedBorderWrapper>
      </motion.div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Context Panel */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Context Intelligence
          </h3>
          
          {/* Project Context */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Active Project</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-medium">{currentProject.name}</p>
                <p className="text-sm text-muted-foreground">
                  {currentProject.description}
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  Last updated {format(new Date(currentProject.lastModified), 'MMM d, HH:mm')}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Knowledge Access */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Knowledge Access
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Collections</span>
                  <Badge variant="outline">{knowledgeCollections.length}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Documents</span>
                  <Badge variant="outline">
                    {knowledgeCollections.reduce((total, collection) => total + collection.documentCount, 0)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Learning Level</span>
                  <Badge variant="outline">
                    {projectIntelligence?.learningLevel || 0}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Active Insights */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Active Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {projectIntelligence?.keyInsights?.slice(0, 3).map((insight, index) => (
                  <div key={index} className="text-xs p-2 rounded bg-muted">
                    {insight.title}
                  </div>
                )) || (
                  <p className="text-xs text-muted-foreground">
                    No insights available yet
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Chat Settings */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Chat Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>Context Awareness</span>
                <Badge variant="default">High</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Source Grounding</span>
                <Badge variant="default">Enabled</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Learning Mode</span>
                <Badge variant="default">Active</Badge>
              </div>
              <Button size="sm" variant="outline" className="w-full">
                <Settings className="h-3 w-3 mr-1" />
                Configure
              </Button>
            </CardContent>
          </Card>
        </div>
      </aside>

      {/* Main Chat Interface */}
      <main className="flex-1 flex flex-col">
        {/* Chat Header */}
        <header className="border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <h2 className="font-semibold">Contextual AI Assistant</h2>
                  <p className="text-sm text-muted-foreground">
                    Project-aware conversations • {messages.length} messages
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="ghost">
                <History className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="ghost">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </header>

        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.sender === 'user' ? "justify-end" : "justify-start"
                )}
              >
                {message.sender === 'assistant' && (
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                    {message.isThinking ? (
                      <Brain className="h-4 w-4 text-primary animate-pulse" />
                    ) : (
                      <Bot className="h-4 w-4 text-primary" />
                    )}
                  </div>
                )}

                <div
                  className={cn(
                    "max-w-2xl rounded-lg p-4",
                    message.sender === 'user'
                      ? "bg-primary text-primary-foreground ml-12"
                      : message.isThinking
                      ? "bg-muted border-2 border-dashed"
                      : "bg-muted"
                  )}
                >
                  <div className="space-y-2">
                    <p className="text-sm">{message.content}</p>
                    
                    {message.context && !message.isThinking && (
                      <div className="space-y-2 pt-2 border-t border-border/50">
                        {message.context.sources && message.context.sources.length > 0 && (
                          <div>
                            <p className="text-xs text-muted-foreground mb-1">Sources:</p>
                            <div className="flex flex-wrap gap-1">
                              {message.context.sources.map((source, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  <Link className="h-2 w-2 mr-1" />
                                  {source}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {message.context.insights && message.context.insights.length > 0 && (
                          <div>
                            <p className="text-xs text-muted-foreground mb-1">Insights Applied:</p>
                            <div className="space-y-1">
                              {message.context.insights.map((insight, index) => (
                                <div key={index} className="text-xs flex items-center gap-1">
                                  <Lightbulb className="h-2 w-2" />
                                  {insight}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {message.context.projectRelevance && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>Project Relevance:</span>
                            <div className="flex items-center gap-1">
                              <div className="w-12 h-1 bg-muted rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-primary rounded-full" 
                                  style={{ width: `${message.context.projectRelevance}%` }} 
                                />
                              </div>
                              <span>{message.context.projectRelevance}%</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {format(message.timestamp, 'HH:mm')}
                    </div>
                  </div>
                </div>

                {message.sender === 'user' && (
                  <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4" />
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <footer className="border-t p-4">
          <div className="flex items-end gap-2">
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Ask anything about your project..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                >
                  {isLoading ? (
                    <Brain className="h-4 w-4 animate-pulse" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              <div className="flex items-center gap-2">
                <Button size="sm" variant="ghost" className="text-xs">
                  <Paperclip className="h-3 w-3 mr-1" />
                  Attach
                </Button>
                <Button size="sm" variant="ghost" className="text-xs">
                  <Mic className="h-3 w-3 mr-1" />
                  Voice
                </Button>
                <div className="flex-1" />
                <p className="text-xs text-muted-foreground">
                  Context-aware • Learning enabled
                </p>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  );
}