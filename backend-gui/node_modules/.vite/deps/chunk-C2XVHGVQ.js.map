{"version": 3, "sources": ["../../@trpc/server/dist/observable/observable.mjs", "../../@trpc/server/dist/observable/operators.mjs", "../../@trpc/server/dist/observable/behaviorSubject.mjs", "../../@trpc/client/dist/links/internals/createChain.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/createProxy.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/error/formatter.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/rpc/codes.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/utils.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/error/TRPCError.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/transformer.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/router.mjs", "../../@trpc/server/dist/vendor/unpromise/unpromise.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/utils/disposable.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/utils/timerResource.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/utils/createDeferred.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/utils/withPing.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/jsonl.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/tracked.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/stream/sse.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/middleware.mjs", "../../@trpc/server/dist/vendor/standard-schema-v1/error.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/parser.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/procedureBuilder.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/rootConfig.mjs", "../../@trpc/server/dist/unstable-core-do-not-import/initTRPC.mjs", "../../@trpc/client/dist/TRPCClientError.mjs", "../../@trpc/client/dist/internals/TRPCUntypedClient.mjs", "../../@trpc/client/dist/createTRPCUntypedClient.mjs", "../../@trpc/client/dist/createTRPCClient.mjs", "../../@trpc/client/dist/getFetch.mjs", "../../@trpc/client/dist/links/internals/contentTypes.mjs", "../../@trpc/client/dist/internals/dataLoader.mjs", "../../@trpc/client/dist/internals/signals.mjs", "../../@trpc/client/dist/internals/transformer.mjs", "../../@trpc/client/dist/links/internals/httpUtils.mjs", "../../@trpc/client/dist/links/httpBatchLink.mjs", "../../@trpc/client/dist/links/httpBatchStreamLink.mjs", "../../@trpc/client/dist/links/httpLink.mjs", "../../@trpc/client/dist/links/loggerLink.mjs", "../../@trpc/client/dist/links/splitLink.mjs", "../../@trpc/client/dist/links/wsLink/wsClient/options.mjs", "../../@trpc/client/dist/links/internals/urlWithConnectionParams.mjs", "../../@trpc/client/dist/links/wsLink/wsClient/utils.mjs", "../../@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs", "../../@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs", "../../@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs", "../../@trpc/client/dist/links/wsLink/createWsClient.mjs", "../../@trpc/client/dist/links/wsLink/wsLink.mjs", "../../@trpc/client/dist/internals/inputWithTrackedEventId.mjs", "../../@trpc/client/dist/links/httpSubscriptionLink.mjs", "../../@trpc/client/dist/links/retryLink.mjs"], "sourcesContent": ["/** @public */ function isObservable(x) {\n    return typeof x === 'object' && x !== null && 'subscribe' in x;\n}\n/** @public */ function observable(subscribe) {\n    const self = {\n        subscribe (observer) {\n            let teardownRef = null;\n            let isDone = false;\n            let unsubscribed = false;\n            let teardownImmediately = false;\n            function unsubscribe() {\n                if (teardownRef === null) {\n                    teardownImmediately = true;\n                    return;\n                }\n                if (unsubscribed) {\n                    return;\n                }\n                unsubscribed = true;\n                if (typeof teardownRef === 'function') {\n                    teardownRef();\n                } else if (teardownRef) {\n                    teardownRef.unsubscribe();\n                }\n            }\n            teardownRef = subscribe({\n                next (value) {\n                    if (isDone) {\n                        return;\n                    }\n                    observer.next?.(value);\n                },\n                error (err) {\n                    if (isDone) {\n                        return;\n                    }\n                    isDone = true;\n                    observer.error?.(err);\n                    unsubscribe();\n                },\n                complete () {\n                    if (isDone) {\n                        return;\n                    }\n                    isDone = true;\n                    observer.complete?.();\n                    unsubscribe();\n                }\n            });\n            if (teardownImmediately) {\n                unsubscribe();\n            }\n            return {\n                unsubscribe\n            };\n        },\n        pipe (...operations) {\n            return operations.reduce(pipeReducer, self);\n        }\n    };\n    return self;\n}\nfunction pipeReducer(prev, fn) {\n    return fn(prev);\n}\n/** @internal */ function observableToPromise(observable) {\n    const ac = new AbortController();\n    const promise = new Promise((resolve, reject)=>{\n        let isDone = false;\n        function onDone() {\n            if (isDone) {\n                return;\n            }\n            isDone = true;\n            obs$.unsubscribe();\n        }\n        ac.signal.addEventListener('abort', ()=>{\n            reject(ac.signal.reason);\n        });\n        const obs$ = observable.subscribe({\n            next (data) {\n                isDone = true;\n                resolve(data);\n                onDone();\n            },\n            error (data) {\n                reject(data);\n            },\n            complete () {\n                ac.abort();\n                onDone();\n            }\n        });\n    });\n    return promise;\n}\n/**\n * @internal\n */ function observableToReadableStream(observable, signal) {\n    let unsub = null;\n    const onAbort = ()=>{\n        unsub?.unsubscribe();\n        unsub = null;\n        signal.removeEventListener('abort', onAbort);\n    };\n    return new ReadableStream({\n        start (controller) {\n            unsub = observable.subscribe({\n                next (data) {\n                    controller.enqueue({\n                        ok: true,\n                        value: data\n                    });\n                },\n                error (error) {\n                    controller.enqueue({\n                        ok: false,\n                        error\n                    });\n                    controller.close();\n                },\n                complete () {\n                    controller.close();\n                }\n            });\n            if (signal.aborted) {\n                onAbort();\n            } else {\n                signal.addEventListener('abort', onAbort, {\n                    once: true\n                });\n            }\n        },\n        cancel () {\n            onAbort();\n        }\n    });\n}\n/** @internal */ function observableToAsyncIterable(observable, signal) {\n    const stream = observableToReadableStream(observable, signal);\n    const reader = stream.getReader();\n    const iterator = {\n        async next () {\n            const value = await reader.read();\n            if (value.done) {\n                return {\n                    value: undefined,\n                    done: true\n                };\n            }\n            const { value: result } = value;\n            if (!result.ok) {\n                throw result.error;\n            }\n            return {\n                value: result.value,\n                done: false\n            };\n        },\n        async return () {\n            await reader.cancel();\n            return {\n                value: undefined,\n                done: true\n            };\n        }\n    };\n    return {\n        [Symbol.asyncIterator] () {\n            return iterator;\n        }\n    };\n}\n\nexport { isObservable, observable, observableToAsyncIterable, observableToPromise };\n", "import { observable } from './observable.mjs';\n\nfunction map(project) {\n    return (source)=>{\n        return observable((destination)=>{\n            let index = 0;\n            const subscription = source.subscribe({\n                next (value) {\n                    destination.next(project(value, index++));\n                },\n                error (error) {\n                    destination.error(error);\n                },\n                complete () {\n                    destination.complete();\n                }\n            });\n            return subscription;\n        });\n    };\n}\nfunction share(_opts) {\n    return (source)=>{\n        let refCount = 0;\n        let subscription = null;\n        const observers = [];\n        function startIfNeeded() {\n            if (subscription) {\n                return;\n            }\n            subscription = source.subscribe({\n                next (value) {\n                    for (const observer of observers){\n                        observer.next?.(value);\n                    }\n                },\n                error (error) {\n                    for (const observer of observers){\n                        observer.error?.(error);\n                    }\n                },\n                complete () {\n                    for (const observer of observers){\n                        observer.complete?.();\n                    }\n                }\n            });\n        }\n        function resetIfNeeded() {\n            // \"resetOnRefCountZero\"\n            if (refCount === 0 && subscription) {\n                const _sub = subscription;\n                subscription = null;\n                _sub.unsubscribe();\n            }\n        }\n        return observable((subscriber)=>{\n            refCount++;\n            observers.push(subscriber);\n            startIfNeeded();\n            return {\n                unsubscribe () {\n                    refCount--;\n                    resetIfNeeded();\n                    const index = observers.findIndex((v)=>v === subscriber);\n                    if (index > -1) {\n                        observers.splice(index, 1);\n                    }\n                }\n            };\n        });\n    };\n}\nfunction tap(observer) {\n    return (source)=>{\n        return observable((destination)=>{\n            return source.subscribe({\n                next (value) {\n                    observer.next?.(value);\n                    destination.next(value);\n                },\n                error (error) {\n                    observer.error?.(error);\n                    destination.error(error);\n                },\n                complete () {\n                    observer.complete?.();\n                    destination.complete();\n                }\n            });\n        });\n    };\n}\nconst distinctUnsetMarker = Symbol();\nfunction distinctUntilChanged(compare = (a, b)=>a === b) {\n    return (source)=>{\n        return observable((destination)=>{\n            let lastValue = distinctUnsetMarker;\n            return source.subscribe({\n                next (value) {\n                    if (lastValue !== distinctUnsetMarker && compare(lastValue, value)) {\n                        return;\n                    }\n                    lastValue = value;\n                    destination.next(value);\n                },\n                error (error) {\n                    destination.error(error);\n                },\n                complete () {\n                    destination.complete();\n                }\n            });\n        });\n    };\n}\nconst isDeepEqual = (a, b)=>{\n    if (a === b) {\n        return true;\n    }\n    const bothAreObjects = a && b && typeof a === 'object' && typeof b === 'object';\n    return !!bothAreObjects && Object.keys(a).length === Object.keys(b).length && Object.entries(a).every(([k, v])=>isDeepEqual(v, b[k]));\n};\nfunction distinctUntilDeepChanged() {\n    return distinctUntilChanged(isDeepEqual);\n}\n\nexport { distinctUntilChanged, distinctUntilDeepChanged, map, share, tap };\n", "import { observable } from './observable.mjs';\n\n/**\n * @internal\n * An observable that maintains and provides a \"current value\" to subscribers\n * @see https://www.learnrxjs.io/learn-rxjs/subjects/behaviorsubject\n */ function behaviorSubject(initialValue) {\n    let value = initialValue;\n    const observerList = [];\n    const addObserver = (observer)=>{\n        if (value !== undefined) {\n            observer.next(value);\n        }\n        observerList.push(observer);\n    };\n    const removeObserver = (observer)=>{\n        observerList.splice(observerList.indexOf(observer), 1);\n    };\n    const obs = observable((observer)=>{\n        addObserver(observer);\n        return ()=>{\n            removeObserver(observer);\n        };\n    });\n    obs.next = (nextValue)=>{\n        if (value === nextValue) {\n            return;\n        }\n        value = nextValue;\n        for (const observer of observerList){\n            observer.next(nextValue);\n        }\n    };\n    obs.get = ()=>value;\n    return obs;\n}\n\nexport { behaviorSubject };\n", "import { observable } from '@trpc/server/observable';\n\n/** @internal */ function createChain(opts) {\n    return observable((observer)=>{\n        function execute(index = 0, op = opts.op) {\n            const next = opts.links[index];\n            if (!next) {\n                throw new Error('No more links to execute - did you forget to add an ending link?');\n            }\n            const subscription = next({\n                op,\n                next (nextOp) {\n                    const nextObserver = execute(index + 1, nextOp);\n                    return nextObserver;\n                }\n            });\n            return subscription;\n        }\n        const obs$ = execute();\n        return obs$.subscribe(observer);\n    });\n}\n\nexport { createChain };\n", "const noop = ()=>{\n// noop\n};\nconst freezeIfAvailable = (obj)=>{\n    if (Object.freeze) {\n        Object.freeze(obj);\n    }\n};\nfunction createInnerProxy(callback, path, memo) {\n    var _memo, _cacheKey;\n    const cacheKey = path.join('.');\n    (_memo = memo)[_cacheKey = cacheKey] ?? (_memo[_cacheKey] = new Proxy(noop, {\n        get (_obj, key) {\n            if (typeof key !== 'string' || key === 'then') {\n                // special case for if the proxy is accidentally treated\n                // like a PromiseLike (like in `Promise.resolve(proxy)`)\n                return undefined;\n            }\n            return createInnerProxy(callback, [\n                ...path,\n                key\n            ], memo);\n        },\n        apply (_1, _2, args) {\n            const lastOfPath = path[path.length - 1];\n            let opts = {\n                args,\n                path\n            };\n            // special handling for e.g. `trpc.hello.call(this, 'there')` and `trpc.hello.apply(this, ['there'])\n            if (lastOfPath === 'call') {\n                opts = {\n                    args: args.length >= 2 ? [\n                        args[1]\n                    ] : [],\n                    path: path.slice(0, -1)\n                };\n            } else if (lastOfPath === 'apply') {\n                opts = {\n                    args: args.length >= 2 ? args[1] : [],\n                    path: path.slice(0, -1)\n                };\n            }\n            freezeIfAvailable(opts.args);\n            freezeIfAvailable(opts.path);\n            return callback(opts);\n        }\n    }));\n    return memo[cacheKey];\n}\n/**\n * Creates a proxy that calls the callback with the path and arguments\n *\n * @internal\n */ const createRecursiveProxy = (callback)=>createInnerProxy(callback, [], Object.create(null));\n/**\n * Used in place of `new Proxy` where each handler will map 1 level deep to another value.\n *\n * @internal\n */ const createFlatProxy = (callback)=>{\n    return new Proxy(noop, {\n        get (_obj, name) {\n            if (name === 'then') {\n                // special case for if the proxy is accidentally treated\n                // like a PromiseLike (like in `Promise.resolve(proxy)`)\n                return undefined;\n            }\n            return callback(name);\n        }\n    });\n};\n\nexport { createFlatProxy, createRecursiveProxy };\n", "const defaultFormatter = ({ shape })=>{\n    return shape;\n};\n\nexport { defaultFormatter };\n", "// reference: https://www.jsonrpc.org/specification\n/**\n * JSON-RPC 2.0 Error codes\n *\n * `-32000` to `-32099` are reserved for implementation-defined server-errors.\n * For tRPC we're copying the last digits of HTTP 4XX errors.\n */ const TRPC_ERROR_CODES_BY_KEY = {\n    /**\n   * Invalid JSON was received by the server.\n   * An error occurred on the server while parsing the JSON text.\n   */ PARSE_ERROR: -32700,\n    /**\n   * The JSON sent is not a valid Request object.\n   */ BAD_REQUEST: -32600,\n    // Internal JSON-RPC error\n    INTERNAL_SERVER_ERROR: -32603,\n    NOT_IMPLEMENTED: -32603,\n    BAD_GATEWAY: -32603,\n    SERVICE_UNAVAILABLE: -32603,\n    GATEWAY_TIMEOUT: -32603,\n    // Implementation specific errors\n    UNAUTHORIZED: -32001,\n    PAYMENT_REQUIRED: -32002,\n    FORBIDDEN: -32003,\n    NOT_FOUND: -32004,\n    METHOD_NOT_SUPPORTED: -32005,\n    TIMEOUT: -32008,\n    CONFLICT: -32009,\n    PRECONDITION_FAILED: -32012,\n    PAYLOAD_TOO_LARGE: -32013,\n    UNSUPPORTED_MEDIA_TYPE: -32015,\n    UNPROCESSABLE_CONTENT: -32022,\n    TOO_MANY_REQUESTS: -32029,\n    CLIENT_CLOSED_REQUEST: -32099\n};\n// pure\nconst TRPC_ERROR_CODES_BY_NUMBER = {\n    [-32700]: 'PARSE_ERROR',\n    [-32600]: 'BAD_REQUEST',\n    [-32603]: 'INTERNAL_SERVER_ERROR',\n    [-32001]: 'UNAUTHORIZED',\n    [-32002]: 'PAYMENT_REQUIRED',\n    [-32003]: 'FORBIDDEN',\n    [-32004]: 'NOT_FOUND',\n    [-32005]: 'METHOD_NOT_SUPPORTED',\n    [-32008]: 'TIMEOUT',\n    [-32009]: 'CONFLICT',\n    [-32012]: 'PRECONDITION_FAILED',\n    [-32013]: 'PAYLOAD_TOO_LARGE',\n    [-32015]: 'UNSUPPORTED_MEDIA_TYPE',\n    [-32022]: 'UNPROCESSABLE_CONTENT',\n    [-32029]: 'TOO_MANY_REQUESTS',\n    [-32099]: 'CLIENT_CLOSED_REQUEST'\n};\n\nexport { TRPC_ERROR_CODES_BY_KEY, TRPC_ERROR_CODES_BY_NUMBER };\n", "/** @internal */ const unsetMarker = Symbol();\n/**\n * Ensures there are no duplicate keys when building a procedure.\n * @internal\n */ function mergeWithoutOverrides(obj1, ...objs) {\n    const newObj = Object.assign(Object.create(null), obj1);\n    for (const overrides of objs){\n        for(const key in overrides){\n            if (key in newObj && newObj[key] !== overrides[key]) {\n                throw new Error(`Duplicate key ${key}`);\n            }\n            newObj[key] = overrides[key];\n        }\n    }\n    return newObj;\n}\n/**\n * Check that value is object\n * @internal\n */ function isObject(value) {\n    return !!value && !Array.isArray(value) && typeof value === 'object';\n}\nfunction isFunction(fn) {\n    return typeof fn === 'function';\n}\n/**\n * Create an object without inheriting anything from `Object.prototype`\n * @internal\n */ function omitPrototype(obj) {\n    return Object.assign(Object.create(null), obj);\n}\nconst asyncIteratorsSupported = typeof Symbol === 'function' && !!Symbol.asyncIterator;\nfunction isAsyncIterable(value) {\n    return asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value;\n}\n/**\n * Run an IIFE\n */ const run = (fn)=>fn();\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\nfunction identity(it) {\n    return it;\n}\n/**\n * Generic runtime assertion function. Throws, if the condition is not `true`.\n *\n * Can be used as a slightly less dangerous variant of type assertions. Code\n * mistakes would be revealed at runtime then (hopefully during testing).\n */ function assert(condition, msg = 'no additional info') {\n    if (!condition) {\n        throw new Error(`AssertionError: ${msg}`);\n    }\n}\nfunction sleep(ms = 0) {\n    return new Promise((res)=>setTimeout(res, ms));\n}\n/**\n * Ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */ function abortSignalsAnyPonyfill(signals) {\n    if (typeof AbortSignal.any === 'function') {\n        return AbortSignal.any(signals);\n    }\n    const ac = new AbortController();\n    for (const signal of signals){\n        if (signal.aborted) {\n            trigger();\n            break;\n        }\n        signal.addEventListener('abort', trigger, {\n            once: true\n        });\n    }\n    return ac.signal;\n    function trigger() {\n        ac.abort();\n        for (const signal of signals){\n            signal.removeEventListener('abort', trigger);\n        }\n    }\n}\n\nexport { abortSignalsAnyPonyfill, assert, identity, isAsyncIterable, isFunction, isObject, mergeWithoutOverrides, noop, omitPrototype, run, sleep, unsetMarker };\n", "import { isObject } from '../utils.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nclass UnknownCauseError extends Error {\n}\nfunction getCauseFromUnknown(cause) {\n    if (cause instanceof Error) {\n        return cause;\n    }\n    const type = typeof cause;\n    if (type === 'undefined' || type === 'function' || cause === null) {\n        return undefined;\n    }\n    // Primitive types just get wrapped in an error\n    if (type !== 'object') {\n        // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        return new Error(String(cause));\n    }\n    // If it's an object, we'll create a synthetic error\n    if (isObject(cause)) {\n        return Object.assign(new UnknownCauseError(), cause);\n    }\n    return undefined;\n}\nfunction getTRPCErrorFromUnknown(cause) {\n    if (cause instanceof TRPCError) {\n        return cause;\n    }\n    if (cause instanceof Error && cause.name === 'TRPCError') {\n        // https://github.com/trpc/trpc/pull/4848\n        return cause;\n    }\n    const trpcError = new TRPCError({\n        code: 'INTERNAL_SERVER_ERROR',\n        cause\n    });\n    // Inherit stack from error\n    if (cause instanceof Error && cause.stack) {\n        trpcError.stack = cause.stack;\n    }\n    return trpcError;\n}\nclass TRPCError extends Error {\n    constructor(opts){\n        const cause = getCauseFromUnknown(opts.cause);\n        const message = opts.message ?? cause?.message ?? opts.code;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore https://github.com/tc39/proposal-error-cause\n        super(message, {\n            cause\n        }), // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n        _define_property(this, \"cause\", void 0), _define_property(this, \"code\", void 0);\n        this.code = opts.code;\n        this.name = 'TRPCError';\n        this.cause ?? (this.cause = cause);\n    }\n}\n\nexport { TRPCError, getCauseFromUnknown, getTRPCErrorFromUnknown };\n", "import { isObject } from './utils.mjs';\n\n/**\n * @internal\n */ function getDataTransformer(transformer) {\n    if ('input' in transformer) {\n        return transformer;\n    }\n    return {\n        input: transformer,\n        output: transformer\n    };\n}\n/**\n * @internal\n */ const defaultTransformer = {\n    input: {\n        serialize: (obj)=>obj,\n        deserialize: (obj)=>obj\n    },\n    output: {\n        serialize: (obj)=>obj,\n        deserialize: (obj)=>obj\n    }\n};\nfunction transformTRPCResponseItem(config, item) {\n    if ('error' in item) {\n        return {\n            ...item,\n            error: config.transformer.output.serialize(item.error)\n        };\n    }\n    if ('data' in item.result) {\n        return {\n            ...item,\n            result: {\n                ...item.result,\n                data: config.transformer.output.serialize(item.result.data)\n            }\n        };\n    }\n    return item;\n}\n/**\n * Takes a unserialized `TRPCResponse` and serializes it with the router's transformers\n **/ function transformTRPCResponse(config, itemOrItems) {\n    return Array.isArray(itemOrItems) ? itemOrItems.map((item)=>transformTRPCResponseItem(config, item)) : transformTRPCResponseItem(config, itemOrItems);\n}\n// FIXME:\n// - the generics here are probably unnecessary\n// - the RPC-spec could probably be simplified to combine HTTP + WS\n/** @internal */ function transformResultInner(response, transformer) {\n    if ('error' in response) {\n        const error = transformer.deserialize(response.error);\n        return {\n            ok: false,\n            error: {\n                ...response,\n                error\n            }\n        };\n    }\n    const result = {\n        ...response.result,\n        ...(!response.result.type || response.result.type === 'data') && {\n            type: 'data',\n            data: transformer.deserialize(response.result.data)\n        }\n    };\n    return {\n        ok: true,\n        result\n    };\n}\nclass TransformResultError extends Error {\n    constructor(){\n        super('Unable to transform response from server');\n    }\n}\n/**\n * Transforms and validates that the result is a valid TRPCResponse\n * @internal\n */ function transformResult(response, transformer) {\n    let result;\n    try {\n        // Use the data transformers on the JSON-response\n        result = transformResultInner(response, transformer);\n    } catch  {\n        throw new TransformResultError();\n    }\n    // check that output of the transformers is a valid TRPCResponse\n    if (!result.ok && (!isObject(result.error.error) || typeof result.error.error['code'] !== 'number')) {\n        throw new TransformResultError();\n    }\n    if (result.ok && !isObject(result.result)) {\n        throw new TransformResultError();\n    }\n    return result;\n}\n\nexport { defaultTransformer, getDataTransformer, transformResult, transformTRPCResponse };\n", "import { createRecursiveProxy } from './createProxy.mjs';\nimport { defaultFormatter } from './error/formatter.mjs';\nimport { TRPCError, getTRPCErrorFromUnknown } from './error/TRPCError.mjs';\nimport { defaultTransformer } from './transformer.mjs';\nimport { mergeWithoutOverrides, omitPrototype, isObject, isFunction } from './utils.mjs';\n\nconst lazySymbol = Symbol('lazy');\nfunction once(fn) {\n    const uncalled = Symbol();\n    let result = uncalled;\n    return ()=>{\n        if (result === uncalled) {\n            result = fn();\n        }\n        return result;\n    };\n}\n/**\n * Lazy load a router\n * @see https://trpc.io/docs/server/merging-routers#lazy-load\n */ function lazy(importRouter) {\n    async function resolve() {\n        const mod = await importRouter();\n        // if the module is a router, return it\n        if (isRouter(mod)) {\n            return mod;\n        }\n        const routers = Object.values(mod);\n        if (routers.length !== 1 || !isRouter(routers[0])) {\n            throw new Error(\"Invalid router module - either define exactly 1 export or return the router directly.\\nExample: `lazy(() => import('./slow.js').then((m) => m.slowRouter))`\");\n        }\n        return routers[0];\n    }\n    resolve[lazySymbol] = true;\n    return resolve;\n}\nfunction isLazy(input) {\n    return typeof input === 'function' && lazySymbol in input;\n}\nfunction isRouter(value) {\n    return isObject(value) && isObject(value['_def']) && 'router' in value['_def'];\n}\nconst emptyRouter = {\n    _ctx: null,\n    _errorShape: null,\n    _meta: null,\n    queries: {},\n    mutations: {},\n    subscriptions: {},\n    errorFormatter: defaultFormatter,\n    transformer: defaultTransformer\n};\n/**\n * Reserved words that can't be used as router or procedure names\n */ const reservedWords = [\n    /**\n   * Then is a reserved word because otherwise we can't return a promise that returns a Proxy\n   * since JS will think that `.then` is something that exists\n   */ 'then',\n    /**\n   * `fn.call()` and `fn.apply()` are reserved words because otherwise we can't call a function using `.call` or `.apply`\n   */ 'call',\n    'apply'\n];\n/**\n * @internal\n */ function createRouterFactory(config) {\n    function createRouterInner(input) {\n        const reservedWordsUsed = new Set(Object.keys(input).filter((v)=>reservedWords.includes(v)));\n        if (reservedWordsUsed.size > 0) {\n            throw new Error('Reserved words used in `router({})` call: ' + Array.from(reservedWordsUsed).join(', '));\n        }\n        const procedures = omitPrototype({});\n        const lazy = omitPrototype({});\n        function createLazyLoader(opts) {\n            return {\n                ref: opts.ref,\n                load: once(async ()=>{\n                    const router = await opts.ref();\n                    const lazyPath = [\n                        ...opts.path,\n                        opts.key\n                    ];\n                    const lazyKey = lazyPath.join('.');\n                    opts.aggregate[opts.key] = step(router._def.record, lazyPath);\n                    delete lazy[lazyKey];\n                    // add lazy loaders for nested routers\n                    for (const [nestedKey, nestedItem] of Object.entries(router._def.lazy)){\n                        const nestedRouterKey = [\n                            ...lazyPath,\n                            nestedKey\n                        ].join('.');\n                        // console.log('adding lazy', nestedRouterKey);\n                        lazy[nestedRouterKey] = createLazyLoader({\n                            ref: nestedItem.ref,\n                            path: lazyPath,\n                            key: nestedKey,\n                            aggregate: opts.aggregate[opts.key]\n                        });\n                    }\n                })\n            };\n        }\n        function step(from, path = []) {\n            const aggregate = omitPrototype({});\n            for (const [key, item] of Object.entries(from ?? {})){\n                if (isLazy(item)) {\n                    lazy[[\n                        ...path,\n                        key\n                    ].join('.')] = createLazyLoader({\n                        path,\n                        ref: item,\n                        key,\n                        aggregate\n                    });\n                    continue;\n                }\n                if (isRouter(item)) {\n                    aggregate[key] = step(item._def.record, [\n                        ...path,\n                        key\n                    ]);\n                    continue;\n                }\n                if (!isProcedure(item)) {\n                    // RouterRecord\n                    aggregate[key] = step(item, [\n                        ...path,\n                        key\n                    ]);\n                    continue;\n                }\n                const newPath = [\n                    ...path,\n                    key\n                ].join('.');\n                if (procedures[newPath]) {\n                    throw new Error(`Duplicate key: ${newPath}`);\n                }\n                procedures[newPath] = item;\n                aggregate[key] = item;\n            }\n            return aggregate;\n        }\n        const record = step(input);\n        const _def = {\n            _config: config,\n            router: true,\n            procedures,\n            lazy,\n            ...emptyRouter,\n            record\n        };\n        const router = {\n            ...record,\n            _def,\n            createCaller: createCallerFactory()({\n                _def\n            })\n        };\n        return router;\n    }\n    return createRouterInner;\n}\nfunction isProcedure(procedureOrRouter) {\n    return typeof procedureOrRouter === 'function';\n}\n/**\n * @internal\n */ async function getProcedureAtPath(router, path) {\n    const { _def } = router;\n    let procedure = _def.procedures[path];\n    while(!procedure){\n        const key = Object.keys(_def.lazy).find((key)=>path.startsWith(key));\n        // console.log(`found lazy: ${key ?? 'NOPE'} (fullPath: ${fullPath})`);\n        if (!key) {\n            return null;\n        }\n        // console.log('loading', key, '.......');\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const lazyRouter = _def.lazy[key];\n        await lazyRouter.load();\n        procedure = _def.procedures[path];\n    }\n    return procedure;\n}\n/**\n * @internal\n */ async function callProcedure(opts) {\n    const { type, path } = opts;\n    const proc = await getProcedureAtPath(opts.router, path);\n    if (!proc || !isProcedure(proc) || proc._def.type !== type && !opts.allowMethodOverride) {\n        throw new TRPCError({\n            code: 'NOT_FOUND',\n            message: `No \"${type}\"-procedure on path \"${path}\"`\n        });\n    }\n    /* istanbul ignore if -- @preserve */ if (proc._def.type !== type && opts.allowMethodOverride && proc._def.type === 'subscription') {\n        throw new TRPCError({\n            code: 'METHOD_NOT_SUPPORTED',\n            message: `Method override is not supported for subscriptions`\n        });\n    }\n    return proc(opts);\n}\nfunction createCallerFactory() {\n    return function createCallerInner(router) {\n        const { _def } = router;\n        return function createCaller(ctxOrCallback, opts) {\n            return createRecursiveProxy(async ({ path, args })=>{\n                const fullPath = path.join('.');\n                if (path.length === 1 && path[0] === '_def') {\n                    return _def;\n                }\n                const procedure = await getProcedureAtPath(router, fullPath);\n                let ctx = undefined;\n                try {\n                    if (!procedure) {\n                        throw new TRPCError({\n                            code: 'NOT_FOUND',\n                            message: `No procedure found on path \"${path}\"`\n                        });\n                    }\n                    ctx = isFunction(ctxOrCallback) ? await Promise.resolve(ctxOrCallback()) : ctxOrCallback;\n                    return await procedure({\n                        path: fullPath,\n                        getRawInput: async ()=>args[0],\n                        ctx,\n                        type: procedure._def.type,\n                        signal: opts?.signal\n                    });\n                } catch (cause) {\n                    opts?.onError?.({\n                        ctx,\n                        error: getTRPCErrorFromUnknown(cause),\n                        input: args[0],\n                        path: fullPath,\n                        type: procedure?._def.type ?? 'unknown'\n                    });\n                    throw cause;\n                }\n            });\n        };\n    };\n}\nfunction mergeRouters(...routerList) {\n    const record = mergeWithoutOverrides({}, ...routerList.map((r)=>r._def.record));\n    const errorFormatter = routerList.reduce((currentErrorFormatter, nextRouter)=>{\n        if (nextRouter._def._config.errorFormatter && nextRouter._def._config.errorFormatter !== defaultFormatter) {\n            if (currentErrorFormatter !== defaultFormatter && currentErrorFormatter !== nextRouter._def._config.errorFormatter) {\n                throw new Error('You seem to have several error formatters');\n            }\n            return nextRouter._def._config.errorFormatter;\n        }\n        return currentErrorFormatter;\n    }, defaultFormatter);\n    const transformer = routerList.reduce((prev, current)=>{\n        if (current._def._config.transformer && current._def._config.transformer !== defaultTransformer) {\n            if (prev !== defaultTransformer && prev !== current._def._config.transformer) {\n                throw new Error('You seem to have several transformers');\n            }\n            return current._def._config.transformer;\n        }\n        return prev;\n    }, defaultTransformer);\n    const router = createRouterFactory({\n        errorFormatter,\n        transformer,\n        isDev: routerList.every((r)=>r._def._config.isDev),\n        allowOutsideOfServer: routerList.every((r)=>r._def._config.allowOutsideOfServer),\n        isServer: routerList.every((r)=>r._def._config.isServer),\n        $types: routerList[0]?._def._config.$types\n    })(record);\n    return router;\n}\n\nexport { callProcedure, createCallerFactory, createRouterFactory, getProcedureAtPath, lazy, mergeRouters };\n", "/* eslint-disable @typescript-eslint/unbound-method */ function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar _computedKey;\n/** Memory safe (weakmapped) cache of the ProxyPromise for each Promise,\n * which is retained for the lifetime of the original Promise.\n */ const subscribableCache = new WeakMap();\n/** A NOOP function allowing a consistent interface for settled\n * SubscribedPromises (settled promises are not subscribed - they resolve\n * immediately). */ const NOOP = ()=>{\n// noop\n};\n_computedKey = Symbol.toStringTag;\nlet _computedKey1 = _computedKey;\n/**\n * Every `Promise<T>` can be shadowed by a single `ProxyPromise<T>`. It is\n * created once, cached and reused throughout the lifetime of the Promise. Get a\n * Promise's ProxyPromise using `Unpromise.proxy(promise)`.\n *\n * The `ProxyPromise<T>` attaches handlers to the original `Promise<T>`\n * `.then()` and `.catch()` just once. Promises derived from it use a\n * subscription- (and unsubscription-) based mechanism that monitors these\n * handlers.\n *\n * Every time you call `.subscribe()`, `.then()` `.catch()` or `.finally()` on a\n * `ProxyPromise<T>` it returns a `SubscribedPromise<T>` having an additional\n * `unsubscribe()` method. Calling `unsubscribe()` detaches reference chains\n * from the original, potentially long-lived Promise, eliminating memory leaks.\n *\n * This approach can eliminate the memory leaks that otherwise come about from\n * repeated `race()` or `any()` calls invoking `.then()` and `.catch()` multiple\n * times on the same long-lived native Promise (subscriptions which can never be\n * cleaned up).\n *\n * `Unpromise.race(promises)` is a reference implementation of `Promise.race`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.any(promises)` is a reference implementation of `Promise.any`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.resolve(promise)` returns an ephemeral `SubscribedPromise<T>` for\n * any given `Promise<T>` facilitating arbitrary async/await patterns. Behind\n * the scenes, `resolve` is implemented simply as\n * `Unpromise.proxy(promise).subscribe()`. Don't forget to call `.unsubscribe()`\n * to tidy up!\n *\n */ class Unpromise {\n    /** Create a promise that mitigates uncontrolled subscription to a long-lived\n   * Promise via .then() and .catch() - otherwise a source of memory leaks.\n   *\n   * The returned promise has an `unsubscribe()` method which can be called when\n   * the Promise is no longer being tracked by application logic, and which\n   * ensures that there is no reference chain from the original promise to the\n   * new one, and therefore no memory leak.\n   *\n   * If original promise has not yet settled, this adds a new unique promise\n   * that listens to then/catch events, along with an `unsubscribe()` method to\n   * detach it.\n   *\n   * If original promise has settled, then creates a new Promise.resolve() or\n   * Promise.reject() and provided unsubscribe is a noop.\n   *\n   * If you call `unsubscribe()` before the returned Promise has settled, it\n   * will never settle.\n   */ subscribe() {\n        // in all cases we will combine some promise with its unsubscribe function\n        let promise;\n        let unsubscribe;\n        const { settlement } = this;\n        if (settlement === null) {\n            // not yet settled - subscribe new promise. Expect eventual settlement\n            if (this.subscribers === null) {\n                // invariant - it is not settled, so it must have subscribers\n                throw new Error(\"Unpromise settled but still has subscribers\");\n            }\n            const subscriber = withResolvers();\n            this.subscribers = listWithMember(this.subscribers, subscriber);\n            promise = subscriber.promise;\n            unsubscribe = ()=>{\n                if (this.subscribers !== null) {\n                    this.subscribers = listWithoutMember(this.subscribers, subscriber);\n                }\n            };\n        } else {\n            // settled - don't create subscribed promise. Just resolve or reject\n            const { status } = settlement;\n            if (status === \"fulfilled\") {\n                promise = Promise.resolve(settlement.value);\n            } else {\n                promise = Promise.reject(settlement.reason);\n            }\n            unsubscribe = NOOP;\n        }\n        // extend promise signature with the extra method\n        return Object.assign(promise, {\n            unsubscribe\n        });\n    }\n    /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */ then(onfulfilled, onrejected) {\n        const subscribed = this.subscribe();\n        const { unsubscribe } = subscribed;\n        return Object.assign(subscribed.then(onfulfilled, onrejected), {\n            unsubscribe\n        });\n    }\n    catch(onrejected) {\n        const subscribed = this.subscribe();\n        const { unsubscribe } = subscribed;\n        return Object.assign(subscribed.catch(onrejected), {\n            unsubscribe\n        });\n    }\n    finally(onfinally) {\n        const subscribed = this.subscribe();\n        const { unsubscribe } = subscribed;\n        return Object.assign(subscribed.finally(onfinally), {\n            unsubscribe\n        });\n    }\n    /** Unpromise STATIC METHODS */ /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime\n   * of the provided Promise reference) */ static proxy(promise) {\n        const cached = Unpromise.getSubscribablePromise(promise);\n        return typeof cached !== \"undefined\" ? cached : Unpromise.createSubscribablePromise(promise);\n    }\n    /** Create and store an Unpromise keyed by an original Promise. */ static createSubscribablePromise(promise) {\n        const created = new Unpromise(promise);\n        subscribableCache.set(promise, created); // resolve promise to unpromise\n        subscribableCache.set(created, created); // resolve the unpromise to itself\n        return created;\n    }\n    /** Retrieve a previously-created Unpromise keyed by an original Promise. */ static getSubscribablePromise(promise) {\n        return subscribableCache.get(promise);\n    }\n    /** Promise STATIC METHODS */ /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from\n   * it (that can be later unsubscribed to eliminate Memory leaks) */ static resolve(value) {\n        const promise = typeof value === \"object\" && value !== null && \"then\" in value && typeof value.then === \"function\" ? value : Promise.resolve(value);\n        return Unpromise.proxy(promise).subscribe();\n    }\n    static async any(values) {\n        const valuesArray = Array.isArray(values) ? values : [\n            ...values\n        ];\n        const subscribedPromises = valuesArray.map(Unpromise.resolve);\n        try {\n            return await Promise.any(subscribedPromises);\n        } finally{\n            subscribedPromises.forEach(({ unsubscribe })=>{\n                unsubscribe();\n            });\n        }\n    }\n    static async race(values) {\n        const valuesArray = Array.isArray(values) ? values : [\n            ...values\n        ];\n        const subscribedPromises = valuesArray.map(Unpromise.resolve);\n        try {\n            return await Promise.race(subscribedPromises);\n        } finally{\n            subscribedPromises.forEach(({ unsubscribe })=>{\n                unsubscribe();\n            });\n        }\n    }\n    /** Create a race of SubscribedPromises that will fulfil to a single winning\n   * Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises\n   * accumulating .then() and .catch() subscribers. Allows simple logic to\n   * consume the result, like...\n   * ```ts\n   * const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);\n   * if(winner === promiseB){\n   *   const result = await promiseB;\n   *   // do the thing\n   * }\n   * ```\n   * */ static async raceReferences(promises) {\n        // map each promise to an eventual 1-tuple containing itself\n        const selfPromises = promises.map(resolveSelfTuple);\n        // now race them. They will fulfil to a readonly [P] or reject.\n        try {\n            return await Promise.race(selfPromises);\n        } finally{\n            for (const promise of selfPromises){\n                // unsubscribe proxy promises when the race is over to mitigate memory leaks\n                promise.unsubscribe();\n            }\n        }\n    }\n    constructor(arg){\n        /** INSTANCE IMPLEMENTATION */ /** The promise shadowed by this Unpromise<T>  */ _define_property(this, \"promise\", void 0);\n        /** Promises expecting eventual settlement (unless unsubscribed first). This list is deleted\n   * after the original promise settles - no further notifications will be issued. */ _define_property(this, \"subscribers\", []);\n        /** The Promise's settlement (recorded when it fulfils or rejects). This is consulted when\n   * calling .subscribe() .then() .catch() .finally() to see if an immediately-resolving Promise\n   * can be returned, and therefore subscription can be bypassed. */ _define_property(this, \"settlement\", null);\n        /** TOSTRING SUPPORT */ _define_property(this, _computedKey1, \"Unpromise\");\n        // handle either a Promise or a Promise executor function\n        if (typeof arg === \"function\") {\n            this.promise = new Promise(arg);\n        } else {\n            this.promise = arg;\n        }\n        // subscribe for eventual fulfilment and rejection\n        // handle PromiseLike objects (that at least have .then)\n        const thenReturn = this.promise.then((value)=>{\n            // atomically record fulfilment and detach subscriber list\n            const { subscribers } = this;\n            this.subscribers = null;\n            this.settlement = {\n                status: \"fulfilled\",\n                value\n            };\n            // notify fulfilment to subscriber list\n            subscribers?.forEach(({ resolve })=>{\n                resolve(value);\n            });\n        });\n        // handle Promise (that also have a .catch behaviour)\n        if (\"catch\" in thenReturn) {\n            thenReturn.catch((reason)=>{\n                // atomically record rejection and detach subscriber list\n                const { subscribers } = this;\n                this.subscribers = null;\n                this.settlement = {\n                    status: \"rejected\",\n                    reason\n                };\n                // notify rejection to subscriber list\n                subscribers?.forEach(({ reject })=>{\n                    reject(reason);\n                });\n            });\n        }\n    }\n}\n/** Promises a 1-tuple containing the original promise when it resolves. Allows\n * awaiting the eventual Promise ***reference*** (easy to destructure and\n * exactly compare with ===). Avoids resolving to the Promise ***value*** (which\n * may be ambiguous and therefore hard to identify as the winner of a race).\n * You can call unsubscribe on the Promise to mitigate memory leaks.\n * */ function resolveSelfTuple(promise) {\n    return Unpromise.proxy(promise).then(()=>[\n            promise\n        ]);\n}\n/** VENDORED (Future) PROMISE UTILITIES */ /** Reference implementation of https://github.com/tc39/proposal-promise-with-resolvers */ function withResolvers() {\n    let resolve;\n    let reject;\n    const promise = new Promise((_resolve, _reject)=>{\n        resolve = _resolve;\n        reject = _reject;\n    });\n    return {\n        promise,\n        resolve,\n        reject\n    };\n}\n/** IMMUTABLE LIST OPERATIONS */ function listWithMember(arr, member) {\n    return [\n        ...arr,\n        member\n    ];\n}\nfunction listWithoutIndex(arr, index) {\n    return [\n        ...arr.slice(0, index),\n        ...arr.slice(index + 1)\n    ];\n}\nfunction listWithoutMember(arr, member) {\n    const index = arr.indexOf(member);\n    if (index !== -1) {\n        return listWithoutIndex(arr, index);\n    }\n    return arr;\n}\n\nexport { Unpromise, resolveSelfTuple };\n", "// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nvar _Symbol, // @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\n_Symbol1;\n(_Symbol = Symbol).dispose ?? (_Symbol.dispose = Symbol());\n(_Symbol1 = Symbol).asyncDispose ?? (_Symbol1.asyncDispose = Symbol());\n/**\n * Takes a value and a dispose function and returns a new object that implements the Disposable interface.\n * The returned object is the original value augmented with a Symbol.dispose method.\n * @param thing The value to make disposable\n * @param dispose Function to call when disposing the resource\n * @returns The original value with Symbol.dispose method added\n */ function makeResource(thing, dispose) {\n    const it = thing;\n    // eslint-disable-next-line no-restricted-syntax\n    const existing = it[Symbol.dispose];\n    // eslint-disable-next-line no-restricted-syntax\n    it[Symbol.dispose] = ()=>{\n        dispose();\n        existing?.();\n    };\n    return it;\n}\n/**\n * Takes a value and an async dispose function and returns a new object that implements the AsyncDisposable interface.\n * The returned object is the original value augmented with a Symbol.asyncDispose method.\n * @param thing The value to make async disposable\n * @param dispose Async function to call when disposing the resource\n * @returns The original value with Symbol.asyncDispose method added\n */ function makeAsyncResource(thing, dispose) {\n    const it = thing;\n    // eslint-disable-next-line no-restricted-syntax\n    const existing = it[Symbol.asyncDispose];\n    // eslint-disable-next-line no-restricted-syntax\n    it[Symbol.asyncDispose] = async ()=>{\n        await dispose();\n        await existing?.();\n    };\n    return it;\n}\n\nexport { makeAsyncResource, makeResource };\n", "import { makeResource } from './disposable.mjs';\n\nconst disposablePromiseTimerResult = Symbol();\nfunction timerResource(ms) {\n    let timer = null;\n    return makeResource({\n        start () {\n            if (timer) {\n                throw new Error('Timer already started');\n            }\n            const promise = new Promise((resolve)=>{\n                timer = setTimeout(()=>resolve(disposablePromiseTimerResult), ms);\n            });\n            return promise;\n        }\n    }, ()=>{\n        if (timer) {\n            clearTimeout(timer);\n        }\n    });\n}\n\nexport { disposablePromiseTimerResult, timerResource };\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */ function createDeferred() {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    return {\n        promise,\n        resolve: resolve,\n        reject: reject\n    };\n}\n\nexport { createDeferred };\n", "import { Unpromise } from '../../../vendor/unpromise/unpromise.mjs';\nimport { iteratorResource } from './asyncIterable.mjs';\nimport { timerResource, disposablePromiseTimerResult } from './timerResource.mjs';\n\nfunction _ts_add_disposable_resource(env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose, inner;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n            if (async) inner = dispose;\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        if (inner) dispose = function() {\n            try {\n                inner.call(this);\n            } catch (e) {\n                return Promise.reject(e);\n            }\n        };\n        env.stack.push({\n            value: value,\n            dispose: dispose,\n            async: async\n        });\n    } else if (async) {\n        env.stack.push({\n            async: true\n        });\n    }\n    return value;\n}\nfunction _ts_dispose_resources(env) {\n    var _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function(error, suppressed, message) {\n        var e = new Error(message);\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n    };\n    return (_ts_dispose_resources = function _ts_dispose_resources(env) {\n        function fail(e) {\n            env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        var r, s = 0;\n        function next() {\n            while(r = env.stack.pop()){\n                try {\n                    if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n                    if (r.dispose) {\n                        var result = r.dispose.call(r.value);\n                        if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {\n                            fail(e);\n                            return next();\n                        });\n                    } else s |= 1;\n                } catch (e) {\n                    fail(e);\n                }\n            }\n            if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    })(env);\n}\nconst PING_SYM = Symbol('ping');\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields {@link PING_SYM}\n * whenever no value has been yielded for {@link pingIntervalMs}.\n */ async function* withPing(iterable, pingIntervalMs) {\n    const env = {\n        stack: [],\n        error: void 0,\n        hasError: false\n    };\n    try {\n        const iterator = _ts_add_disposable_resource(env, iteratorResource(iterable), true);\n        ;\n        // declaration outside the loop for garbage collection reasons\n        let result;\n        let nextPromise = iterator.next();\n        while(true){\n            const env = {\n                stack: [],\n                error: void 0,\n                hasError: false\n            };\n            try {\n                const pingPromise = _ts_add_disposable_resource(env, timerResource(pingIntervalMs), false);\n                ;\n                result = await Unpromise.race([\n                    nextPromise,\n                    pingPromise.start()\n                ]);\n                if (result === disposablePromiseTimerResult) {\n                    // cancelled\n                    yield PING_SYM;\n                    continue;\n                }\n                if (result.done) {\n                    return result.value;\n                }\n                nextPromise = iterator.next();\n                yield result.value;\n                // free up reference for garbage collection\n                result = null;\n            } catch (e) {\n                env.error = e;\n                env.hasError = true;\n            } finally{\n                _ts_dispose_resources(env);\n            }\n        }\n    } catch (e) {\n        env.error = e;\n        env.hasError = true;\n    } finally{\n        const result = _ts_dispose_resources(env);\n        if (result) await result;\n    }\n}\n\nexport { PING_SYM, withPing };\n", "import { isObject, isFunction, run, isAsyncIterable } from '../utils.mjs';\nimport { iteratorResource } from './utils/asyncIterable.mjs';\nimport { createDeferred } from './utils/createDeferred.mjs';\nimport { makeResource } from './utils/disposable.mjs';\nimport { mergeAsyncIterables } from './utils/mergeAsyncIterables.mjs';\nimport { readableStreamFrom } from './utils/readableStreamFrom.mjs';\nimport { PING_SYM, withPing } from './utils/withPing.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _ts_add_disposable_resource(env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose, inner;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n            if (async) inner = dispose;\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        if (inner) dispose = function() {\n            try {\n                inner.call(this);\n            } catch (e) {\n                return Promise.reject(e);\n            }\n        };\n        env.stack.push({\n            value: value,\n            dispose: dispose,\n            async: async\n        });\n    } else if (async) {\n        env.stack.push({\n            async: true\n        });\n    }\n    return value;\n}\nfunction _ts_dispose_resources(env) {\n    var _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function(error, suppressed, message) {\n        var e = new Error(message);\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n    };\n    return (_ts_dispose_resources = function _ts_dispose_resources(env) {\n        function fail(e) {\n            env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        var r, s = 0;\n        function next() {\n            while(r = env.stack.pop()){\n                try {\n                    if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n                    if (r.dispose) {\n                        var result = r.dispose.call(r.value);\n                        if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {\n                            fail(e);\n                            return next();\n                        });\n                    } else s |= 1;\n                } catch (e) {\n                    fail(e);\n                }\n            }\n            if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    })(env);\n}\nfunction isPlainObject(value) {\n    return Object.prototype.toString.call(value) === '[object Object]';\n}\n// ---------- types\nconst CHUNK_VALUE_TYPE_PROMISE = 0;\nconst CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;\nconst PROMISE_STATUS_FULFILLED = 0;\nconst PROMISE_STATUS_REJECTED = 1;\nconst ASYNC_ITERABLE_STATUS_RETURN = 0;\nconst ASYNC_ITERABLE_STATUS_YIELD = 1;\nconst ASYNC_ITERABLE_STATUS_ERROR = 2;\nfunction isPromise(value) {\n    return (isObject(value) || isFunction(value)) && typeof value?.['then'] === 'function' && typeof value?.['catch'] === 'function';\n}\nclass MaxDepthError extends Error {\n    constructor(path){\n        super('Max depth reached at path: ' + path.join('.')), _define_property(this, \"path\", void 0), this.path = path;\n    }\n}\nasync function* createBatchStreamProducer(opts) {\n    const { data } = opts;\n    let counter = 0;\n    const placeholder = 0;\n    const mergedIterables = mergeAsyncIterables();\n    function registerAsync(callback) {\n        const idx = counter++;\n        const iterable = callback(idx);\n        mergedIterables.add(iterable);\n        return idx;\n    }\n    function encodePromise(promise, path) {\n        return registerAsync(async function*(idx) {\n            const error = checkMaxDepth(path);\n            if (error) {\n                // Catch any errors from the original promise to ensure they're reported\n                promise.catch((cause)=>{\n                    opts.onError?.({\n                        error: cause,\n                        path\n                    });\n                });\n                // Replace the promise with a rejected one containing the max depth error\n                promise = Promise.reject(error);\n            }\n            try {\n                const next = await promise;\n                yield [\n                    idx,\n                    PROMISE_STATUS_FULFILLED,\n                    encode(next, path)\n                ];\n            } catch (cause) {\n                opts.onError?.({\n                    error: cause,\n                    path\n                });\n                yield [\n                    idx,\n                    PROMISE_STATUS_REJECTED,\n                    opts.formatError?.({\n                        error: cause,\n                        path\n                    })\n                ];\n            }\n        });\n    }\n    function encodeAsyncIterable(iterable, path) {\n        return registerAsync(async function*(idx) {\n            const env = {\n                stack: [],\n                error: void 0,\n                hasError: false\n            };\n            try {\n                const error = checkMaxDepth(path);\n                if (error) {\n                    throw error;\n                }\n                const iterator = _ts_add_disposable_resource(env, iteratorResource(iterable), true);\n                ;\n                try {\n                    while(true){\n                        const next = await iterator.next();\n                        if (next.done) {\n                            yield [\n                                idx,\n                                ASYNC_ITERABLE_STATUS_RETURN,\n                                encode(next.value, path)\n                            ];\n                            break;\n                        }\n                        yield [\n                            idx,\n                            ASYNC_ITERABLE_STATUS_YIELD,\n                            encode(next.value, path)\n                        ];\n                    }\n                } catch (cause) {\n                    opts.onError?.({\n                        error: cause,\n                        path\n                    });\n                    yield [\n                        idx,\n                        ASYNC_ITERABLE_STATUS_ERROR,\n                        opts.formatError?.({\n                            error: cause,\n                            path\n                        })\n                    ];\n                }\n            } catch (e) {\n                env.error = e;\n                env.hasError = true;\n            } finally{\n                const result = _ts_dispose_resources(env);\n                if (result) await result;\n            }\n        });\n    }\n    function checkMaxDepth(path) {\n        if (opts.maxDepth && path.length > opts.maxDepth) {\n            return new MaxDepthError(path);\n        }\n        return null;\n    }\n    function encodeAsync(value, path) {\n        if (isPromise(value)) {\n            return [\n                CHUNK_VALUE_TYPE_PROMISE,\n                encodePromise(value, path)\n            ];\n        }\n        if (isAsyncIterable(value)) {\n            if (opts.maxDepth && path.length >= opts.maxDepth) {\n                throw new Error('Max depth reached');\n            }\n            return [\n                CHUNK_VALUE_TYPE_ASYNC_ITERABLE,\n                encodeAsyncIterable(value, path)\n            ];\n        }\n        return null;\n    }\n    function encode(value, path) {\n        if (value === undefined) {\n            return [\n                []\n            ];\n        }\n        const reg = encodeAsync(value, path);\n        if (reg) {\n            return [\n                [\n                    placeholder\n                ],\n                [\n                    null,\n                    ...reg\n                ]\n            ];\n        }\n        if (!isPlainObject(value)) {\n            return [\n                [\n                    value\n                ]\n            ];\n        }\n        const newObj = {};\n        const asyncValues = [];\n        for (const [key, item] of Object.entries(value)){\n            const transformed = encodeAsync(item, [\n                ...path,\n                key\n            ]);\n            if (!transformed) {\n                newObj[key] = item;\n                continue;\n            }\n            newObj[key] = placeholder;\n            asyncValues.push([\n                key,\n                ...transformed\n            ]);\n        }\n        return [\n            [\n                newObj\n            ],\n            ...asyncValues\n        ];\n    }\n    const newHead = {};\n    for (const [key, item] of Object.entries(data)){\n        newHead[key] = encode(item, [\n            key\n        ]);\n    }\n    yield newHead;\n    let iterable = mergedIterables;\n    if (opts.pingMs) {\n        iterable = withPing(mergedIterables, opts.pingMs);\n    }\n    for await (const value of iterable){\n        yield value;\n    }\n}\n/**\n * JSON Lines stream producer\n * @see https://jsonlines.org/\n */ function jsonlStreamProducer(opts) {\n    let stream = readableStreamFrom(createBatchStreamProducer(opts));\n    const { serialize } = opts;\n    if (serialize) {\n        stream = stream.pipeThrough(new TransformStream({\n            transform (chunk, controller) {\n                if (chunk === PING_SYM) {\n                    controller.enqueue(PING_SYM);\n                } else {\n                    controller.enqueue(serialize(chunk));\n                }\n            }\n        }));\n    }\n    return stream.pipeThrough(new TransformStream({\n        transform (chunk, controller) {\n            if (chunk === PING_SYM) {\n                controller.enqueue(' ');\n            } else {\n                controller.enqueue(JSON.stringify(chunk) + '\\n');\n            }\n        }\n    })).pipeThrough(new TextEncoderStream());\n}\nclass AsyncError extends Error {\n    constructor(data){\n        super('Received error from server'), _define_property(this, \"data\", void 0), this.data = data;\n    }\n}\nconst nodeJsStreamToReaderEsque = (source)=>{\n    return {\n        getReader () {\n            const stream = new ReadableStream({\n                start (controller) {\n                    source.on('data', (chunk)=>{\n                        controller.enqueue(chunk);\n                    });\n                    source.on('end', ()=>{\n                        controller.close();\n                    });\n                    source.on('error', (error)=>{\n                        controller.error(error);\n                    });\n                }\n            });\n            return stream.getReader();\n        }\n    };\n};\nfunction createLineAccumulator(from) {\n    const reader = 'getReader' in from ? from.getReader() : nodeJsStreamToReaderEsque(from).getReader();\n    let lineAggregate = '';\n    return new ReadableStream({\n        async pull (controller) {\n            const { done, value } = await reader.read();\n            if (done) {\n                controller.close();\n            } else {\n                controller.enqueue(value);\n            }\n        },\n        cancel () {\n            return reader.cancel();\n        }\n    }).pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({\n        transform (chunk, controller) {\n            lineAggregate += chunk;\n            const parts = lineAggregate.split('\\n');\n            lineAggregate = parts.pop() ?? '';\n            for (const part of parts){\n                controller.enqueue(part);\n            }\n        }\n    }));\n}\nfunction createConsumerStream(from) {\n    const stream = createLineAccumulator(from);\n    let sentHead = false;\n    return stream.pipeThrough(new TransformStream({\n        transform (line, controller) {\n            if (!sentHead) {\n                const head = JSON.parse(line);\n                controller.enqueue(head);\n                sentHead = true;\n            } else {\n                const chunk = JSON.parse(line);\n                controller.enqueue(chunk);\n            }\n        }\n    }));\n}\n/**\n * Creates a handler for managing stream controllers and their lifecycle\n */ function createStreamsManager(abortController) {\n    const controllerMap = new Map();\n    /**\n   * Checks if there are no pending controllers or deferred promises\n   */ function isEmpty() {\n        return Array.from(controllerMap.values()).every((c)=>c.closed);\n    }\n    /**\n   * Creates a stream controller\n   */ function createStreamController() {\n        let originalController;\n        const stream = new ReadableStream({\n            start (controller) {\n                originalController = controller;\n            }\n        });\n        const streamController = {\n            enqueue: (v)=>originalController.enqueue(v),\n            close: ()=>{\n                originalController.close();\n                clear();\n                if (isEmpty()) {\n                    abortController.abort();\n                }\n            },\n            closed: false,\n            getReaderResource: ()=>{\n                const reader = stream.getReader();\n                return makeResource(reader, ()=>{\n                    reader.releaseLock();\n                    streamController.close();\n                });\n            },\n            error: (reason)=>{\n                originalController.error(reason);\n                clear();\n            }\n        };\n        function clear() {\n            Object.assign(streamController, {\n                closed: true,\n                close: ()=>{\n                // noop\n                },\n                enqueue: ()=>{\n                // noop\n                },\n                getReaderResource: null,\n                error: ()=>{\n                // noop\n                }\n            });\n        }\n        return streamController;\n    }\n    /**\n   * Gets or creates a stream controller\n   */ function getOrCreate(chunkId) {\n        let c = controllerMap.get(chunkId);\n        if (!c) {\n            c = createStreamController();\n            controllerMap.set(chunkId, c);\n        }\n        return c;\n    }\n    /**\n   * Cancels all pending controllers and rejects deferred promises\n   */ function cancelAll(reason) {\n        for (const controller of controllerMap.values()){\n            controller.error(reason);\n        }\n    }\n    return {\n        getOrCreate,\n        isEmpty,\n        cancelAll\n    };\n}\n/**\n * JSON Lines stream consumer\n * @see https://jsonlines.org/\n */ async function jsonlStreamConsumer(opts) {\n    const { deserialize = (v)=>v } = opts;\n    let source = createConsumerStream(opts.from);\n    if (deserialize) {\n        source = source.pipeThrough(new TransformStream({\n            transform (chunk, controller) {\n                controller.enqueue(deserialize(chunk));\n            }\n        }));\n    }\n    let headDeferred = createDeferred();\n    const streamManager = createStreamsManager(opts.abortController);\n    function decodeChunkDefinition(value) {\n        const [_path, type, chunkId] = value;\n        const controller = streamManager.getOrCreate(chunkId);\n        switch(type){\n            case CHUNK_VALUE_TYPE_PROMISE:\n                {\n                    return run(async ()=>{\n                        const env = {\n                            stack: [],\n                            error: void 0,\n                            hasError: false\n                        };\n                        try {\n                            const reader = _ts_add_disposable_resource(env, controller.getReaderResource(), false);\n                            ;\n                            const { value } = await reader.read();\n                            const [_chunkId, status, data] = value;\n                            switch(status){\n                                case PROMISE_STATUS_FULFILLED:\n                                    return decode(data);\n                                case PROMISE_STATUS_REJECTED:\n                                    throw opts.formatError?.({\n                                        error: data\n                                    }) ?? new AsyncError(data);\n                            }\n                        } catch (e) {\n                            env.error = e;\n                            env.hasError = true;\n                        } finally{\n                            _ts_dispose_resources(env);\n                        }\n                    });\n                }\n            case CHUNK_VALUE_TYPE_ASYNC_ITERABLE:\n                {\n                    return run(async function*() {\n                        const env = {\n                            stack: [],\n                            error: void 0,\n                            hasError: false\n                        };\n                        try {\n                            const reader = _ts_add_disposable_resource(env, controller.getReaderResource(), false);\n                            ;\n                            while(true){\n                                const { value } = await reader.read();\n                                const [_chunkId, status, data] = value;\n                                switch(status){\n                                    case ASYNC_ITERABLE_STATUS_YIELD:\n                                        yield decode(data);\n                                        break;\n                                    case ASYNC_ITERABLE_STATUS_RETURN:\n                                        return decode(data);\n                                    case ASYNC_ITERABLE_STATUS_ERROR:\n                                        throw opts.formatError?.({\n                                            error: data\n                                        }) ?? new AsyncError(data);\n                                }\n                            }\n                        } catch (e) {\n                            env.error = e;\n                            env.hasError = true;\n                        } finally{\n                            _ts_dispose_resources(env);\n                        }\n                    });\n                }\n        }\n    }\n    function decode(value) {\n        const [[data], ...asyncProps] = value;\n        for (const value of asyncProps){\n            const [key] = value;\n            const decoded = decodeChunkDefinition(value);\n            if (key === null) {\n                return decoded;\n            }\n            data[key] = decoded;\n        }\n        return data;\n    }\n    const closeOrAbort = (reason)=>{\n        headDeferred?.reject(reason);\n        streamManager.cancelAll(reason);\n    };\n    source.pipeTo(new WritableStream({\n        write (chunkOrHead) {\n            if (headDeferred) {\n                const head = chunkOrHead;\n                for (const [key, value] of Object.entries(chunkOrHead)){\n                    const parsed = decode(value);\n                    head[key] = parsed;\n                }\n                headDeferred.resolve(head);\n                headDeferred = null;\n                return;\n            }\n            const chunk = chunkOrHead;\n            const [idx] = chunk;\n            const controller = streamManager.getOrCreate(idx);\n            controller.enqueue(chunk);\n        },\n        close: ()=>closeOrAbort(new Error('Stream closed')),\n        abort: closeOrAbort\n    }), {\n        signal: opts.abortController.signal\n    }).catch((error)=>{\n        opts.onError?.({\n            error\n        });\n        closeOrAbort(error);\n    });\n    return [\n        await headDeferred.promise,\n        streamManager\n    ];\n}\n\nexport { isPromise, jsonlStreamConsumer, jsonlStreamProducer };\n", "const trackedSymbol = Symbol();\n/**\n * Produce a typed server-sent event message\n * @deprecated use `tracked(id, data)` instead\n */ function sse(event) {\n    return tracked(event.id, event.data);\n}\nfunction isTrackedEnvelope(value) {\n    return Array.isArray(value) && value[2] === trackedSymbol;\n}\n/**\n * Automatically track an event so that it can be resumed from a given id if the connection is lost\n */ function tracked(id, data) {\n    if (id === '') {\n        // This limitation could be removed by using different SSE event names / channels for tracked event and non-tracked event\n        throw new Error('`id` must not be an empty string as empty string is the same as not setting the id at all');\n    }\n    return [\n        id,\n        data,\n        trackedSymbol\n    ];\n}\n\nexport { isTrackedEnvelope, sse, tracked };\n", "import { Unpromise } from '../../vendor/unpromise/unpromise.mjs';\nimport { getTRPCErrorFromUnknown } from '../error/TRPCError.mjs';\nimport { isAbortError } from '../http/abortError.mjs';\nimport { identity, run } from '../utils.mjs';\nimport { isTrackedEnvelope } from './tracked.mjs';\nimport { takeWithGrace, withMaxDuration } from './utils/asyncIterable.mjs';\nimport { makeAsyncResource } from './utils/disposable.mjs';\nimport { readableStreamFrom } from './utils/readableStreamFrom.mjs';\nimport { timerResource, disposablePromiseTimerResult } from './utils/timerResource.mjs';\nimport { withPing, PING_SYM } from './utils/withPing.mjs';\n\nfunction _ts_add_disposable_resource(env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose, inner;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n            if (async) inner = dispose;\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        if (inner) dispose = function() {\n            try {\n                inner.call(this);\n            } catch (e) {\n                return Promise.reject(e);\n            }\n        };\n        env.stack.push({\n            value: value,\n            dispose: dispose,\n            async: async\n        });\n    } else if (async) {\n        env.stack.push({\n            async: true\n        });\n    }\n    return value;\n}\nfunction _ts_dispose_resources(env) {\n    var _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function(error, suppressed, message) {\n        var e = new Error(message);\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n    };\n    return (_ts_dispose_resources = function _ts_dispose_resources(env) {\n        function fail(e) {\n            env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        var r, s = 0;\n        function next() {\n            while(r = env.stack.pop()){\n                try {\n                    if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n                    if (r.dispose) {\n                        var result = r.dispose.call(r.value);\n                        if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {\n                            fail(e);\n                            return next();\n                        });\n                    } else s |= 1;\n                } catch (e) {\n                    fail(e);\n                }\n            }\n            if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    })(env);\n}\nconst PING_EVENT = 'ping';\nconst SERIALIZED_ERROR_EVENT = 'serialized-error';\nconst CONNECTED_EVENT = 'connected';\nconst RETURN_EVENT = 'return';\n/**\n *\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */ function sseStreamProducer(opts) {\n    const { serialize = identity } = opts;\n    const ping = {\n        enabled: opts.ping?.enabled ?? false,\n        intervalMs: opts.ping?.intervalMs ?? 1000\n    };\n    const client = opts.client ?? {};\n    if (ping.enabled && client.reconnectAfterInactivityMs && ping.intervalMs > client.reconnectAfterInactivityMs) {\n        throw new Error(`Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${ping.intervalMs} client.reconnectAfterInactivityMs: ${client.reconnectAfterInactivityMs}`);\n    }\n    async function* generator() {\n        yield {\n            event: CONNECTED_EVENT,\n            data: JSON.stringify(client)\n        };\n        let iterable = opts.data;\n        if (opts.emitAndEndImmediately) {\n            iterable = takeWithGrace(iterable, {\n                count: 1,\n                gracePeriodMs: 1\n            });\n        }\n        if (opts.maxDurationMs && opts.maxDurationMs > 0 && opts.maxDurationMs !== Infinity) {\n            iterable = withMaxDuration(iterable, {\n                maxDurationMs: opts.maxDurationMs\n            });\n        }\n        if (ping.enabled && ping.intervalMs !== Infinity && ping.intervalMs > 0) {\n            iterable = withPing(iterable, ping.intervalMs);\n        }\n        // We need those declarations outside the loop for garbage collection reasons. If they were\n        // declared inside, they would not be freed until the next value is present.\n        let value;\n        let chunk;\n        for await (value of iterable){\n            if (value === PING_SYM) {\n                yield {\n                    event: PING_EVENT,\n                    data: ''\n                };\n                continue;\n            }\n            chunk = isTrackedEnvelope(value) ? {\n                id: value[0],\n                data: value[1]\n            } : {\n                data: value\n            };\n            chunk.data = JSON.stringify(serialize(chunk.data));\n            yield chunk;\n            // free up references for garbage collection\n            value = null;\n            chunk = null;\n        }\n    }\n    async function* generatorWithErrorHandling() {\n        try {\n            yield* generator();\n            yield {\n                event: RETURN_EVENT,\n                data: ''\n            };\n        } catch (cause) {\n            if (isAbortError(cause)) {\n                // ignore abort errors, send any other errors\n                return;\n            }\n            // `err` must be caused by `opts.data`, `JSON.stringify` or `serialize`.\n            // So, a user error in any case.\n            const error = getTRPCErrorFromUnknown(cause);\n            const data = opts.formatError?.({\n                error\n            }) ?? null;\n            yield {\n                event: SERIALIZED_ERROR_EVENT,\n                data: JSON.stringify(serialize(data))\n            };\n        }\n    }\n    const stream = readableStreamFrom(generatorWithErrorHandling());\n    return stream.pipeThrough(new TransformStream({\n        transform (chunk, controller) {\n            if ('event' in chunk) {\n                controller.enqueue(`event: ${chunk.event}\\n`);\n            }\n            if ('data' in chunk) {\n                controller.enqueue(`data: ${chunk.data}\\n`);\n            }\n            if ('id' in chunk) {\n                controller.enqueue(`id: ${chunk.id}\\n`);\n            }\n            if ('comment' in chunk) {\n                controller.enqueue(`: ${chunk.comment}\\n`);\n            }\n            controller.enqueue('\\n\\n');\n        }\n    })).pipeThrough(new TextEncoderStream());\n}\nasync function withTimeout(opts) {\n    const env = {\n        stack: [],\n        error: void 0,\n        hasError: false\n    };\n    try {\n        const timeoutPromise = _ts_add_disposable_resource(env, timerResource(opts.timeoutMs), false);\n        ;\n        const res = await Unpromise.race([\n            opts.promise,\n            timeoutPromise.start()\n        ]);\n        if (res === disposablePromiseTimerResult) {\n            return await opts.onTimeout();\n        }\n        return res;\n    } catch (e) {\n        env.error = e;\n        env.hasError = true;\n    } finally{\n        _ts_dispose_resources(env);\n    }\n}\n/**\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */ function sseStreamConsumer(opts) {\n    const { deserialize = (v)=>v } = opts;\n    let clientOptions = {};\n    const signal = opts.signal;\n    let _es = null;\n    const createStream = ()=>new ReadableStream({\n            async start (controller) {\n                const [url, init] = await Promise.all([\n                    opts.url(),\n                    opts.init()\n                ]);\n                const eventSource = _es = new opts.EventSource(url, init);\n                controller.enqueue({\n                    type: 'connecting',\n                    eventSource: _es,\n                    event: null\n                });\n                eventSource.addEventListener(CONNECTED_EVENT, (_msg)=>{\n                    const msg = _msg;\n                    const options = JSON.parse(msg.data);\n                    clientOptions = options;\n                    controller.enqueue({\n                        type: 'connected',\n                        options,\n                        eventSource\n                    });\n                });\n                eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg)=>{\n                    const msg = _msg;\n                    controller.enqueue({\n                        type: 'serialized-error',\n                        error: deserialize(JSON.parse(msg.data)),\n                        eventSource\n                    });\n                });\n                eventSource.addEventListener(PING_EVENT, ()=>{\n                    controller.enqueue({\n                        type: 'ping',\n                        eventSource\n                    });\n                });\n                eventSource.addEventListener(RETURN_EVENT, ()=>{\n                    eventSource.close();\n                    controller.close();\n                    _es = null;\n                });\n                eventSource.addEventListener('error', (event)=>{\n                    if (eventSource.readyState === eventSource.CLOSED) {\n                        controller.error(event);\n                    } else {\n                        controller.enqueue({\n                            type: 'connecting',\n                            eventSource,\n                            event\n                        });\n                    }\n                });\n                eventSource.addEventListener('message', (_msg)=>{\n                    const msg = _msg;\n                    const chunk = deserialize(JSON.parse(msg.data));\n                    const def = {\n                        data: chunk\n                    };\n                    if (msg.lastEventId) {\n                        def.id = msg.lastEventId;\n                    }\n                    controller.enqueue({\n                        type: 'data',\n                        data: def,\n                        eventSource\n                    });\n                });\n                const onAbort = ()=>{\n                    try {\n                        eventSource.close();\n                        controller.close();\n                    } catch  {\n                    // ignore errors in case the controller is already closed\n                    }\n                };\n                if (signal.aborted) {\n                    onAbort();\n                } else {\n                    signal.addEventListener('abort', onAbort);\n                }\n            },\n            cancel () {\n                _es?.close();\n            }\n        });\n    const getStreamResource = ()=>{\n        let stream = createStream();\n        let reader = stream.getReader();\n        async function dispose() {\n            await reader.cancel();\n            _es = null;\n        }\n        return makeAsyncResource({\n            read () {\n                return reader.read();\n            },\n            async recreate () {\n                await dispose();\n                stream = createStream();\n                reader = stream.getReader();\n            }\n        }, dispose);\n    };\n    return run(async function*() {\n        const env = {\n            stack: [],\n            error: void 0,\n            hasError: false\n        };\n        try {\n            const stream = _ts_add_disposable_resource(env, getStreamResource(), true);\n            ;\n            while(true){\n                let promise = stream.read();\n                const timeoutMs = clientOptions.reconnectAfterInactivityMs;\n                if (timeoutMs) {\n                    promise = withTimeout({\n                        promise,\n                        timeoutMs,\n                        onTimeout: async ()=>{\n                            const res = {\n                                value: {\n                                    type: 'timeout',\n                                    ms: timeoutMs,\n                                    eventSource: _es\n                                },\n                                done: false\n                            };\n                            // Close and release old reader\n                            await stream.recreate();\n                            return res;\n                        }\n                    });\n                }\n                const result = await promise;\n                if (result.done) {\n                    return result.value;\n                }\n                yield result.value;\n            }\n        } catch (e) {\n            env.error = e;\n            env.hasError = true;\n        } finally{\n            const result = _ts_dispose_resources(env);\n            if (result) await result;\n        }\n    });\n}\nconst sseHeaders = {\n    'Content-Type': 'text/event-stream',\n    'Cache-Control': 'no-cache, no-transform',\n    'X-Accel-Buffering': 'no',\n    Connection: 'keep-alive'\n};\n\nexport { sseHeaders, sseStreamConsumer, sseStreamProducer };\n", "import { TRPCError } from './error/TRPCError.mjs';\nimport { isObject } from './utils.mjs';\n\n/** @internal */ const middlewareMarker = 'middlewareMarker';\n/**\n * @internal\n */ function createMiddlewareFactory() {\n    function createMiddlewareInner(middlewares) {\n        return {\n            _middlewares: middlewares,\n            unstable_pipe (middlewareBuilderOrFn) {\n                const pipedMiddleware = '_middlewares' in middlewareBuilderOrFn ? middlewareBuilderOrFn._middlewares : [\n                    middlewareBuilderOrFn\n                ];\n                return createMiddlewareInner([\n                    ...middlewares,\n                    ...pipedMiddleware\n                ]);\n            }\n        };\n    }\n    function createMiddleware(fn) {\n        return createMiddlewareInner([\n            fn\n        ]);\n    }\n    return createMiddleware;\n}\n/**\n * Create a standalone middleware\n * @see https://trpc.io/docs/v11/server/middlewares#experimental-standalone-middlewares\n * @deprecated use `.concat()` instead\n */ const experimental_standaloneMiddleware = ()=>({\n        create: createMiddlewareFactory()\n    });\n/**\n * @internal\n * Please note, `trpc-openapi` uses this function.\n */ function createInputMiddleware(parse) {\n    const inputMiddleware = async function inputValidatorMiddleware(opts) {\n        let parsedInput;\n        const rawInput = await opts.getRawInput();\n        try {\n            parsedInput = await parse(rawInput);\n        } catch (cause) {\n            throw new TRPCError({\n                code: 'BAD_REQUEST',\n                cause\n            });\n        }\n        // Multiple input parsers\n        const combinedInput = isObject(opts.input) && isObject(parsedInput) ? {\n            ...opts.input,\n            ...parsedInput\n        } : parsedInput;\n        return opts.next({\n            input: combinedInput\n        });\n    };\n    inputMiddleware._type = 'input';\n    return inputMiddleware;\n}\n/**\n * @internal\n */ function createOutputMiddleware(parse) {\n    const outputMiddleware = async function outputValidatorMiddleware({ next }) {\n        const result = await next();\n        if (!result.ok) {\n            // pass through failures without validating\n            return result;\n        }\n        try {\n            const data = await parse(result.data);\n            return {\n                ...result,\n                data\n            };\n        } catch (cause) {\n            throw new TRPCError({\n                message: 'Output validation failed',\n                code: 'INTERNAL_SERVER_ERROR',\n                cause\n            });\n        }\n    };\n    outputMiddleware._type = 'output';\n    return outputMiddleware;\n}\n\nexport { createInputMiddleware, createMiddlewareFactory, createOutputMiddleware, experimental_standaloneMiddleware, middlewareMarker };\n", "function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/** A schema error with useful information. */ class StandardSchemaV1Error extends Error {\n    /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */ constructor(issues){\n        super(issues[0]?.message), /** The schema issues. */ _define_property(this, \"issues\", void 0);\n        this.name = 'SchemaError';\n        this.issues = issues;\n    }\n}\n\nexport { StandardSchemaV1Error };\n", "import { StandardSchemaV1Error } from '../vendor/standard-schema-v1/error.mjs';\n\nfunction getParseFn(procedureParser) {\n    const parser = procedureParser;\n    const isStandardSchema = '~standard' in parser;\n    if (typeof parser === 'function' && typeof parser.assert === 'function') {\n        // ParserArkTypeEsque - arktype schemas shouldn't be called as a function because they return a union type instead of throwing\n        return parser.assert.bind(parser);\n    }\n    if (typeof parser === 'function' && !isStandardSchema) {\n        // ParserValibotEsque (>= v0.31.0)\n        // ParserCustomValidatorEsque - note the check for standard-schema conformance - some libraries like `effect` use function schemas which are *not* a \"parse\" function.\n        return parser;\n    }\n    if (typeof parser.parseAsync === 'function') {\n        // ParserZodEsque\n        return parser.parseAsync.bind(parser);\n    }\n    if (typeof parser.parse === 'function') {\n        // ParserZodEsque\n        // ParserValibotEsque (< v0.13.0)\n        return parser.parse.bind(parser);\n    }\n    if (typeof parser.validateSync === 'function') {\n        // ParserYupEsque\n        return parser.validateSync.bind(parser);\n    }\n    if (typeof parser.create === 'function') {\n        // ParserSuperstructEsque\n        return parser.create.bind(parser);\n    }\n    if (typeof parser.assert === 'function') {\n        // ParserScaleEsque\n        return (value)=>{\n            parser.assert(value);\n            return value;\n        };\n    }\n    if (isStandardSchema) {\n        // StandardSchemaEsque\n        return async (value)=>{\n            const result = await parser['~standard'].validate(value);\n            if (result.issues) {\n                throw new StandardSchemaV1Error(result.issues);\n            }\n            return result.value;\n        };\n    }\n    throw new Error('Could not find a validator fn');\n}\n\nexport { getParseFn };\n", "import { TRPCError, getTRPCErrorFromUnknown } from './error/TRPCError.mjs';\nimport { createOutputMiddleware, createInputMiddleware, middlewareMarker } from './middleware.mjs';\nimport { getParseFn } from './parser.mjs';\nimport { mergeWithoutOverrides } from './utils.mjs';\n\nfunction createNewBuilder(def1, def2) {\n    const { middlewares = [], inputs, meta, ...rest } = def2;\n    // TODO: maybe have a fn here to warn about calls\n    return createBuilder({\n        ...mergeWithoutOverrides(def1, rest),\n        inputs: [\n            ...def1.inputs,\n            ...inputs ?? []\n        ],\n        middlewares: [\n            ...def1.middlewares,\n            ...middlewares\n        ],\n        meta: def1.meta && meta ? {\n            ...def1.meta,\n            ...meta\n        } : meta ?? def1.meta\n    });\n}\nfunction createBuilder(initDef = {}) {\n    const _def = {\n        procedure: true,\n        inputs: [],\n        middlewares: [],\n        ...initDef\n    };\n    const builder = {\n        _def,\n        input (input) {\n            const parser = getParseFn(input);\n            return createNewBuilder(_def, {\n                inputs: [\n                    input\n                ],\n                middlewares: [\n                    createInputMiddleware(parser)\n                ]\n            });\n        },\n        output (output) {\n            const parser = getParseFn(output);\n            return createNewBuilder(_def, {\n                output,\n                middlewares: [\n                    createOutputMiddleware(parser)\n                ]\n            });\n        },\n        meta (meta) {\n            return createNewBuilder(_def, {\n                meta\n            });\n        },\n        use (middlewareBuilderOrFn) {\n            // Distinguish between a middleware builder and a middleware function\n            const middlewares = '_middlewares' in middlewareBuilderOrFn ? middlewareBuilderOrFn._middlewares : [\n                middlewareBuilderOrFn\n            ];\n            return createNewBuilder(_def, {\n                middlewares: middlewares\n            });\n        },\n        unstable_concat (builder) {\n            return createNewBuilder(_def, builder._def);\n        },\n        concat (builder) {\n            return createNewBuilder(_def, builder._def);\n        },\n        query (resolver) {\n            return createResolver({\n                ..._def,\n                type: 'query'\n            }, resolver);\n        },\n        mutation (resolver) {\n            return createResolver({\n                ..._def,\n                type: 'mutation'\n            }, resolver);\n        },\n        subscription (resolver) {\n            return createResolver({\n                ..._def,\n                type: 'subscription'\n            }, resolver);\n        },\n        experimental_caller (caller) {\n            return createNewBuilder(_def, {\n                caller\n            });\n        }\n    };\n    return builder;\n}\nfunction createResolver(_defIn, resolver) {\n    const finalBuilder = createNewBuilder(_defIn, {\n        resolver,\n        middlewares: [\n            async function resolveMiddleware(opts) {\n                const data = await resolver(opts);\n                return {\n                    marker: middlewareMarker,\n                    ok: true,\n                    data,\n                    ctx: opts.ctx\n                };\n            }\n        ]\n    });\n    const _def = {\n        ...finalBuilder._def,\n        type: _defIn.type,\n        experimental_caller: Boolean(finalBuilder._def.caller),\n        meta: finalBuilder._def.meta,\n        $types: null\n    };\n    const invoke = createProcedureCaller(finalBuilder._def);\n    const callerOverride = finalBuilder._def.caller;\n    if (!callerOverride) {\n        return invoke;\n    }\n    const callerWrapper = async (...args)=>{\n        return await callerOverride({\n            args,\n            invoke,\n            _def: _def\n        });\n    };\n    callerWrapper._def = _def;\n    return callerWrapper;\n}\nconst codeblock = `\nThis is a client-only function.\nIf you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls\n`.trim();\n// run the middlewares recursively with the resolver as the last one\nasync function callRecursive(index, _def, opts) {\n    try {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const middleware = _def.middlewares[index];\n        const result = await middleware({\n            ...opts,\n            meta: _def.meta,\n            input: opts.input,\n            next (_nextOpts) {\n                const nextOpts = _nextOpts;\n                return callRecursive(index + 1, _def, {\n                    ...opts,\n                    ctx: nextOpts?.ctx ? {\n                        ...opts.ctx,\n                        ...nextOpts.ctx\n                    } : opts.ctx,\n                    input: nextOpts && 'input' in nextOpts ? nextOpts.input : opts.input,\n                    getRawInput: nextOpts?.getRawInput ?? opts.getRawInput\n                });\n            }\n        });\n        return result;\n    } catch (cause) {\n        return {\n            ok: false,\n            error: getTRPCErrorFromUnknown(cause),\n            marker: middlewareMarker\n        };\n    }\n}\nfunction createProcedureCaller(_def) {\n    async function procedure(opts) {\n        // is direct server-side call\n        if (!opts || !('getRawInput' in opts)) {\n            throw new Error(codeblock);\n        }\n        // there's always at least one \"next\" since we wrap this.resolver in a middleware\n        const result = await callRecursive(0, _def, opts);\n        if (!result) {\n            throw new TRPCError({\n                code: 'INTERNAL_SERVER_ERROR',\n                message: 'No result from middlewares - did you forget to `return next()`?'\n            });\n        }\n        if (!result.ok) {\n            // re-throw original error\n            throw result.error;\n        }\n        return result.data;\n    }\n    procedure._def = _def;\n    procedure.procedure = true;\n    procedure.meta = _def.meta;\n    // FIXME typecast shouldn't be needed - fixittt\n    return procedure;\n}\n\nexport { createBuilder };\n", "/**\n * The default check to see if we're in a server\n */ const isServerDefault = typeof window === 'undefined' || 'Deno' in window || // eslint-disable-next-line @typescript-eslint/dot-notation\nglobalThis.process?.env?.['NODE_ENV'] === 'test' || !!globalThis.process?.env?.['JEST_WORKER_ID'] || !!globalThis.process?.env?.['VITEST_WORKER_ID'];\n\nexport { isServerDefault };\n", "import { defaultFormatter } from './error/formatter.mjs';\nimport { createMiddlewareFactory } from './middleware.mjs';\nimport { createBuilder } from './procedureBuilder.mjs';\nimport { isServerDefault } from './rootConfig.mjs';\nimport { createCallerFactory, mergeRouters, createRouterFactory } from './router.mjs';\nimport { getDataTransformer, defaultTransformer } from './transformer.mjs';\n\nclass TRPCBuilder {\n    /**\n   * Add a context shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/server/context\n   */ context() {\n        return new TRPCBuilder();\n    }\n    /**\n   * Add a meta shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/quickstart\n   */ meta() {\n        return new TRPCBuilder();\n    }\n    /**\n   * Create the root object\n   * @see https://trpc.io/docs/v11/server/routers#initialize-trpc\n   */ create(opts) {\n        const config = {\n            ...opts,\n            transformer: getDataTransformer(opts?.transformer ?? defaultTransformer),\n            isDev: opts?.isDev ?? // eslint-disable-next-line @typescript-eslint/dot-notation\n            globalThis.process?.env['NODE_ENV'] !== 'production',\n            allowOutsideOfServer: opts?.allowOutsideOfServer ?? false,\n            errorFormatter: opts?.errorFormatter ?? defaultFormatter,\n            isServer: opts?.isServer ?? isServerDefault,\n            /**\n       * These are just types, they can't be used at runtime\n       * @internal\n       */ $types: null\n        };\n        {\n            // Server check\n            const isServer = opts?.isServer ?? isServerDefault;\n            if (!isServer && opts?.allowOutsideOfServer !== true) {\n                throw new Error(`You're trying to use @trpc/server in a non-server environment. This is not supported by default.`);\n            }\n        }\n        return {\n            /**\n       * Your router config\n       * @internal\n       */ _config: config,\n            /**\n       * Builder object for creating procedures\n       * @see https://trpc.io/docs/v11/server/procedures\n       */ procedure: createBuilder({\n                meta: opts?.defaultMeta\n            }),\n            /**\n       * Create reusable middlewares\n       * @see https://trpc.io/docs/v11/server/middlewares\n       */ middleware: createMiddlewareFactory(),\n            /**\n       * Create a router\n       * @see https://trpc.io/docs/v11/server/routers\n       */ router: createRouterFactory(config),\n            /**\n       * Merge Routers\n       * @see https://trpc.io/docs/v11/server/merging-routers\n       */ mergeRouters,\n            /**\n       * Create a server-side caller for a router\n       * @see https://trpc.io/docs/v11/server/server-side-calls\n       */ createCallerFactory: createCallerFactory()\n        };\n    }\n}\n/**\n * Builder to initialize the tRPC root object - use this exactly once per backend\n * @see https://trpc.io/docs/v11/quickstart\n */ const initTRPC = new TRPCBuilder();\n\nexport { initTRPC };\n", "import { isObject } from '@trpc/server/unstable-core-do-not-import';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction isTRPCClientError(cause) {\n    return cause instanceof TRPCClientError || /**\n     * @deprecated\n     * Delete in next major\n     */ cause instanceof Error && cause.name === 'TRPCClientError';\n}\nfunction isTRPCErrorResponse(obj) {\n    return isObject(obj) && isObject(obj['error']) && typeof obj['error']['code'] === 'number' && typeof obj['error']['message'] === 'string';\n}\nfunction getMessageFromUnknownError(err, fallback) {\n    if (typeof err === 'string') {\n        return err;\n    }\n    if (isObject(err) && typeof err['message'] === 'string') {\n        return err['message'];\n    }\n    return fallback;\n}\nclass TRPCClientError extends Error {\n    static from(_cause, opts = {}) {\n        const cause = _cause;\n        if (isTRPCClientError(cause)) {\n            if (opts.meta) {\n                // Decorate with meta error data\n                cause.meta = {\n                    ...cause.meta,\n                    ...opts.meta\n                };\n            }\n            return cause;\n        }\n        if (isTRPCErrorResponse(cause)) {\n            return new TRPCClientError(cause.error.message, {\n                ...opts,\n                result: cause\n            });\n        }\n        return new TRPCClientError(getMessageFromUnknownError(cause, 'Unknown error'), {\n            ...opts,\n            cause: cause\n        });\n    }\n    constructor(message, opts){\n        const cause = opts?.cause;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore https://github.com/tc39/proposal-error-cause\n        super(message, {\n            cause\n        }), // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n        _define_property(this, \"cause\", void 0), _define_property(this, \"shape\", void 0), _define_property(this, \"data\", void 0), /**\n   * Additional meta data about the error\n   * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here\n   */ _define_property(this, \"meta\", void 0);\n        this.meta = opts?.meta;\n        this.cause = cause;\n        this.shape = opts?.result?.error;\n        this.data = opts?.result?.error.data;\n        this.name = 'TRPCClientError';\n        Object.setPrototypeOf(this, TRPCClientError.prototype);\n    }\n}\n\nexport { TRPCClientError };\n", "import { share, observableToPromise } from '@trpc/server/observable';\nimport { create<PERSON>hain } from '../links/internals/createChain.mjs';\nimport { TRPCClientError } from '../TRPCClientError.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nclass TRPCUntypedClient {\n    $request(opts) {\n        const chain$ = createChain({\n            links: this.links,\n            op: {\n                ...opts,\n                context: opts.context ?? {},\n                id: ++this.requestId\n            }\n        });\n        return chain$.pipe(share());\n    }\n    async requestAsPromise(opts) {\n        try {\n            const req$ = this.$request(opts);\n            const envelope = await observableToPromise(req$);\n            const data = envelope.result.data;\n            return data;\n        } catch (err) {\n            throw TRPCClientError.from(err);\n        }\n    }\n    query(path, input, opts) {\n        return this.requestAsPromise({\n            type: 'query',\n            path,\n            input,\n            context: opts?.context,\n            signal: opts?.signal\n        });\n    }\n    mutation(path, input, opts) {\n        return this.requestAsPromise({\n            type: 'mutation',\n            path,\n            input,\n            context: opts?.context,\n            signal: opts?.signal\n        });\n    }\n    subscription(path, input, opts) {\n        const observable$ = this.$request({\n            type: 'subscription',\n            path,\n            input,\n            context: opts.context,\n            signal: opts.signal\n        });\n        return observable$.subscribe({\n            next (envelope) {\n                switch(envelope.result.type){\n                    case 'state':\n                        {\n                            opts.onConnectionStateChange?.(envelope.result);\n                            break;\n                        }\n                    case 'started':\n                        {\n                            opts.onStarted?.({\n                                context: envelope.context\n                            });\n                            break;\n                        }\n                    case 'stopped':\n                        {\n                            opts.onStopped?.();\n                            break;\n                        }\n                    case 'data':\n                    case undefined:\n                        {\n                            opts.onData?.(envelope.result.data);\n                            break;\n                        }\n                }\n            },\n            error (err) {\n                opts.onError?.(err);\n            },\n            complete () {\n                opts.onComplete?.();\n            }\n        });\n    }\n    constructor(opts){\n        _define_property(this, \"links\", void 0);\n        _define_property(this, \"runtime\", void 0);\n        _define_property(this, \"requestId\", void 0);\n        this.requestId = 0;\n        this.runtime = {};\n        // Initialize the links\n        this.links = opts.links.map((link)=>link(this.runtime));\n    }\n}\n\nexport { TRPCUntypedClient };\n", "import { TRPCUntypedClient } from './internals/TRPCUntypedClient.mjs';\n\nfunction createTRPCUntypedClient(opts) {\n    return new TRPCUntypedClient(opts);\n}\n\nexport { TRPCUntypedClient, createTRPCUntypedClient };\n", "import { createRecursiveProxy, createFlatProxy } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient.mjs';\n\nconst untypedClientSymbol = Symbol.for('trpc_untypedClient');\nconst clientCallTypeMap = {\n    query: 'query',\n    mutate: 'mutation',\n    subscribe: 'subscription'\n};\n/** @internal */ const clientCallTypeToProcedureType = (clientCallType)=>{\n    return clientCallTypeMap[clientCallType];\n};\n/**\n * @internal\n */ function createTRPCClientProxy(client) {\n    const proxy = createRecursiveProxy(({ path, args })=>{\n        const pathCopy = [\n            ...path\n        ];\n        const procedureType = clientCallTypeToProcedureType(pathCopy.pop());\n        const fullPath = pathCopy.join('.');\n        return client[procedureType](fullPath, ...args);\n    });\n    return createFlatProxy((key)=>{\n        if (key === untypedClientSymbol) {\n            return client;\n        }\n        return proxy[key];\n    });\n}\nfunction createTRPCClient(opts) {\n    const client = new TRPCUntypedClient(opts);\n    const proxy = createTRPCClientProxy(client);\n    return proxy;\n}\n/**\n * Get an untyped client from a proxy client\n * @internal\n */ function getUntypedClient(client) {\n    return client[untypedClientSymbol];\n}\n\nexport { clientCallTypeToProcedureType, createTRPCClient, createTRPCClientProxy, getUntypedClient };\n", "const isFunction = (fn)=>typeof fn === 'function';\nfunction getFetch(customFetchImpl) {\n    if (customFetchImpl) {\n        return customFetchImpl;\n    }\n    if (typeof window !== 'undefined' && isFunction(window.fetch)) {\n        return window.fetch;\n    }\n    if (typeof globalThis !== 'undefined' && isFunction(globalThis.fetch)) {\n        return globalThis.fetch;\n    }\n    throw new Error('No fetch implementation found');\n}\n\nexport { getFetch };\n", "function isOctetType(input) {\n    return input instanceof Uint8Array || // File extends from Blob but is only available in nodejs from v20\n    input instanceof Blob;\n}\nfunction isFormData(input) {\n    return input instanceof FormData;\n}\nfunction isNonJsonSerializable(input) {\n    return isOctetType(input) || isFormData(input);\n}\n\nexport { isFormData, isNonJsonSerializable, isOctetType };\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */ /**\n * A function that should never be called unless we messed something up.\n */ const throwFatalError = ()=>{\n    throw new Error('Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new');\n};\n/**\n * Dataloader that's very inspired by https://github.com/graphql/dataloader\n * Less configuration, no caching, and allows you to cancel requests\n * When cancelling a single fetch the whole batch will be cancelled only when _all_ items are cancelled\n */ function dataLoader(batchLoader) {\n    let pendingItems = null;\n    let dispatchTimer = null;\n    const destroyTimerAndPendingItems = ()=>{\n        clearTimeout(dispatchTimer);\n        dispatchTimer = null;\n        pendingItems = null;\n    };\n    /**\n   * Iterate through the items and split them into groups based on the `batchLoader`'s validate function\n   */ function groupItems(items) {\n        const groupedItems = [\n            []\n        ];\n        let index = 0;\n        while(true){\n            const item = items[index];\n            if (!item) {\n                break;\n            }\n            const lastGroup = groupedItems[groupedItems.length - 1];\n            if (item.aborted) {\n                // Item was aborted before it was dispatched\n                item.reject?.(new Error('Aborted'));\n                index++;\n                continue;\n            }\n            const isValid = batchLoader.validate(lastGroup.concat(item).map((it)=>it.key));\n            if (isValid) {\n                lastGroup.push(item);\n                index++;\n                continue;\n            }\n            if (lastGroup.length === 0) {\n                item.reject?.(new Error('Input is too big for a single dispatch'));\n                index++;\n                continue;\n            }\n            // Create new group, next iteration will try to add the item to that\n            groupedItems.push([]);\n        }\n        return groupedItems;\n    }\n    function dispatch() {\n        const groupedItems = groupItems(pendingItems);\n        destroyTimerAndPendingItems();\n        // Create batches for each group of items\n        for (const items of groupedItems){\n            if (!items.length) {\n                continue;\n            }\n            const batch = {\n                items\n            };\n            for (const item of items){\n                item.batch = batch;\n            }\n            const promise = batchLoader.fetch(batch.items.map((_item)=>_item.key));\n            promise.then(async (result)=>{\n                await Promise.all(result.map(async (valueOrPromise, index)=>{\n                    const item = batch.items[index];\n                    try {\n                        const value = await Promise.resolve(valueOrPromise);\n                        item.resolve?.(value);\n                    } catch (cause) {\n                        item.reject?.(cause);\n                    }\n                    item.batch = null;\n                    item.reject = null;\n                    item.resolve = null;\n                }));\n                for (const item of batch.items){\n                    item.reject?.(new Error('Missing result'));\n                    item.batch = null;\n                }\n            }).catch((cause)=>{\n                for (const item of batch.items){\n                    item.reject?.(cause);\n                    item.batch = null;\n                }\n            });\n        }\n    }\n    function load(key) {\n        const item = {\n            aborted: false,\n            key,\n            batch: null,\n            resolve: throwFatalError,\n            reject: throwFatalError\n        };\n        const promise = new Promise((resolve, reject)=>{\n            item.reject = reject;\n            item.resolve = resolve;\n            pendingItems ?? (pendingItems = []);\n            pendingItems.push(item);\n        });\n        dispatchTimer ?? (dispatchTimer = setTimeout(dispatch));\n        return promise;\n    }\n    return {\n        load\n    };\n}\n\nexport { dataLoader };\n", "/**\n * Like `Promise.all()` but for abort signals\n * - When all signals have been aborted, the merged signal will be aborted\n * - If one signal is `null`, no signal will be aborted\n */ function allAbortSignals(...signals) {\n    const ac = new AbortController();\n    const count = signals.length;\n    let abortedCount = 0;\n    const onAbort = ()=>{\n        if (++abortedCount === count) {\n            ac.abort();\n        }\n    };\n    for (const signal of signals){\n        if (signal?.aborted) {\n            onAbort();\n        } else {\n            signal?.addEventListener('abort', onAbort, {\n                once: true\n            });\n        }\n    }\n    return ac.signal;\n}\n/**\n * Like `Promise.race` but for abort signals\n *\n * Basically, a ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */ function raceAbortSignals(...signals) {\n    const ac = new AbortController();\n    for (const signal of signals){\n        if (signal?.aborted) {\n            ac.abort();\n        } else {\n            signal?.addEventListener('abort', ()=>ac.abort(), {\n                once: true\n            });\n        }\n    }\n    return ac.signal;\n}\n\nexport { allAbortSignals, raceAbortSignals };\n", "/**\n * @internal\n */ /**\n * @internal\n */ function getTransformer(transformer) {\n    const _transformer = transformer;\n    if (!_transformer) {\n        return {\n            input: {\n                serialize: (data)=>data,\n                deserialize: (data)=>data\n            },\n            output: {\n                serialize: (data)=>data,\n                deserialize: (data)=>data\n            }\n        };\n    }\n    if ('input' in _transformer) {\n        return _transformer;\n    }\n    return {\n        input: _transformer,\n        output: _transformer\n    };\n}\n\nexport { getTransformer };\n", "import { getFetch } from '../../getFetch.mjs';\nimport { getTransformer } from '../../internals/transformer.mjs';\n\nfunction resolveHTTPLinkOptions(opts) {\n    return {\n        url: opts.url.toString(),\n        fetch: opts.fetch,\n        transformer: getTransformer(opts.transformer),\n        methodOverride: opts.methodOverride\n    };\n}\n// https://github.com/trpc/trpc/pull/669\nfunction arrayToDict(array) {\n    const dict = {};\n    for(let index = 0; index < array.length; index++){\n        const element = array[index];\n        dict[index] = element;\n    }\n    return dict;\n}\nconst METHOD = {\n    query: 'GET',\n    mutation: 'POST',\n    subscription: 'PATCH'\n};\nfunction getInput(opts) {\n    return 'input' in opts ? opts.transformer.input.serialize(opts.input) : arrayToDict(opts.inputs.map((_input)=>opts.transformer.input.serialize(_input)));\n}\nconst getUrl = (opts)=>{\n    const parts = opts.url.split('?');\n    const base = parts[0].replace(/\\/$/, ''); // Remove any trailing slashes\n    let url = base + '/' + opts.path;\n    const queryParts = [];\n    if (parts[1]) {\n        queryParts.push(parts[1]);\n    }\n    if ('inputs' in opts) {\n        queryParts.push('batch=1');\n    }\n    if (opts.type === 'query' || opts.type === 'subscription') {\n        const input = getInput(opts);\n        if (input !== undefined && opts.methodOverride !== 'POST') {\n            queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);\n        }\n    }\n    if (queryParts.length) {\n        url += '?' + queryParts.join('&');\n    }\n    return url;\n};\nconst getBody = (opts)=>{\n    if (opts.type === 'query' && opts.methodOverride !== 'POST') {\n        return undefined;\n    }\n    const input = getInput(opts);\n    return input !== undefined ? JSON.stringify(input) : undefined;\n};\nconst jsonHttpRequester = (opts)=>{\n    return httpRequest({\n        ...opts,\n        contentTypeHeader: 'application/json',\n        getUrl,\n        getBody\n    });\n};\n/**\n * Polyfill for DOMException with AbortError name\n */ class AbortError extends Error {\n    constructor(){\n        const name = 'AbortError';\n        super(name);\n        this.name = name;\n        this.message = name;\n    }\n}\n/**\n * Polyfill for `signal.throwIfAborted()`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted\n */ const throwIfAborted = (signal)=>{\n    if (!signal?.aborted) {\n        return;\n    }\n    // If available, use the native implementation\n    signal.throwIfAborted?.();\n    // If we have `DOMException`, use it\n    if (typeof DOMException !== 'undefined') {\n        throw new DOMException('AbortError', 'AbortError');\n    }\n    // Otherwise, use our own implementation\n    throw new AbortError();\n};\nasync function fetchHTTPResponse(opts) {\n    throwIfAborted(opts.signal);\n    const url = opts.getUrl(opts);\n    const body = opts.getBody(opts);\n    const { type } = opts;\n    const resolvedHeaders = await (async ()=>{\n        const heads = await opts.headers();\n        if (Symbol.iterator in heads) {\n            return Object.fromEntries(heads);\n        }\n        return heads;\n    })();\n    const headers = {\n        ...opts.contentTypeHeader ? {\n            'content-type': opts.contentTypeHeader\n        } : {},\n        ...opts.trpcAcceptHeader ? {\n            'trpc-accept': opts.trpcAcceptHeader\n        } : undefined,\n        ...resolvedHeaders\n    };\n    return getFetch(opts.fetch)(url, {\n        method: opts.methodOverride ?? METHOD[type],\n        signal: opts.signal,\n        body,\n        headers\n    });\n}\nasync function httpRequest(opts) {\n    const meta = {};\n    const res = await fetchHTTPResponse(opts);\n    meta.response = res;\n    const json = await res.json();\n    meta.responseJSON = json;\n    return {\n        json: json,\n        meta\n    };\n}\n\nexport { fetchHTTPResponse, getBody, getInput, getUrl, httpRequest, jsonHttpRequester, resolveHTTPLinkOptions };\n", "import { observable } from '@trpc/server/observable';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport { dataLoader } from '../internals/dataLoader.mjs';\nimport { allAbortSignals } from '../internals/signals.mjs';\nimport { TRPCClientError } from '../TRPCClientError.mjs';\nimport { resolveHTTPLinkOptions, jsonHttpRequester, getUrl } from './internals/httpUtils.mjs';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchLink\n */ function httpBatchLink(opts) {\n    const resolvedOpts = resolveHTTPLinkOptions(opts);\n    const maxURLLength = opts.maxURLLength ?? Infinity;\n    const maxItems = opts.maxItems ?? Infinity;\n    return ()=>{\n        const batchLoader = (type)=>{\n            return {\n                validate (batchOps) {\n                    if (maxURLLength === Infinity && maxItems === Infinity) {\n                        // escape hatch for quick calcs\n                        return true;\n                    }\n                    if (batchOps.length > maxItems) {\n                        return false;\n                    }\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const url = getUrl({\n                        ...resolvedOpts,\n                        type,\n                        path,\n                        inputs,\n                        signal: null\n                    });\n                    return url.length <= maxURLLength;\n                },\n                async fetch (batchOps) {\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const signal = allAbortSignals(...batchOps.map((op)=>op.signal));\n                    const res = await jsonHttpRequester({\n                        ...resolvedOpts,\n                        path,\n                        inputs,\n                        type,\n                        headers () {\n                            if (!opts.headers) {\n                                return {};\n                            }\n                            if (typeof opts.headers === 'function') {\n                                return opts.headers({\n                                    opList: batchOps\n                                });\n                            }\n                            return opts.headers;\n                        },\n                        signal\n                    });\n                    const resJSON = Array.isArray(res.json) ? res.json : batchOps.map(()=>res.json);\n                    const result = resJSON.map((item)=>({\n                            meta: res.meta,\n                            json: item\n                        }));\n                    return result;\n                }\n            };\n        };\n        const query = dataLoader(batchLoader('query'));\n        const mutation = dataLoader(batchLoader('mutation'));\n        const loaders = {\n            query,\n            mutation\n        };\n        return ({ op })=>{\n            return observable((observer)=>{\n                /* istanbul ignore if -- @preserve */ if (op.type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const loader = loaders[op.type];\n                const promise = loader.load(op);\n                let _res = undefined;\n                promise.then((res)=>{\n                    _res = res;\n                    const transformed = transformResult(res.json, resolvedOpts.transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(TRPCClientError.from(transformed.error, {\n                            meta: res.meta\n                        }));\n                        return;\n                    }\n                    observer.next({\n                        context: res.meta,\n                        result: transformed.result\n                    });\n                    observer.complete();\n                }).catch((err)=>{\n                    observer.error(TRPCClientError.from(err, {\n                        meta: _res?.meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n\nexport { httpBatchLink };\n", "import { observable } from '@trpc/server/observable';\nimport { jsonlStreamConsumer } from '@trpc/server/unstable-core-do-not-import';\nimport { dataLoader } from '../internals/dataLoader.mjs';\nimport { allAbortSignals, raceAbortSignals } from '../internals/signals.mjs';\nimport { TRPCClientError } from '../TRPCClientError.mjs';\nimport { resolveHTTPLinkOptions, fetchHTTPResponse, getBody, getUrl } from './internals/httpUtils.mjs';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n */ function httpBatchStreamLink(opts) {\n    const resolvedOpts = resolveHTTPLinkOptions(opts);\n    const maxURLLength = opts.maxURLLength ?? Infinity;\n    const maxItems = opts.maxItems ?? Infinity;\n    return ()=>{\n        const batchLoader = (type)=>{\n            return {\n                validate (batchOps) {\n                    if (maxURLLength === Infinity && maxItems === Infinity) {\n                        // escape hatch for quick calcs\n                        return true;\n                    }\n                    if (batchOps.length > maxItems) {\n                        return false;\n                    }\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const url = getUrl({\n                        ...resolvedOpts,\n                        type,\n                        path,\n                        inputs,\n                        signal: null\n                    });\n                    return url.length <= maxURLLength;\n                },\n                async fetch (batchOps) {\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const batchSignals = allAbortSignals(...batchOps.map((op)=>op.signal));\n                    const abortController = new AbortController();\n                    const responsePromise = fetchHTTPResponse({\n                        ...resolvedOpts,\n                        signal: raceAbortSignals(batchSignals, abortController.signal),\n                        type,\n                        contentTypeHeader: 'application/json',\n                        trpcAcceptHeader: 'application/jsonl',\n                        getUrl,\n                        getBody,\n                        inputs,\n                        path,\n                        headers () {\n                            if (!opts.headers) {\n                                return {};\n                            }\n                            if (typeof opts.headers === 'function') {\n                                return opts.headers({\n                                    opList: batchOps\n                                });\n                            }\n                            return opts.headers;\n                        }\n                    });\n                    const res = await responsePromise;\n                    const [head] = await jsonlStreamConsumer({\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        from: res.body,\n                        deserialize: resolvedOpts.transformer.output.deserialize,\n                        // onError: console.error,\n                        formatError (opts) {\n                            const error = opts.error;\n                            return TRPCClientError.from({\n                                error\n                            });\n                        },\n                        abortController\n                    });\n                    const promises = Object.keys(batchOps).map(async (key)=>{\n                        let json = await Promise.resolve(head[key]);\n                        if ('result' in json) {\n                            /**\n                 * Not very pretty, but we need to unwrap nested data as promises\n                 * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n                 */ const result = await Promise.resolve(json.result);\n                            json = {\n                                result: {\n                                    data: await Promise.resolve(result.data)\n                                }\n                            };\n                        }\n                        return {\n                            json,\n                            meta: {\n                                response: res\n                            }\n                        };\n                    });\n                    return promises;\n                }\n            };\n        };\n        const query = dataLoader(batchLoader('query'));\n        const mutation = dataLoader(batchLoader('mutation'));\n        const loaders = {\n            query,\n            mutation\n        };\n        return ({ op })=>{\n            return observable((observer)=>{\n                /* istanbul ignore if -- @preserve */ if (op.type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const loader = loaders[op.type];\n                const promise = loader.load(op);\n                let _res = undefined;\n                promise.then((res)=>{\n                    _res = res;\n                    if ('error' in res.json) {\n                        observer.error(TRPCClientError.from(res.json, {\n                            meta: res.meta\n                        }));\n                        return;\n                    } else if ('result' in res.json) {\n                        observer.next({\n                            context: res.meta,\n                            result: res.json.result\n                        });\n                        observer.complete();\n                        return;\n                    }\n                    observer.complete();\n                }).catch((err)=>{\n                    observer.error(TRPCClientError.from(err, {\n                        meta: _res?.meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n/**\n * @deprecated use {@link httpBatchStreamLink} instead\n */ const unstable_httpBatchStreamLink = httpBatchStreamLink;\n\nexport { httpBatchStreamLink, unstable_httpBatchStreamLink };\n", "import { observable } from '@trpc/server/observable';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../TRPCClientError.mjs';\nimport { resolveHTTPLinkOptions, httpRequest, jsonHttpRequester, getUrl } from './internals/httpUtils.mjs';\nimport { isFormData, isOctetType } from './internals/contentTypes.mjs';\n\nconst universalRequester = (opts)=>{\n    if ('input' in opts) {\n        const { input } = opts;\n        if (isFormData(input)) {\n            if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n                throw new Error('FormData is only supported for mutations');\n            }\n            return httpRequest({\n                ...opts,\n                // The browser will set this automatically and include the boundary= in it\n                contentTypeHeader: undefined,\n                getUrl,\n                getBody: ()=>input\n            });\n        }\n        if (isOctetType(input)) {\n            if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n                throw new Error('Octet type input is only supported for mutations');\n            }\n            return httpRequest({\n                ...opts,\n                contentTypeHeader: 'application/octet-stream',\n                getUrl,\n                getBody: ()=>input\n            });\n        }\n    }\n    return jsonHttpRequester(opts);\n};\n/**\n * @see https://trpc.io/docs/client/links/httpLink\n */ function httpLink(opts) {\n    const resolvedOpts = resolveHTTPLinkOptions(opts);\n    return ()=>{\n        return ({ op })=>{\n            return observable((observer)=>{\n                const { path, input, type } = op;\n                /* istanbul ignore if -- @preserve */ if (type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const request = universalRequester({\n                    ...resolvedOpts,\n                    type,\n                    path,\n                    input,\n                    signal: op.signal,\n                    headers () {\n                        if (!opts.headers) {\n                            return {};\n                        }\n                        if (typeof opts.headers === 'function') {\n                            return opts.headers({\n                                op\n                            });\n                        }\n                        return opts.headers;\n                    }\n                });\n                let meta = undefined;\n                request.then((res)=>{\n                    meta = res.meta;\n                    const transformed = transformResult(res.json, resolvedOpts.transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(TRPCClientError.from(transformed.error, {\n                            meta\n                        }));\n                        return;\n                    }\n                    observer.next({\n                        context: res.meta,\n                        result: transformed.result\n                    });\n                    observer.complete();\n                }).catch((cause)=>{\n                    observer.error(TRPCClientError.from(cause, {\n                        meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n\nexport { httpLink };\n", "import { observable, tap } from '@trpc/server/observable';\n\n/// <reference lib=\"dom.iterable\" />\n// `dom.iterable` types are explicitly required for extracting `FormData` values,\n// as all implementations of `Symbol.iterable` are separated from the main `dom` types.\n// Using triple-slash directive makes sure that it will be available,\n// even if end-user `tsconfig.json` omits it in the `lib` array.\nfunction isFormData(value) {\n    if (typeof FormData === 'undefined') {\n        // FormData is not supported\n        return false;\n    }\n    return value instanceof FormData;\n}\nconst palettes = {\n    css: {\n        query: [\n            '72e3ff',\n            '3fb0d8'\n        ],\n        mutation: [\n            'c5a3fc',\n            '904dfc'\n        ],\n        subscription: [\n            'ff49e1',\n            'd83fbe'\n        ]\n    },\n    ansi: {\n        regular: {\n            // Cyan background, black and white text respectively\n            query: [\n                '\\x1b[30;46m',\n                '\\x1b[97;46m'\n            ],\n            // Magenta background, black and white text respectively\n            mutation: [\n                '\\x1b[30;45m',\n                '\\x1b[97;45m'\n            ],\n            // Green background, black and white text respectively\n            subscription: [\n                '\\x1b[30;42m',\n                '\\x1b[97;42m'\n            ]\n        },\n        bold: {\n            query: [\n                '\\x1b[1;30;46m',\n                '\\x1b[1;97;46m'\n            ],\n            mutation: [\n                '\\x1b[1;30;45m',\n                '\\x1b[1;97;45m'\n            ],\n            subscription: [\n                '\\x1b[1;30;42m',\n                '\\x1b[1;97;42m'\n            ]\n        }\n    }\n};\nfunction constructPartsAndArgs(opts) {\n    const { direction, type, withContext, path, id, input } = opts;\n    const parts = [];\n    const args = [];\n    if (opts.colorMode === 'none') {\n        parts.push(direction === 'up' ? '>>' : '<<', type, `#${id}`, path);\n    } else if (opts.colorMode === 'ansi') {\n        const [lightRegular, darkRegular] = palettes.ansi.regular[type];\n        const [lightBold, darkBold] = palettes.ansi.bold[type];\n        const reset = '\\x1b[0m';\n        parts.push(direction === 'up' ? lightRegular : darkRegular, direction === 'up' ? '>>' : '<<', type, direction === 'up' ? lightBold : darkBold, `#${id}`, path, reset);\n    } else {\n        // css color mode\n        const [light, dark] = palettes.css[type];\n        const css = `\n    background-color: #${direction === 'up' ? light : dark};\n    color: ${direction === 'up' ? 'black' : 'white'};\n    padding: 2px;\n  `;\n        parts.push('%c', direction === 'up' ? '>>' : '<<', type, `#${id}`, `%c${path}%c`, '%O');\n        args.push(css, `${css}; font-weight: bold;`, `${css}; font-weight: normal;`);\n    }\n    if (direction === 'up') {\n        args.push(withContext ? {\n            input,\n            context: opts.context\n        } : {\n            input\n        });\n    } else {\n        args.push({\n            input,\n            result: opts.result,\n            elapsedMs: opts.elapsedMs,\n            ...withContext && {\n                context: opts.context\n            }\n        });\n    }\n    return {\n        parts,\n        args\n    };\n}\n// maybe this should be moved to it's own package\nconst defaultLogger = ({ c = console, colorMode = 'css', withContext })=>(props)=>{\n        const rawInput = props.input;\n        const input = isFormData(rawInput) ? Object.fromEntries(rawInput) : rawInput;\n        const { parts, args } = constructPartsAndArgs({\n            ...props,\n            colorMode,\n            input,\n            withContext\n        });\n        const fn = props.direction === 'down' && props.result && (props.result instanceof Error || 'error' in props.result.result && props.result.result.error) ? 'error' : 'log';\n        c[fn].apply(null, [\n            parts.join(' ')\n        ].concat(args));\n    };\n/**\n * @see https://trpc.io/docs/v11/client/links/loggerLink\n */ function loggerLink(opts = {}) {\n    const { enabled = ()=>true } = opts;\n    const colorMode = opts.colorMode ?? (typeof window === 'undefined' ? 'ansi' : 'css');\n    const withContext = opts.withContext ?? colorMode === 'css';\n    const { logger = defaultLogger({\n        c: opts.console,\n        colorMode,\n        withContext\n    }) } = opts;\n    return ()=>{\n        return ({ op, next })=>{\n            return observable((observer)=>{\n                // ->\n                if (enabled({\n                    ...op,\n                    direction: 'up'\n                })) {\n                    logger({\n                        ...op,\n                        direction: 'up'\n                    });\n                }\n                const requestStartTime = Date.now();\n                function logResult(result) {\n                    const elapsedMs = Date.now() - requestStartTime;\n                    if (enabled({\n                        ...op,\n                        direction: 'down',\n                        result\n                    })) {\n                        logger({\n                            ...op,\n                            direction: 'down',\n                            elapsedMs,\n                            result\n                        });\n                    }\n                }\n                return next(op).pipe(tap({\n                    next (result) {\n                        logResult(result);\n                    },\n                    error (result) {\n                        logResult(result);\n                    }\n                })).subscribe(observer);\n            });\n        };\n    };\n}\n\nexport { loggerLink };\n", "import { observable } from '@trpc/server/observable';\nimport { create<PERSON>hain } from './internals/createChain.mjs';\n\nfunction asArray(value) {\n    return Array.isArray(value) ? value : [\n        value\n    ];\n}\nfunction splitLink(opts) {\n    return (runtime)=>{\n        const yes = asArray(opts.true).map((link)=>link(runtime));\n        const no = asArray(opts.false).map((link)=>link(runtime));\n        return (props)=>{\n            return observable((observer)=>{\n                const links = opts.condition(props.op) ? yes : no;\n                return createChain({\n                    op: props.op,\n                    links\n                }).subscribe(observer);\n            });\n        };\n    };\n}\n\nexport { splitLink };\n", "const lazyDefaults = {\n    enabled: false,\n    closeMs: 0\n};\nconst keepAliveDefaults = {\n    enabled: false,\n    pongTimeoutMs: 1000,\n    intervalMs: 5000\n};\n/**\n * Calculates a delay for exponential backoff based on the retry attempt index.\n * The delay starts at 0 for the first attempt and doubles for each subsequent attempt,\n * capped at 30 seconds.\n */ const exponentialBackoff = (attemptIndex)=>{\n    return attemptIndex === 0 ? 0 : Math.min(1000 * 2 ** attemptIndex, 30000);\n};\n\nexport { exponentialBackoff, keepAliveDefaults, lazyDefaults };\n", "/**\n * Get the result of a value or function that returns a value\n * It also optionally accepts typesafe arguments for the function\n */ const resultOf = (value, ...args)=>{\n    return typeof value === 'function' ? value(...args) : value;\n};\n\nexport { resultOf };\n", "import { resultOf } from '../../internals/urlWithConnectionParams.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nclass TRPCWebSocketClosedError extends Error {\n    constructor(opts){\n        super(opts.message, {\n            cause: opts.cause\n        });\n        this.name = 'TRPCWebSocketClosedError';\n        Object.setPrototypeOf(this, TRPCWebSocketClosedError.prototype);\n    }\n}\n/**\n * Utility class for managing a timeout that can be started, stopped, and reset.\n * Useful for scenarios where the timeout duration is reset dynamically based on events.\n */ class ResettableTimeout {\n    /**\n   * Resets the current timeout, restarting it with the same duration.\n   * Does nothing if no timeout is active.\n   */ reset() {\n        if (!this.timeout) return;\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n    }\n    start() {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n    }\n    stop() {\n        clearTimeout(this.timeout);\n        this.timeout = undefined;\n    }\n    constructor(onTimeout, timeoutMs){\n        _define_property(this, \"onTimeout\", void 0);\n        _define_property(this, \"timeoutMs\", void 0);\n        _define_property(this, \"timeout\", void 0);\n        this.onTimeout = onTimeout;\n        this.timeoutMs = timeoutMs;\n    }\n}\n// Ponyfill for Promise.withResolvers https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\nfunction withResolvers() {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return {\n        promise,\n        resolve: resolve,\n        reject: reject\n    };\n}\n/**\n * Resolves a WebSocket URL and optionally appends connection parameters.\n *\n * If connectionParams are provided, appends 'connectionParams=1' query parameter.\n */ async function prepareUrl(urlOptions) {\n    const url = await resultOf(urlOptions.url);\n    if (!urlOptions.connectionParams) return url;\n    // append `?connectionParams=1` when connection params are used\n    const prefix = url.includes('?') ? '&' : '?';\n    const connectionParams = `${prefix}connectionParams=1`;\n    return url + connectionParams;\n}\nasync function buildConnectionMessage(connectionParams) {\n    const message = {\n        method: 'connectionParams',\n        data: await resultOf(connectionParams)\n    };\n    return JSON.stringify(message);\n}\n\nexport { ResettableTimeout, TRPCWebSocketClosedError, buildConnectionMessage, prepareUrl, withResolvers };\n", "import { withResolvers } from './utils.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * Manages WebSocket requests, tracking their lifecycle and providing utility methods\n * for handling outgoing and pending requests.\n *\n * - **Outgoing requests**: Requests that are queued and waiting to be sent.\n * - **Pending requests**: Requests that have been sent and are in flight awaiting a response.\n *   For subscriptions, multiple responses may be received until the subscription is closed.\n */ class RequestManager {\n    /**\n   * Registers a new request by adding it to the outgoing queue and setting up\n   * callbacks for lifecycle events such as completion or error.\n   *\n   * @param message - The outgoing message to be sent.\n   * @param callbacks - Callback functions to observe the request's state.\n   * @returns A cleanup function to manually remove the request.\n   */ register(message, callbacks) {\n        const { promise: end, resolve } = withResolvers();\n        this.outgoingRequests.push({\n            id: String(message.id),\n            message,\n            end,\n            callbacks: {\n                next: callbacks.next,\n                complete: ()=>{\n                    callbacks.complete();\n                    resolve();\n                },\n                error: (e)=>{\n                    callbacks.error(e);\n                    resolve();\n                }\n            }\n        });\n        return ()=>{\n            this.delete(message.id);\n            callbacks.complete();\n            resolve();\n        };\n    }\n    /**\n   * Deletes a request from both the outgoing and pending collections, if it exists.\n   */ delete(messageId) {\n        if (messageId === null) return;\n        this.outgoingRequests = this.outgoingRequests.filter(({ id })=>id !== String(messageId));\n        delete this.pendingRequests[String(messageId)];\n    }\n    /**\n   * Moves all outgoing requests to the pending state and clears the outgoing queue.\n   *\n   * The caller is expected to handle the actual sending of the requests\n   * (e.g., sending them over the network) after this method is called.\n   *\n   * @returns The list of requests that were transitioned to the pending state.\n   */ flush() {\n        const requests = this.outgoingRequests;\n        this.outgoingRequests = [];\n        for (const request of requests){\n            this.pendingRequests[request.id] = request;\n        }\n        return requests;\n    }\n    /**\n   * Retrieves all currently pending requests, which are in flight awaiting responses\n   * or handling ongoing subscriptions.\n   */ getPendingRequests() {\n        return Object.values(this.pendingRequests);\n    }\n    /**\n   * Retrieves a specific pending request by its message ID.\n   */ getPendingRequest(messageId) {\n        if (messageId === null) return null;\n        return this.pendingRequests[String(messageId)];\n    }\n    /**\n   * Retrieves all outgoing requests, which are waiting to be sent.\n   */ getOutgoingRequests() {\n        return this.outgoingRequests;\n    }\n    /**\n   * Retrieves all requests, both outgoing and pending, with their respective states.\n   *\n   * @returns An array of all requests with their state (\"outgoing\" or \"pending\").\n   */ getRequests() {\n        return [\n            ...this.getOutgoingRequests().map((request)=>({\n                    state: 'outgoing',\n                    message: request.message,\n                    end: request.end,\n                    callbacks: request.callbacks\n                })),\n            ...this.getPendingRequests().map((request)=>({\n                    state: 'pending',\n                    message: request.message,\n                    end: request.end,\n                    callbacks: request.callbacks\n                }))\n        ];\n    }\n    /**\n   * Checks if there are any pending requests, including ongoing subscriptions.\n   */ hasPendingRequests() {\n        return this.getPendingRequests().length > 0;\n    }\n    /**\n   * Checks if there are any pending subscriptions\n   */ hasPendingSubscriptions() {\n        return this.getPendingRequests().some((request)=>request.message.method === 'subscription');\n    }\n    /**\n   * Checks if there are any outgoing requests waiting to be sent.\n   */ hasOutgoingRequests() {\n        return this.outgoingRequests.length > 0;\n    }\n    constructor(){\n        /**\n   * Stores requests that are outgoing, meaning they are registered but not yet sent over the WebSocket.\n   */ _define_property(this, \"outgoingRequests\", new Array());\n        /**\n   * Stores requests that are pending (in flight), meaning they have been sent over the WebSocket\n   * and are awaiting responses. For subscriptions, this includes requests\n   * that may receive multiple responses.\n   */ _define_property(this, \"pendingRequests\", {});\n    }\n}\n\nexport { RequestManager };\n", "import { behaviorSubject } from '@trpc/server/observable';\nimport { prepareUrl, buildConnectionMessage, withResolvers } from './utils.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * Opens a WebSocket connection asynchronously and returns a promise\n * that resolves when the connection is successfully established.\n * The promise rejects if an error occurs during the connection attempt.\n */ function asyncWsOpen(ws) {\n    const { promise, resolve, reject } = withResolvers();\n    ws.addEventListener('open', ()=>{\n        ws.removeEventListener('error', reject);\n        resolve();\n    });\n    ws.addEventListener('error', reject);\n    return promise;\n}\n/**\n * Sets up a periodic ping-pong mechanism to keep the WebSocket connection alive.\n *\n * - Sends \"PING\" messages at regular intervals defined by `intervalMs`.\n * - If a \"PONG\" response is not received within the `pongTimeoutMs`, the WebSocket is closed.\n * - The ping timer resets upon receiving any message to maintain activity.\n * - Automatically starts the ping process when the WebSocket connection is opened.\n * - Cleans up timers when the WebSocket is closed.\n *\n * @param ws - The WebSocket instance to manage.\n * @param options - Configuration options for ping-pong intervals and timeouts.\n */ function setupPingInterval(ws, { intervalMs, pongTimeoutMs }) {\n    let pingTimeout;\n    let pongTimeout;\n    function start() {\n        pingTimeout = setTimeout(()=>{\n            ws.send('PING');\n            pongTimeout = setTimeout(()=>{\n                ws.close();\n            }, pongTimeoutMs);\n        }, intervalMs);\n    }\n    function reset() {\n        clearTimeout(pingTimeout);\n        start();\n    }\n    function pong() {\n        clearTimeout(pongTimeout);\n        reset();\n    }\n    ws.addEventListener('open', start);\n    ws.addEventListener('message', ({ data })=>{\n        clearTimeout(pingTimeout);\n        start();\n        if (data === 'PONG') {\n            pong();\n        }\n    });\n    ws.addEventListener('close', ()=>{\n        clearTimeout(pingTimeout);\n        clearTimeout(pongTimeout);\n    });\n}\n/**\n * Manages a WebSocket connection with support for reconnection, keep-alive mechanisms,\n * and observable state tracking.\n */ class WsConnection {\n    get ws() {\n        return this.wsObservable.get();\n    }\n    set ws(ws) {\n        this.wsObservable.next(ws);\n    }\n    /**\n   * Checks if the WebSocket connection is open and ready to communicate.\n   */ isOpen() {\n        return !!this.ws && this.ws.readyState === this.WebSocketPonyfill.OPEN && !this.openPromise;\n    }\n    /**\n   * Checks if the WebSocket connection is closed or in the process of closing.\n   */ isClosed() {\n        return !!this.ws && (this.ws.readyState === this.WebSocketPonyfill.CLOSING || this.ws.readyState === this.WebSocketPonyfill.CLOSED);\n    }\n    async open() {\n        if (this.openPromise) return this.openPromise;\n        this.id = ++WsConnection.connectCount;\n        const wsPromise = prepareUrl(this.urlOptions).then((url)=>new this.WebSocketPonyfill(url));\n        this.openPromise = wsPromise.then(async (ws)=>{\n            this.ws = ws;\n            // Setup ping listener\n            ws.addEventListener('message', function({ data }) {\n                if (data === 'PING') {\n                    this.send('PONG');\n                }\n            });\n            if (this.keepAliveOpts.enabled) {\n                setupPingInterval(ws, this.keepAliveOpts);\n            }\n            ws.addEventListener('close', ()=>{\n                if (this.ws === ws) {\n                    this.ws = null;\n                }\n            });\n            await asyncWsOpen(ws);\n            if (this.urlOptions.connectionParams) {\n                ws.send(await buildConnectionMessage(this.urlOptions.connectionParams));\n            }\n        });\n        try {\n            await this.openPromise;\n        } finally{\n            this.openPromise = null;\n        }\n    }\n    /**\n   * Closes the WebSocket connection gracefully.\n   * Waits for any ongoing open operation to complete before closing.\n   */ async close() {\n        try {\n            await this.openPromise;\n        } finally{\n            this.ws?.close();\n        }\n    }\n    constructor(opts){\n        _define_property(this, \"id\", ++WsConnection.connectCount);\n        _define_property(this, \"WebSocketPonyfill\", void 0);\n        _define_property(this, \"urlOptions\", void 0);\n        _define_property(this, \"keepAliveOpts\", void 0);\n        _define_property(this, \"wsObservable\", behaviorSubject(null));\n        /**\n   * Manages the WebSocket opening process, ensuring that only one open operation\n   * occurs at a time. Tracks the ongoing operation with `openPromise` to avoid\n   * redundant calls and ensure proper synchronization.\n   *\n   * Sets up the keep-alive mechanism and necessary event listeners for the connection.\n   *\n   * @returns A promise that resolves once the WebSocket connection is successfully opened.\n   */ _define_property(this, \"openPromise\", null);\n        this.WebSocketPonyfill = opts.WebSocketPonyfill ?? WebSocket;\n        if (!this.WebSocketPonyfill) {\n            throw new Error(\"No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill\");\n        }\n        this.urlOptions = opts.urlOptions;\n        this.keepAliveOpts = opts.keepAlive;\n    }\n}\n_define_property(WsConnection, \"connectCount\", 0);\n/**\n * Provides a backward-compatible representation of the connection state.\n */ function backwardCompatibility(connection) {\n    if (connection.isOpen()) {\n        return {\n            id: connection.id,\n            state: 'open',\n            ws: connection.ws\n        };\n    }\n    if (connection.isClosed()) {\n        return {\n            id: connection.id,\n            state: 'closed',\n            ws: connection.ws\n        };\n    }\n    if (!connection.ws) {\n        return null;\n    }\n    return {\n        id: connection.id,\n        state: 'connecting',\n        ws: connection.ws\n    };\n}\n\nexport { WsConnection, backwardCompatibility };\n", "import { observable, behaviorSubject } from '@trpc/server/observable';\nimport { transformResult, run, sleep } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../../../TRPCClientError.mjs';\nimport { lazyDefaults, keepAliveDefaults, exponentialBackoff } from './options.mjs';\nimport { RequestManager } from './requestManager.mjs';\nimport { TRPCWebSocketClosedError, ResettableTimeout } from './utils.mjs';\nimport { backwardCompatibility, WsConnection } from './wsConnection.mjs';\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * A WebSocket client for managing TRPC operations, supporting lazy initialization,\n * reconnection, keep-alive, and request management.\n */ class WsClient {\n    /**\n   * Opens the WebSocket connection. Handles reconnection attempts and updates\n   * the connection state accordingly.\n   */ async open() {\n        this.allowReconnect = true;\n        if (this.connectionState.get().state !== 'connecting') {\n            this.connectionState.next({\n                type: 'state',\n                state: 'connecting',\n                error: null\n            });\n        }\n        try {\n            await this.activeConnection.open();\n        } catch (error) {\n            this.reconnect(new TRPCWebSocketClosedError({\n                message: 'Initialization error',\n                cause: error\n            }));\n            return this.reconnecting;\n        }\n    }\n    /**\n   * Closes the WebSocket connection and stops managing requests.\n   * Ensures all outgoing and pending requests are properly finalized.\n   */ async close() {\n        this.allowReconnect = false;\n        this.inactivityTimeout.stop();\n        const requestsToAwait = [];\n        for (const request of this.requestManager.getRequests()){\n            if (request.message.method === 'subscription') {\n                request.callbacks.complete();\n            } else if (request.state === 'outgoing') {\n                request.callbacks.error(TRPCClientError.from(new TRPCWebSocketClosedError({\n                    message: 'Closed before connection was established'\n                })));\n            } else {\n                requestsToAwait.push(request.end);\n            }\n        }\n        await Promise.all(requestsToAwait).catch(()=>null);\n        await this.activeConnection.close().catch(()=>null);\n        this.connectionState.next({\n            type: 'state',\n            state: 'idle',\n            error: null\n        });\n    }\n    /**\n   * Method to request the server.\n   * Handles data transformation, batching of requests, and subscription lifecycle.\n   *\n   * @param op - The operation details including id, type, path, input and signal\n   * @param transformer - Data transformer for serializing requests and deserializing responses\n   * @param lastEventId - Optional ID of the last received event for subscriptions\n   *\n   * @returns An observable that emits operation results and handles cleanup\n   */ request({ op: { id, type, path, input, signal }, transformer, lastEventId }) {\n        return observable((observer)=>{\n            const abort = this.batchSend({\n                id,\n                method: type,\n                params: {\n                    input: transformer.input.serialize(input),\n                    path,\n                    lastEventId\n                }\n            }, {\n                ...observer,\n                next (event) {\n                    const transformed = transformResult(event, transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(TRPCClientError.from(transformed.error));\n                        return;\n                    }\n                    observer.next({\n                        result: transformed.result\n                    });\n                }\n            });\n            return ()=>{\n                abort();\n                if (type === 'subscription' && this.activeConnection.isOpen()) {\n                    this.send({\n                        id,\n                        method: 'subscription.stop'\n                    });\n                }\n                signal?.removeEventListener('abort', abort);\n            };\n        });\n    }\n    get connection() {\n        return backwardCompatibility(this.activeConnection);\n    }\n    reconnect(closedError) {\n        this.connectionState.next({\n            type: 'state',\n            state: 'connecting',\n            error: TRPCClientError.from(closedError)\n        });\n        if (this.reconnecting) return;\n        const tryReconnect = async (attemptIndex)=>{\n            try {\n                await sleep(this.reconnectRetryDelay(attemptIndex));\n                if (this.allowReconnect) {\n                    await this.activeConnection.close();\n                    await this.activeConnection.open();\n                    if (this.requestManager.hasPendingRequests()) {\n                        this.send(this.requestManager.getPendingRequests().map(({ message })=>message));\n                    }\n                }\n                this.reconnecting = null;\n            } catch  {\n                await tryReconnect(attemptIndex + 1);\n            }\n        };\n        this.reconnecting = tryReconnect(0);\n    }\n    setupWebSocketListeners(ws) {\n        const handleCloseOrError = (cause)=>{\n            const reqs = this.requestManager.getPendingRequests();\n            for (const { message, callbacks } of reqs){\n                if (message.method === 'subscription') continue;\n                callbacks.error(TRPCClientError.from(cause ?? new TRPCWebSocketClosedError({\n                    message: 'WebSocket closed',\n                    cause\n                })));\n                this.requestManager.delete(message.id);\n            }\n        };\n        ws.addEventListener('open', ()=>{\n            run(async ()=>{\n                if (this.lazyMode) {\n                    this.inactivityTimeout.start();\n                }\n                this.callbacks.onOpen?.();\n                this.connectionState.next({\n                    type: 'state',\n                    state: 'pending',\n                    error: null\n                });\n            }).catch((error)=>{\n                ws.close(3000);\n                handleCloseOrError(error);\n            });\n        });\n        ws.addEventListener('message', ({ data })=>{\n            this.inactivityTimeout.reset();\n            if (typeof data !== 'string' || [\n                'PING',\n                'PONG'\n            ].includes(data)) return;\n            const incomingMessage = JSON.parse(data);\n            if ('method' in incomingMessage) {\n                this.handleIncomingRequest(incomingMessage);\n                return;\n            }\n            this.handleResponseMessage(incomingMessage);\n        });\n        ws.addEventListener('close', (event)=>{\n            handleCloseOrError(event);\n            this.callbacks.onClose?.(event);\n            if (!this.lazyMode || this.requestManager.hasPendingSubscriptions()) {\n                this.reconnect(new TRPCWebSocketClosedError({\n                    message: 'WebSocket closed',\n                    cause: event\n                }));\n            }\n        });\n        ws.addEventListener('error', (event)=>{\n            handleCloseOrError(event);\n            this.callbacks.onError?.(event);\n            this.reconnect(new TRPCWebSocketClosedError({\n                message: 'WebSocket closed',\n                cause: event\n            }));\n        });\n    }\n    handleResponseMessage(message) {\n        const request = this.requestManager.getPendingRequest(message.id);\n        if (!request) return;\n        request.callbacks.next(message);\n        let completed = true;\n        if ('result' in message && request.message.method === 'subscription') {\n            if (message.result.type === 'data') {\n                request.message.params.lastEventId = message.result.id;\n            }\n            if (message.result.type !== 'stopped') {\n                completed = false;\n            }\n        }\n        if (completed) {\n            request.callbacks.complete();\n            this.requestManager.delete(message.id);\n        }\n    }\n    handleIncomingRequest(message) {\n        if (message.method === 'reconnect') {\n            this.reconnect(new TRPCWebSocketClosedError({\n                message: 'Server requested reconnect'\n            }));\n        }\n    }\n    /**\n   * Sends a message or batch of messages directly to the server.\n   */ send(messageOrMessages) {\n        if (!this.activeConnection.isOpen()) {\n            throw new Error('Active connection is not open');\n        }\n        const messages = messageOrMessages instanceof Array ? messageOrMessages : [\n            messageOrMessages\n        ];\n        this.activeConnection.ws.send(JSON.stringify(messages.length === 1 ? messages[0] : messages));\n    }\n    /**\n   * Groups requests for batch sending.\n   *\n   * @returns A function to abort the batched request.\n   */ batchSend(message, callbacks) {\n        this.inactivityTimeout.reset();\n        run(async ()=>{\n            if (!this.activeConnection.isOpen()) {\n                await this.open();\n            }\n            await sleep(0);\n            if (!this.requestManager.hasOutgoingRequests()) return;\n            this.send(this.requestManager.flush().map(({ message })=>message));\n        }).catch((err)=>{\n            this.requestManager.delete(message.id);\n            callbacks.error(TRPCClientError.from(err));\n        });\n        return this.requestManager.register(message, callbacks);\n    }\n    constructor(opts){\n        /**\n   * Observable tracking the current connection state, including errors.\n   */ _define_property(this, \"connectionState\", void 0);\n        _define_property(this, \"allowReconnect\", false);\n        _define_property(this, \"requestManager\", new RequestManager());\n        _define_property(this, \"activeConnection\", void 0);\n        _define_property(this, \"reconnectRetryDelay\", void 0);\n        _define_property(this, \"inactivityTimeout\", void 0);\n        _define_property(this, \"callbacks\", void 0);\n        _define_property(this, \"lazyMode\", void 0);\n        /**\n   * Manages the reconnection process for the WebSocket using retry logic.\n   * Ensures that only one reconnection attempt is active at a time by tracking the current\n   * reconnection state in the `reconnecting` promise.\n   */ _define_property(this, \"reconnecting\", null);\n        // Initialize callbacks, connection parameters, and options.\n        this.callbacks = {\n            onOpen: opts.onOpen,\n            onClose: opts.onClose,\n            onError: opts.onError\n        };\n        const lazyOptions = {\n            ...lazyDefaults,\n            ...opts.lazy\n        };\n        // Set up inactivity timeout for lazy connections.\n        this.inactivityTimeout = new ResettableTimeout(()=>{\n            if (this.requestManager.hasOutgoingRequests() || this.requestManager.hasPendingRequests()) {\n                this.inactivityTimeout.reset();\n                return;\n            }\n            this.close().catch(()=>null);\n        }, lazyOptions.closeMs);\n        // Initialize the WebSocket connection.\n        this.activeConnection = new WsConnection({\n            WebSocketPonyfill: opts.WebSocket,\n            urlOptions: opts,\n            keepAlive: {\n                ...keepAliveDefaults,\n                ...opts.keepAlive\n            }\n        });\n        this.activeConnection.wsObservable.subscribe({\n            next: (ws)=>{\n                if (!ws) return;\n                this.setupWebSocketListeners(ws);\n            }\n        });\n        this.reconnectRetryDelay = opts.retryDelayMs ?? exponentialBackoff;\n        this.lazyMode = lazyOptions.enabled;\n        this.connectionState = behaviorSubject({\n            type: 'state',\n            state: lazyOptions.enabled ? 'idle' : 'connecting',\n            error: null\n        });\n        // Automatically open the connection if lazy mode is disabled.\n        if (!this.lazyMode) {\n            this.open().catch(()=>null);\n        }\n    }\n}\n\nexport { WsClient };\n", "import { WsClient } from './wsClient/wsClient.mjs';\n\nfunction createWSClient(opts) {\n    return new WsClient(opts);\n}\n\nexport { createWSClient };\n", "import { observable } from '@trpc/server/observable';\nimport { getTransformer } from '../../internals/transformer.mjs';\nexport { createWSClient } from './createWsClient.mjs';\n\nfunction wsLink(opts) {\n    const { client } = opts;\n    const transformer = getTransformer(opts.transformer);\n    return ()=>{\n        return ({ op })=>{\n            return observable((observer)=>{\n                const connStateSubscription = op.type === 'subscription' ? client.connectionState.subscribe({\n                    next (result) {\n                        observer.next({\n                            result,\n                            context: op.context\n                        });\n                    }\n                }) : null;\n                const requestSubscription = client.request({\n                    op,\n                    transformer\n                }).subscribe(observer);\n                return ()=>{\n                    requestSubscription.unsubscribe();\n                    connStateSubscription?.unsubscribe();\n                };\n            });\n        };\n    };\n}\n\nexport { wsLink };\n", "function inputWithTrackedEventId(input, lastEventId) {\n    if (!lastEventId) {\n        return input;\n    }\n    if (input != null && typeof input !== 'object') {\n        return input;\n    }\n    return {\n        ...input ?? {},\n        lastEventId\n    };\n}\n\nexport { inputWithTrackedEventId };\n", "import { observable, behaviorSubject } from '@trpc/server/observable';\nimport { TRPC_ERROR_CODES_BY_KEY } from '@trpc/server/rpc';\nimport { sseStreamConsumer, run } from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId.mjs';\nimport { raceAbortSignals } from '../internals/signals.mjs';\nimport { TRPCClientError } from '../TRPCClientError.mjs';\nimport { getTransformer } from '../internals/transformer.mjs';\nimport { getUrl } from './internals/httpUtils.mjs';\nimport { resultOf } from './internals/urlWithConnectionParams.mjs';\n\nasync function urlWithConnectionParams(opts) {\n    let url = await resultOf(opts.url);\n    if (opts.connectionParams) {\n        const params = await resultOf(opts.connectionParams);\n        const prefix = url.includes('?') ? '&' : '?';\n        url += prefix + 'connectionParams=' + encodeURIComponent(JSON.stringify(params));\n    }\n    return url;\n}\n/**\n * tRPC error codes that are considered retryable\n * With out of the box SSE, the client will reconnect when these errors are encountered\n */ const codes5xx = [\n    TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,\n    TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,\n    TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,\n    TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR\n];\n/**\n * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n */ function httpSubscriptionLink(opts) {\n    const transformer = getTransformer(opts.transformer);\n    return ()=>{\n        return ({ op })=>{\n            return observable((observer)=>{\n                const { type, path, input } = op;\n                /* istanbul ignore if -- @preserve */ if (type !== 'subscription') {\n                    throw new Error('httpSubscriptionLink only supports subscriptions');\n                }\n                let lastEventId = undefined;\n                const ac = new AbortController();\n                const signal = raceAbortSignals(op.signal, ac.signal);\n                const eventSourceStream = sseStreamConsumer({\n                    url: async ()=>getUrl({\n                            transformer,\n                            url: await urlWithConnectionParams(opts),\n                            input: inputWithTrackedEventId(input, lastEventId),\n                            path,\n                            type,\n                            signal: null\n                        }),\n                    init: ()=>resultOf(opts.eventSourceOptions, {\n                            op\n                        }),\n                    signal,\n                    deserialize: transformer.output.deserialize,\n                    EventSource: opts.EventSource ?? globalThis.EventSource\n                });\n                const connectionState = behaviorSubject({\n                    type: 'state',\n                    state: 'connecting',\n                    error: null\n                });\n                const connectionSub = connectionState.subscribe({\n                    next (state) {\n                        observer.next({\n                            result: state\n                        });\n                    }\n                });\n                run(async ()=>{\n                    for await (const chunk of eventSourceStream){\n                        switch(chunk.type){\n                            case 'ping':\n                                break;\n                            case 'data':\n                                const chunkData = chunk.data;\n                                let result;\n                                if (chunkData.id) {\n                                    // if the `tracked()`-helper is used, we always have an `id` field\n                                    lastEventId = chunkData.id;\n                                    result = {\n                                        id: chunkData.id,\n                                        data: chunkData\n                                    };\n                                } else {\n                                    result = {\n                                        data: chunkData.data\n                                    };\n                                }\n                                observer.next({\n                                    result,\n                                    context: {\n                                        eventSource: chunk.eventSource\n                                    }\n                                });\n                                break;\n                            case 'connected':\n                                {\n                                    observer.next({\n                                        result: {\n                                            type: 'started'\n                                        },\n                                        context: {\n                                            eventSource: chunk.eventSource\n                                        }\n                                    });\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'pending',\n                                        error: null\n                                    });\n                                    break;\n                                }\n                            case 'serialized-error':\n                                {\n                                    const error = TRPCClientError.from({\n                                        error: chunk.error\n                                    });\n                                    if (codes5xx.includes(chunk.error.code)) {\n                                        //\n                                        connectionState.next({\n                                            type: 'state',\n                                            state: 'connecting',\n                                            error\n                                        });\n                                        break;\n                                    }\n                                    //\n                                    // non-retryable error, cancel the subscription\n                                    throw error;\n                                }\n                            case 'connecting':\n                                {\n                                    const lastState = connectionState.get();\n                                    const error = chunk.event && TRPCClientError.from(chunk.event);\n                                    if (!error && lastState.state === 'connecting') {\n                                        break;\n                                    }\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'connecting',\n                                        error\n                                    });\n                                    break;\n                                }\n                            case 'timeout':\n                                {\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'connecting',\n                                        error: new TRPCClientError(`Timeout of ${chunk.ms}ms reached while waiting for a response`)\n                                    });\n                                }\n                        }\n                    }\n                    observer.next({\n                        result: {\n                            type: 'stopped'\n                        }\n                    });\n                    connectionState.next({\n                        type: 'state',\n                        state: 'idle',\n                        error: null\n                    });\n                    observer.complete();\n                }).catch((error)=>{\n                    observer.error(TRPCClientError.from(error));\n                });\n                return ()=>{\n                    observer.complete();\n                    ac.abort();\n                    connectionSub.unsubscribe();\n                };\n            });\n        };\n    };\n}\n/**\n * @deprecated use {@link httpSubscriptionLink} instead\n */ const unstable_httpSubscriptionLink = httpSubscriptionLink;\n\nexport { httpSubscriptionLink, unstable_httpSubscriptionLink };\n", "import { observable } from '@trpc/server/observable';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId.mjs';\n\n/* istanbul ignore file -- @preserve */ // We're not actually exporting this link\n/**\n * @see https://trpc.io/docs/v11/client/links/retryLink\n */ function retryLink(opts) {\n    // initialized config\n    return ()=>{\n        // initialized in app\n        return (callOpts)=>{\n            // initialized for request\n            return observable((observer)=>{\n                let next$;\n                let callNextTimeout = undefined;\n                let lastEventId = undefined;\n                attempt(1);\n                function opWithLastEventId() {\n                    const op = callOpts.op;\n                    if (!lastEventId) {\n                        return op;\n                    }\n                    return {\n                        ...op,\n                        input: inputWithTrackedEventId(op.input, lastEventId)\n                    };\n                }\n                function attempt(attempts) {\n                    const op = opWithLastEventId();\n                    next$ = callOpts.next(op).subscribe({\n                        error (error) {\n                            const shouldRetry = opts.retry({\n                                op,\n                                attempts,\n                                error\n                            });\n                            if (!shouldRetry) {\n                                observer.error(error);\n                                return;\n                            }\n                            const delayMs = opts.retryDelayMs?.(attempts) ?? 0;\n                            if (delayMs <= 0) {\n                                attempt(attempts + 1);\n                                return;\n                            }\n                            callNextTimeout = setTimeout(()=>attempt(attempts + 1), delayMs);\n                        },\n                        next (envelope) {\n                            //\n                            if ((!envelope.result.type || envelope.result.type === 'data') && envelope.result.id) {\n                                //\n                                lastEventId = envelope.result.id;\n                            }\n                            observer.next(envelope);\n                        },\n                        complete () {\n                            observer.complete();\n                        }\n                    });\n                }\n                return ()=>{\n                    next$.unsubscribe();\n                    clearTimeout(callNextTimeout);\n                };\n            });\n        };\n    };\n}\n\nexport { retryLink };\n"], "mappings": ";AAGe,SAAS,WAAW,WAAW;AAC1C,QAAM,OAAO;AAAA,IACT,UAAW,UAAU;AACjB,UAAI,cAAc;AAClB,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,sBAAsB;AAC1B,eAAS,cAAc;AACnB,YAAI,gBAAgB,MAAM;AACtB,gCAAsB;AACtB;AAAA,QACJ;AACA,YAAI,cAAc;AACd;AAAA,QACJ;AACA,uBAAe;AACf,YAAI,OAAO,gBAAgB,YAAY;AACnC,sBAAY;AAAA,QAChB,WAAW,aAAa;AACpB,sBAAY,YAAY;AAAA,QAC5B;AAAA,MACJ;AACA,oBAAc,UAAU;AAAA,QACpB,KAAM,OAAO;AA1B7B,cAAAA;AA2BoB,cAAI,QAAQ;AACR;AAAA,UACJ;AACA,WAAAA,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;AAAA,QACpB;AAAA,QACA,MAAO,KAAK;AAhC5B,cAAAA;AAiCoB,cAAI,QAAQ;AACR;AAAA,UACJ;AACA,mBAAS;AACT,WAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;AACjB,sBAAY;AAAA,QAChB;AAAA,QACA,WAAY;AAxC5B,cAAAA;AAyCoB,cAAI,QAAQ;AACR;AAAA,UACJ;AACA,mBAAS;AACT,WAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;AACA,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,UAAI,qBAAqB;AACrB,oBAAY;AAAA,MAChB;AACA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,QAAS,YAAY;AACjB,aAAO,WAAW,OAAO,aAAa,IAAI;AAAA,IAC9C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM,IAAI;AAC3B,SAAO,GAAG,IAAI;AAClB;AACiB,SAAS,oBAAoBC,aAAY;AACtD,QAAM,KAAK,IAAI,gBAAgB;AAC/B,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAS;AAC3C,QAAI,SAAS;AACb,aAAS,SAAS;AACd,UAAI,QAAQ;AACR;AAAA,MACJ;AACA,eAAS;AACT,WAAK,YAAY;AAAA,IACrB;AACA,OAAG,OAAO,iBAAiB,SAAS,MAAI;AACpC,aAAO,GAAG,OAAO,MAAM;AAAA,IAC3B,CAAC;AACD,UAAM,OAAOA,YAAW,UAAU;AAAA,MAC9B,KAAM,MAAM;AACR,iBAAS;AACT,gBAAQ,IAAI;AACZ,eAAO;AAAA,MACX;AAAA,MACA,MAAO,MAAM;AACT,eAAO,IAAI;AAAA,MACf;AAAA,MACA,WAAY;AACR,WAAG,MAAM;AACT,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AACX;;;AC1EA,SAAS,MAAM,OAAO;AAClB,SAAO,CAAC,WAAS;AACb,QAAI,WAAW;AACf,QAAI,eAAe;AACnB,UAAM,YAAY,CAAC;AACnB,aAAS,gBAAgB;AACrB,UAAI,cAAc;AACd;AAAA,MACJ;AACA,qBAAe,OAAO,UAAU;AAAA,QAC5B,KAAM,OAAO;AA/B7B,cAAAC;AAgCoB,qBAAW,YAAY,WAAU;AAC7B,aAAAA,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,QACA,MAAO,OAAO;AApC9B,cAAAA;AAqCoB,qBAAW,YAAY,WAAU;AAC7B,aAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;AAAA,UACrB;AAAA,QACJ;AAAA,QACA,WAAY;AAzC5B,cAAAA;AA0CoB,qBAAW,YAAY,WAAU;AAC7B,aAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,gBAAgB;AAErB,UAAI,aAAa,KAAK,cAAc;AAChC,cAAM,OAAO;AACb,uBAAe;AACf,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AACA,WAAO,WAAW,CAAC,eAAa;AAC5B;AACA,gBAAU,KAAK,UAAU;AACzB,oBAAc;AACd,aAAO;AAAA,QACH,cAAe;AACX;AACA,wBAAc;AACd,gBAAM,QAAQ,UAAU,UAAU,CAAC,MAAI,MAAM,UAAU;AACvD,cAAI,QAAQ,IAAI;AACZ,sBAAU,OAAO,OAAO,CAAC;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,IAAI,UAAU;AACnB,SAAO,CAAC,WAAS;AACb,WAAO,WAAW,CAAC,gBAAc;AAC7B,aAAO,OAAO,UAAU;AAAA,QACpB,KAAM,OAAO;AA7E7B,cAAAA;AA8EoB,WAAAA,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;AAChB,sBAAY,KAAK,KAAK;AAAA,QAC1B;AAAA,QACA,MAAO,OAAO;AAjF9B,cAAAA;AAkFoB,WAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;AACjB,sBAAY,MAAM,KAAK;AAAA,QAC3B;AAAA,QACA,WAAY;AArF5B,cAAAA;AAsFoB,WAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;AACA,sBAAY,SAAS;AAAA,QACzB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AACA,IAAM,sBAAsB,OAAO;;;ACvF/B,SAAS,gBAAgB,cAAc;AACvC,MAAI,QAAQ;AACZ,QAAM,eAAe,CAAC;AACtB,QAAM,cAAc,CAAC,aAAW;AAC5B,QAAI,UAAU,QAAW;AACrB,eAAS,KAAK,KAAK;AAAA,IACvB;AACA,iBAAa,KAAK,QAAQ;AAAA,EAC9B;AACA,QAAM,iBAAiB,CAAC,aAAW;AAC/B,iBAAa,OAAO,aAAa,QAAQ,QAAQ,GAAG,CAAC;AAAA,EACzD;AACA,QAAM,MAAM,WAAW,CAAC,aAAW;AAC/B,gBAAY,QAAQ;AACpB,WAAO,MAAI;AACP,qBAAe,QAAQ;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,MAAI,OAAO,CAAC,cAAY;AACpB,QAAI,UAAU,WAAW;AACrB;AAAA,IACJ;AACA,YAAQ;AACR,eAAW,YAAY,cAAa;AAChC,eAAS,KAAK,SAAS;AAAA,IAC3B;AAAA,EACJ;AACA,MAAI,MAAM,MAAI;AACd,SAAO;AACX;;;ACjCiB,SAAS,YAAY,MAAM;AACxC,SAAO,WAAW,CAAC,aAAW;AAC1B,aAAS,QAAQ,QAAQ,GAAG,KAAK,KAAK,IAAI;AACtC,YAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,UAAI,CAAC,MAAM;AACP,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACtF;AACA,YAAM,eAAe,KAAK;AAAA,QACtB;AAAA,QACA,KAAM,QAAQ;AACV,gBAAM,eAAe,QAAQ,QAAQ,GAAG,MAAM;AAC9C,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,OAAO,QAAQ;AACrB,WAAO,KAAK,UAAU,QAAQ;AAAA,EAClC,CAAC;AACL;;;ACrBA,IAAM,OAAO,MAAI;AAEjB;AACA,IAAM,oBAAoB,CAAC,QAAM;AAC7B,MAAI,OAAO,QAAQ;AACf,WAAO,OAAO,GAAG;AAAA,EACrB;AACJ;AACA,SAAS,iBAAiB,UAAU,MAAM,MAAM;AAC5C,MAAI,OAAO;AACX,QAAM,WAAW,KAAK,KAAK,GAAG;AAC9B,GAAC,QAAQ,MAAM,YAAY,QAAQ,MAAM,MAAM,SAAS,IAAI,IAAI,MAAM,MAAM;AAAA,IACxE,IAAK,MAAM,KAAK;AACZ,UAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ;AAG3C,eAAO;AAAA,MACX;AACA,aAAO,iBAAiB,UAAU;AAAA,QAC9B,GAAG;AAAA,QACH;AAAA,MACJ,GAAG,IAAI;AAAA,IACX;AAAA,IACA,MAAO,IAAI,IAAI,MAAM;AACjB,YAAM,aAAa,KAAK,KAAK,SAAS,CAAC;AACvC,UAAI,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACJ;AAEA,UAAI,eAAe,QAAQ;AACvB,eAAO;AAAA,UACH,MAAM,KAAK,UAAU,IAAI;AAAA,YACrB,KAAK,CAAC;AAAA,UACV,IAAI,CAAC;AAAA,UACL,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,QAC1B;AAAA,MACJ,WAAW,eAAe,SAAS;AAC/B,eAAO;AAAA,UACH,MAAM,KAAK,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,UACpC,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,QAC1B;AAAA,MACJ;AACA,wBAAkB,KAAK,IAAI;AAC3B,wBAAkB,KAAK,IAAI;AAC3B,aAAO,SAAS,IAAI;AAAA,IACxB;AAAA,EACJ,CAAC;AACD,SAAO,KAAK,QAAQ;AACxB;AAKI,IAAM,uBAAuB,CAAC,aAAW,iBAAiB,UAAU,CAAC,GAAG,uBAAO,OAAO,IAAI,CAAC;AAK3F,IAAM,kBAAkB,CAAC,aAAW;AACpC,SAAO,IAAI,MAAM,MAAM;AAAA,IACnB,IAAK,MAAM,MAAM;AACb,UAAI,SAAS,QAAQ;AAGjB,eAAO;AAAA,MACX;AACA,aAAO,SAAS,IAAI;AAAA,IACxB;AAAA,EACJ,CAAC;AACL;;;ACtEA,IAAM,mBAAmB,CAAC,EAAE,MAAM,MAAI;AAClC,SAAO;AACX;;;ACII,IAAM,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA,EAI9B,aAAa;AAAA;AAAA;AAAA;AAAA,EAGb,aAAa;AAAA;AAAA,EAEf,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,uBAAuB;AAC3B;;;AClCiB,IAAM,cAAc,OAAO;AAIxC,SAAS,sBAAsB,SAAS,MAAM;AAC9C,QAAM,SAAS,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,IAAI;AACtD,aAAW,aAAa,MAAK;AACzB,eAAU,OAAO,WAAU;AACvB,UAAI,OAAO,UAAU,OAAO,GAAG,MAAM,UAAU,GAAG,GAAG;AACjD,cAAM,IAAI,MAAM,iBAAiB,GAAG,EAAE;AAAA,MAC1C;AACA,aAAO,GAAG,IAAI,UAAU,GAAG;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;AAII,SAAS,SAAS,OAAO;AACzB,SAAO,CAAC,CAAC,SAAS,CAAC,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAU;AAChE;AACA,SAAS,WAAW,IAAI;AACpB,SAAO,OAAO,OAAO;AACzB;AAII,SAAS,cAAc,KAAK;AAC5B,SAAO,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,GAAG;AACjD;AACA,IAAM,0BAA0B,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO;AACzE,SAAS,gBAAgB,OAAO;AAC5B,SAAO,2BAA2B,SAAS,KAAK,KAAK,OAAO,iBAAiB;AACjF;AAGI,IAAM,MAAM,CAAC,OAAK,GAAG;AAgBzB,SAAS,MAAM,KAAK,GAAG;AACnB,SAAO,IAAI,QAAQ,CAAC,QAAM,WAAW,KAAK,EAAE,CAAC;AACjD;;;ACrDA,SAAS,iBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAM,oBAAN,cAAgC,MAAM;AACtC;AACA,SAAS,oBAAoB,OAAO;AAChC,MAAI,iBAAiB,OAAO;AACxB,WAAO;AAAA,EACX;AACA,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,eAAe,SAAS,cAAc,UAAU,MAAM;AAC/D,WAAO;AAAA,EACX;AAEA,MAAI,SAAS,UAAU;AAEnB,WAAO,IAAI,MAAM,OAAO,KAAK,CAAC;AAAA,EAClC;AAEA,MAAI,SAAS,KAAK,GAAG;AACjB,WAAO,OAAO,OAAO,IAAI,kBAAkB,GAAG,KAAK;AAAA,EACvD;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,OAAO;AACpC,MAAI,iBAAiB,WAAW;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB,SAAS,MAAM,SAAS,aAAa;AAEtD,WAAO;AAAA,EACX;AACA,QAAM,YAAY,IAAI,UAAU;AAAA,IAC5B,MAAM;AAAA,IACN;AAAA,EACJ,CAAC;AAED,MAAI,iBAAiB,SAAS,MAAM,OAAO;AACvC,cAAU,QAAQ,MAAM;AAAA,EAC5B;AACA,SAAO;AACX;AACA,IAAM,YAAN,cAAwB,MAAM;AAAA,EAC1B,YAAY,MAAK;AACb,UAAM,QAAQ,oBAAoB,KAAK,KAAK;AAC5C,UAAM,UAAU,KAAK,YAAW,+BAAO,YAAW,KAAK;AAGvD,UAAM,SAAS;AAAA,MACX;AAAA,IACJ,CAAC;AAAA;AAAA,IAED,iBAAiB,MAAM,SAAS,MAAM,GAAG,iBAAiB,MAAM,QAAQ,MAAM;AAC9E,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU,KAAK,QAAQ;AAAA,EAChC;AACJ;;;ACjEI,SAAS,mBAAmB,aAAa;AACzC,MAAI,WAAW,aAAa;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AACJ;AAGI,IAAM,qBAAqB;AAAA,EAC3B,OAAO;AAAA,IACH,WAAW,CAAC,QAAM;AAAA,IAClB,aAAa,CAAC,QAAM;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACJ,WAAW,CAAC,QAAM;AAAA,IAClB,aAAa,CAAC,QAAM;AAAA,EACxB;AACJ;AA2BiB,SAAS,qBAAqB,UAAU,aAAa;AAClE,MAAI,WAAW,UAAU;AACrB,UAAM,QAAQ,YAAY,YAAY,SAAS,KAAK;AACpD,WAAO;AAAA,MACH,IAAI;AAAA,MACJ,OAAO;AAAA,QACH,GAAG;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,SAAS;AAAA,IACX,GAAG,SAAS;AAAA,IACZ,IAAI,CAAC,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,WAAW;AAAA,MAC7D,MAAM;AAAA,MACN,MAAM,YAAY,YAAY,SAAS,OAAO,IAAI;AAAA,IACtD;AAAA,EACJ;AACA,SAAO;AAAA,IACH,IAAI;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,uBAAN,cAAmC,MAAM;AAAA,EACrC,cAAa;AACT,UAAM,0CAA0C;AAAA,EACpD;AACJ;AAII,SAAS,gBAAgB,UAAU,aAAa;AAChD,MAAI;AACJ,MAAI;AAEA,aAAS,qBAAqB,UAAU,WAAW;AAAA,EACvD,QAAS;AACL,UAAM,IAAI,qBAAqB;AAAA,EACnC;AAEA,MAAI,CAAC,OAAO,OAAO,CAAC,SAAS,OAAO,MAAM,KAAK,KAAK,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,WAAW;AACjG,UAAM,IAAI,qBAAqB;AAAA,EACnC;AACA,MAAI,OAAO,MAAM,CAAC,SAAS,OAAO,MAAM,GAAG;AACvC,UAAM,IAAI,qBAAqB;AAAA,EACnC;AACA,SAAO;AACX;;;AC5FA,IAAM,aAAa,OAAO,MAAM;AAChC,SAAS,KAAK,IAAI;AACd,QAAM,WAAW,OAAO;AACxB,MAAI,SAAS;AACb,SAAO,MAAI;AACP,QAAI,WAAW,UAAU;AACrB,eAAS,GAAG;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AACJ;AAoBA,SAAS,OAAO,OAAO;AACnB,SAAO,OAAO,UAAU,cAAc,cAAc;AACxD;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,SAAS,KAAK,KAAK,SAAS,MAAM,MAAM,CAAC,KAAK,YAAY,MAAM,MAAM;AACjF;AACA,IAAM,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,OAAO;AAAA,EACP,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AAAA,EACZ,eAAe,CAAC;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AACjB;AAGI,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAIpB;AAAA;AAAA;AAAA;AAAA,EAGA;AAAA,EACF;AACJ;AAGI,SAAS,oBAAoB,QAAQ;AACrC,WAAS,kBAAkB,OAAO;AAC9B,UAAM,oBAAoB,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,MAAI,cAAc,SAAS,CAAC,CAAC,CAAC;AAC3F,QAAI,kBAAkB,OAAO,GAAG;AAC5B,YAAM,IAAI,MAAM,+CAA+C,MAAM,KAAK,iBAAiB,EAAE,KAAK,IAAI,CAAC;AAAA,IAC3G;AACA,UAAM,aAAa,cAAc,CAAC,CAAC;AACnC,UAAMC,QAAO,cAAc,CAAC,CAAC;AAC7B,aAAS,iBAAiB,MAAM;AAC5B,aAAO;AAAA,QACH,KAAK,KAAK;AAAA,QACV,MAAM,KAAK,YAAU;AACjB,gBAAMC,UAAS,MAAM,KAAK,IAAI;AAC9B,gBAAM,WAAW;AAAA,YACb,GAAG,KAAK;AAAA,YACR,KAAK;AAAA,UACT;AACA,gBAAM,UAAU,SAAS,KAAK,GAAG;AACjC,eAAK,UAAU,KAAK,GAAG,IAAI,KAAKA,QAAO,KAAK,QAAQ,QAAQ;AAC5D,iBAAOD,MAAK,OAAO;AAEnB,qBAAW,CAAC,WAAW,UAAU,KAAK,OAAO,QAAQC,QAAO,KAAK,IAAI,GAAE;AACnE,kBAAM,kBAAkB;AAAA,cACpB,GAAG;AAAA,cACH;AAAA,YACJ,EAAE,KAAK,GAAG;AAEV,YAAAD,MAAK,eAAe,IAAI,iBAAiB;AAAA,cACrC,KAAK,WAAW;AAAA,cAChB,MAAM;AAAA,cACN,KAAK;AAAA,cACL,WAAW,KAAK,UAAU,KAAK,GAAG;AAAA,YACtC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,aAAS,KAAK,MAAM,OAAO,CAAC,GAAG;AAC3B,YAAM,YAAY,cAAc,CAAC,CAAC;AAClC,iBAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,QAAQ,CAAC,CAAC,GAAE;AACjD,YAAI,OAAO,IAAI,GAAG;AACd,UAAAA,MAAK;AAAA,YACD,GAAG;AAAA,YACH;AAAA,UACJ,EAAE,KAAK,GAAG,CAAC,IAAI,iBAAiB;AAAA,YAC5B;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,UACJ,CAAC;AACD;AAAA,QACJ;AACA,YAAI,SAAS,IAAI,GAAG;AAChB,oBAAU,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ;AAAA,YACpC,GAAG;AAAA,YACH;AAAA,UACJ,CAAC;AACD;AAAA,QACJ;AACA,YAAI,CAAC,YAAY,IAAI,GAAG;AAEpB,oBAAU,GAAG,IAAI,KAAK,MAAM;AAAA,YACxB,GAAG;AAAA,YACH;AAAA,UACJ,CAAC;AACD;AAAA,QACJ;AACA,cAAM,UAAU;AAAA,UACZ,GAAG;AAAA,UACH;AAAA,QACJ,EAAE,KAAK,GAAG;AACV,YAAI,WAAW,OAAO,GAAG;AACrB,gBAAM,IAAI,MAAM,kBAAkB,OAAO,EAAE;AAAA,QAC/C;AACA,mBAAW,OAAO,IAAI;AACtB,kBAAU,GAAG,IAAI;AAAA,MACrB;AACA,aAAO;AAAA,IACX;AACA,UAAM,SAAS,KAAK,KAAK;AACzB,UAAM,OAAO;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR;AAAA,MACA,MAAAA;AAAA,MACA,GAAG;AAAA,MACH;AAAA,IACJ;AACA,UAAM,SAAS;AAAA,MACX,GAAG;AAAA,MACH;AAAA,MACA,cAAc,oBAAoB,EAAE;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,mBAAmB;AACpC,SAAO,OAAO,sBAAsB;AACxC;AAGI,eAAe,mBAAmB,QAAQ,MAAM;AAChD,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,YAAY,KAAK,WAAW,IAAI;AACpC,SAAM,CAAC,WAAU;AACb,UAAM,MAAM,OAAO,KAAK,KAAK,IAAI,EAAE,KAAK,CAACE,SAAM,KAAK,WAAWA,IAAG,CAAC;AAEnE,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AAGA,UAAM,aAAa,KAAK,KAAK,GAAG;AAChC,UAAM,WAAW,KAAK;AACtB,gBAAY,KAAK,WAAW,IAAI;AAAA,EACpC;AACA,SAAO;AACX;AAoBA,SAAS,sBAAsB;AAC3B,SAAO,SAAS,kBAAkB,QAAQ;AACtC,UAAM,EAAE,KAAK,IAAI;AACjB,WAAO,SAAS,aAAa,eAAe,MAAM;AAC9C,aAAO,qBAAqB,OAAO,EAAE,MAAM,KAAK,MAAI;AAlNhE,YAAAC;AAmNgB,cAAM,WAAW,KAAK,KAAK,GAAG;AAC9B,YAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,QAAQ;AACzC,iBAAO;AAAA,QACX;AACA,cAAM,YAAY,MAAM,mBAAmB,QAAQ,QAAQ;AAC3D,YAAI,MAAM;AACV,YAAI;AACA,cAAI,CAAC,WAAW;AACZ,kBAAM,IAAI,UAAU;AAAA,cAChB,MAAM;AAAA,cACN,SAAS,+BAA+B,IAAI;AAAA,YAChD,CAAC;AAAA,UACL;AACA,gBAAM,WAAW,aAAa,IAAI,MAAM,QAAQ,QAAQ,cAAc,CAAC,IAAI;AAC3E,iBAAO,MAAM,UAAU;AAAA,YACnB,MAAM;AAAA,YACN,aAAa,YAAU,KAAK,CAAC;AAAA,YAC7B;AAAA,YACA,MAAM,UAAU,KAAK;AAAA,YACrB,QAAQ,6BAAM;AAAA,UAClB,CAAC;AAAA,QACL,SAAS,OAAO;AACZ,WAAAA,MAAA,6BAAM,YAAN,gBAAAA,IAAA,WAAgB;AAAA,YACZ;AAAA,YACA,OAAO,wBAAwB,KAAK;AAAA,YACpC,OAAO,KAAK,CAAC;AAAA,YACb,MAAM;AAAA,YACN,OAAM,uCAAW,KAAK,SAAQ;AAAA,UAClC;AACA,gBAAM;AAAA,QACV;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,YAAY;AAtPrC,MAAAA;AAuPI,QAAM,SAAS,sBAAsB,CAAC,GAAG,GAAG,WAAW,IAAI,CAAC,MAAI,EAAE,KAAK,MAAM,CAAC;AAC9E,QAAM,iBAAiB,WAAW,OAAO,CAAC,uBAAuB,eAAa;AAC1E,QAAI,WAAW,KAAK,QAAQ,kBAAkB,WAAW,KAAK,QAAQ,mBAAmB,kBAAkB;AACvG,UAAI,0BAA0B,oBAAoB,0BAA0B,WAAW,KAAK,QAAQ,gBAAgB;AAChH,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC/D;AACA,aAAO,WAAW,KAAK,QAAQ;AAAA,IACnC;AACA,WAAO;AAAA,EACX,GAAG,gBAAgB;AACnB,QAAM,cAAc,WAAW,OAAO,CAAC,MAAM,YAAU;AACnD,QAAI,QAAQ,KAAK,QAAQ,eAAe,QAAQ,KAAK,QAAQ,gBAAgB,oBAAoB;AAC7F,UAAI,SAAS,sBAAsB,SAAS,QAAQ,KAAK,QAAQ,aAAa;AAC1E,cAAM,IAAI,MAAM,uCAAuC;AAAA,MAC3D;AACA,aAAO,QAAQ,KAAK,QAAQ;AAAA,IAChC;AACA,WAAO;AAAA,EACX,GAAG,kBAAkB;AACrB,QAAM,SAAS,oBAAoB;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,OAAO,WAAW,MAAM,CAAC,MAAI,EAAE,KAAK,QAAQ,KAAK;AAAA,IACjD,sBAAsB,WAAW,MAAM,CAAC,MAAI,EAAE,KAAK,QAAQ,oBAAoB;AAAA,IAC/E,UAAU,WAAW,MAAM,CAAC,MAAI,EAAE,KAAK,QAAQ,QAAQ;AAAA,IACvD,SAAQA,MAAA,WAAW,CAAC,MAAZ,gBAAAA,IAAe,KAAK,QAAQ;AAAA,EACxC,CAAC,EAAE,MAAM;AACT,SAAO;AACX;;;ACnRuD,SAASC,kBAAiB,KAAK,KAAK,OAAO;AAC9F,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAI;AAGA,IAAM,oBAAoB,oBAAI,QAAQ;AAGtB,IAAM,OAAO,MAAI;AAErC;AACA,eAAe,OAAO;AACtB,IAAI,gBAAgB;AAiChB,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBd,YAAY;AAEV,QAAI;AACJ,QAAI;AACJ,UAAM,EAAE,WAAW,IAAI;AACvB,QAAI,eAAe,MAAM;AAErB,UAAI,KAAK,gBAAgB,MAAM;AAE3B,cAAM,IAAI,MAAM,6CAA6C;AAAA,MACjE;AACA,YAAM,aAAa,cAAc;AACjC,WAAK,cAAc,eAAe,KAAK,aAAa,UAAU;AAC9D,gBAAU,WAAW;AACrB,oBAAc,MAAI;AACd,YAAI,KAAK,gBAAgB,MAAM;AAC3B,eAAK,cAAc,kBAAkB,KAAK,aAAa,UAAU;AAAA,QACrE;AAAA,MACJ;AAAA,IACJ,OAAO;AAEH,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,WAAW,aAAa;AACxB,kBAAU,QAAQ,QAAQ,WAAW,KAAK;AAAA,MAC9C,OAAO;AACH,kBAAU,QAAQ,OAAO,WAAW,MAAM;AAAA,MAC9C;AACA,oBAAc;AAAA,IAClB;AAEA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA,EACoE,KAAK,aAAa,YAAY;AAC9F,UAAM,aAAa,KAAK,UAAU;AAClC,UAAM,EAAE,YAAY,IAAI;AACxB,WAAO,OAAO,OAAO,WAAW,KAAK,aAAa,UAAU,GAAG;AAAA,MAC3D;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,YAAY;AACd,UAAM,aAAa,KAAK,UAAU;AAClC,UAAM,EAAE,YAAY,IAAI;AACxB,WAAO,OAAO,OAAO,WAAW,MAAM,UAAU,GAAG;AAAA,MAC/C;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,QAAQ,WAAW;AACf,UAAM,aAAa,KAAK,UAAU;AAClC,UAAM,EAAE,YAAY,IAAI;AACxB,WAAO,OAAO,OAAO,WAAW,QAAQ,SAAS,GAAG;AAAA,MAChD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAEuC,OAAO,MAAM,SAAS;AACzD,UAAM,SAAS,WAAU,uBAAuB,OAAO;AACvD,WAAO,OAAO,WAAW,cAAc,SAAS,WAAU,0BAA0B,OAAO;AAAA,EAC/F;AAAA;AAAA,EACmE,OAAO,0BAA0B,SAAS;AACzG,UAAM,UAAU,IAAI,WAAU,OAAO;AACrC,sBAAkB,IAAI,SAAS,OAAO;AACtC,sBAAkB,IAAI,SAAS,OAAO;AACtC,WAAO;AAAA,EACX;AAAA;AAAA,EAC6E,OAAO,uBAAuB,SAAS;AAChH,WAAO,kBAAkB,IAAI,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAEkE,OAAO,QAAQ,OAAO;AACpF,UAAM,UAAU,OAAO,UAAU,YAAY,UAAU,QAAQ,UAAU,SAAS,OAAO,MAAM,SAAS,aAAa,QAAQ,QAAQ,QAAQ,KAAK;AAClJ,WAAO,WAAU,MAAM,OAAO,EAAE,UAAU;AAAA,EAC9C;AAAA,EACA,aAAa,IAAI,QAAQ;AACrB,UAAM,cAAc,MAAM,QAAQ,MAAM,IAAI,SAAS;AAAA,MACjD,GAAG;AAAA,IACP;AACA,UAAM,qBAAqB,YAAY,IAAI,WAAU,OAAO;AAC5D,QAAI;AACA,aAAO,MAAM,QAAQ,IAAI,kBAAkB;AAAA,IAC/C,UAAE;AACE,yBAAmB,QAAQ,CAAC,EAAE,YAAY,MAAI;AAC1C,oBAAY;AAAA,MAChB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,aAAa,KAAK,QAAQ;AACtB,UAAM,cAAc,MAAM,QAAQ,MAAM,IAAI,SAAS;AAAA,MACjD,GAAG;AAAA,IACP;AACA,UAAM,qBAAqB,YAAY,IAAI,WAAU,OAAO;AAC5D,QAAI;AACA,aAAO,MAAM,QAAQ,KAAK,kBAAkB;AAAA,IAChD,UAAE;AACE,yBAAmB,QAAQ,CAAC,EAAE,YAAY,MAAI;AAC1C,oBAAY;AAAA,MAChB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYI,aAAa,eAAe,UAAU;AAEtC,UAAM,eAAe,SAAS,IAAI,gBAAgB;AAElD,QAAI;AACA,aAAO,MAAM,QAAQ,KAAK,YAAY;AAAA,IAC1C,UAAE;AACE,iBAAW,WAAW,cAAa;AAE/B,gBAAQ,YAAY;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,KAAI;AACqE,IAAAA,kBAAiB,MAAM,WAAW,MAAM;AAE3C,IAAAA,kBAAiB,MAAM,eAAe,CAAC,CAAC;AAGzD,IAAAA,kBAAiB,MAAM,cAAc,IAAI;AAC9E,IAAAA,kBAAiB,MAAM,eAAe,WAAW;AAEzE,QAAI,OAAO,QAAQ,YAAY;AAC3B,WAAK,UAAU,IAAI,QAAQ,GAAG;AAAA,IAClC,OAAO;AACH,WAAK,UAAU;AAAA,IACnB;AAGA,UAAM,aAAa,KAAK,QAAQ,KAAK,CAAC,UAAQ;AAE1C,YAAM,EAAE,YAAY,IAAI;AACxB,WAAK,cAAc;AACnB,WAAK,aAAa;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,MACJ;AAEA,iDAAa,QAAQ,CAAC,EAAE,QAAQ,MAAI;AAChC,gBAAQ,KAAK;AAAA,MACjB;AAAA,IACJ,CAAC;AAED,QAAI,WAAW,YAAY;AACvB,iBAAW,MAAM,CAAC,WAAS;AAEvB,cAAM,EAAE,YAAY,IAAI;AACxB,aAAK,cAAc;AACnB,aAAK,aAAa;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,QACJ;AAEA,mDAAa,QAAQ,CAAC,EAAE,OAAO,MAAI;AAC/B,iBAAO,MAAM;AAAA,QACjB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAMM,SAAS,iBAAiB,SAAS;AACrC,SAAO,UAAU,MAAM,OAAO,EAAE,KAAK,MAAI;AAAA,IACjC;AAAA,EACJ,CAAC;AACT;AACsI,SAAS,gBAAgB;AAC3J,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,UAAU,YAAU;AAC7C,cAAU;AACV,aAAS;AAAA,EACb,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACiC,SAAS,eAAe,KAAK,QAAQ;AAClE,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,KAAK,OAAO;AAClC,SAAO;AAAA,IACH,GAAG,IAAI,MAAM,GAAG,KAAK;AAAA,IACrB,GAAG,IAAI,MAAM,QAAQ,CAAC;AAAA,EAC1B;AACJ;AACA,SAAS,kBAAkB,KAAK,QAAQ;AACpC,QAAM,QAAQ,IAAI,QAAQ,MAAM;AAChC,MAAI,UAAU,IAAI;AACd,WAAO,iBAAiB,KAAK,KAAK;AAAA,EACtC;AACA,SAAO;AACX;;;AC5RA,IAAI;AAAJ,IAEA;AAAA,CACC,UAAU,QAAQ,YAAY,QAAQ,UAAU,OAAO;AAAA,CACvD,WAAW,QAAQ,iBAAiB,SAAS,eAAe,OAAO;AAOhE,SAAS,aAAa,OAAO,SAAS;AACtC,QAAM,KAAK;AAEX,QAAM,WAAW,GAAG,OAAO,OAAO;AAElC,KAAG,OAAO,OAAO,IAAI,MAAI;AACrB,YAAQ;AACR;AAAA,EACJ;AACA,SAAO;AACX;AAOI,SAAS,kBAAkB,OAAO,SAAS;AAC3C,QAAM,KAAK;AAEX,QAAM,WAAW,GAAG,OAAO,YAAY;AAEvC,KAAG,OAAO,YAAY,IAAI,YAAU;AAChC,UAAM,QAAQ;AACd,WAAM;AAAA,EACV;AACA,SAAO;AACX;;;ACtCA,IAAM,+BAA+B,OAAO;AAC5C,SAAS,cAAc,IAAI;AACvB,MAAI,QAAQ;AACZ,SAAO,aAAa;AAAA,IAChB,QAAS;AACL,UAAI,OAAO;AACP,cAAM,IAAI,MAAM,uBAAuB;AAAA,MAC3C;AACA,YAAM,UAAU,IAAI,QAAQ,CAAC,YAAU;AACnC,gBAAQ,WAAW,MAAI,QAAQ,4BAA4B,GAAG,EAAE;AAAA,MACpE,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ,GAAG,MAAI;AACH,QAAI,OAAO;AACP,mBAAa,KAAK;AAAA,IACtB;AAAA,EACJ,CAAC;AACL;;;ACpB8D,SAAS,iBAAiB;AACpF,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAM;AACpC,cAAU;AACV,aAAS;AAAA,EACb,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;ACyDA,IAAM,WAAW,OAAO,MAAM;;;AC7D9B,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,4BAA4B,KAAK,OAAO,OAAO;AACpD,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI,SAAS;AACb,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IACvC;AACA,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAC9B,UAAI,MAAO,SAAQ;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAO,WAAU,WAAW;AAC5B,UAAI;AACA,cAAM,KAAK,IAAI;AAAA,MACnB,SAAS,GAAG;AACR,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B;AAAA,IACJ;AACA,QAAI,MAAM,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,WAAW,OAAO;AACd,QAAI,MAAM,KAAK;AAAA,MACX,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,KAAK;AAChC,MAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAS,OAAO,YAAY,SAAS;AAClH,QAAI,IAAI,IAAI,MAAM,OAAO;AACzB,WAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AAAA,EACnF;AACA,UAAQ,wBAAwB,SAASC,uBAAsBC,MAAK;AAChE,aAAS,KAAK,GAAG;AACb,MAAAA,KAAI,QAAQA,KAAI,WAAW,IAAI,iBAAiB,GAAGA,KAAI,OAAO,0CAA0C,IAAI;AAC5G,MAAAA,KAAI,WAAW;AAAA,IACnB;AACA,QAAI,GAAG,IAAI;AACX,aAAS,OAAO;AACZ,aAAM,IAAIA,KAAI,MAAM,IAAI,GAAE;AACtB,YAAI;AACA,cAAI,CAAC,EAAE,SAAS,MAAM,EAAG,QAAO,IAAI,GAAGA,KAAI,MAAM,KAAK,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,IAAI;AACrF,cAAI,EAAE,SAAS;AACX,gBAAI,SAAS,EAAE,QAAQ,KAAK,EAAE,KAAK;AACnC,gBAAI,EAAE,MAAO,QAAO,KAAK,GAAG,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AACvE,mBAAK,CAAC;AACN,qBAAO,KAAK;AAAA,YAChB,CAAC;AAAA,UACL,MAAO,MAAK;AAAA,QAChB,SAAS,GAAG;AACR,eAAK,CAAC;AAAA,QACV;AAAA,MACJ;AACA,UAAI,MAAM,EAAG,QAAOA,KAAI,WAAW,QAAQ,OAAOA,KAAI,KAAK,IAAI,QAAQ,QAAQ;AAC/E,UAAIA,KAAI,SAAU,OAAMA,KAAI;AAAA,IAChC;AACA,WAAO,KAAK;AAAA,EAChB,GAAG,GAAG;AACV;AAKA,IAAM,2BAA2B;AACjC,IAAM,kCAAkC;AACxC,IAAM,2BAA2B;AACjC,IAAM,0BAA0B;AAChC,IAAM,+BAA+B;AACrC,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAkOpC,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC3B,YAAY,MAAK;AACb,UAAM,4BAA4B,GAAGC,kBAAiB,MAAM,QAAQ,MAAM,GAAG,KAAK,OAAO;AAAA,EAC7F;AACJ;AACA,IAAM,4BAA4B,CAAC,WAAS;AACxC,SAAO;AAAA,IACH,YAAa;AACT,YAAM,SAAS,IAAI,eAAe;AAAA,QAC9B,MAAO,YAAY;AACf,iBAAO,GAAG,QAAQ,CAAC,UAAQ;AACvB,uBAAW,QAAQ,KAAK;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,OAAO,MAAI;AACjB,uBAAW,MAAM;AAAA,UACrB,CAAC;AACD,iBAAO,GAAG,SAAS,CAAC,UAAQ;AACxB,uBAAW,MAAM,KAAK;AAAA,UAC1B,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AACD,aAAO,OAAO,UAAU;AAAA,IAC5B;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,MAAM;AACjC,QAAM,SAAS,eAAe,OAAO,KAAK,UAAU,IAAI,0BAA0B,IAAI,EAAE,UAAU;AAClG,MAAI,gBAAgB;AACpB,SAAO,IAAI,eAAe;AAAA,IACtB,MAAM,KAAM,YAAY;AACpB,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACN,mBAAW,MAAM;AAAA,MACrB,OAAO;AACH,mBAAW,QAAQ,KAAK;AAAA,MAC5B;AAAA,IACJ;AAAA,IACA,SAAU;AACN,aAAO,OAAO,OAAO;AAAA,IACzB;AAAA,EACJ,CAAC,EAAE,YAAY,IAAI,kBAAkB,CAAC,EAAE,YAAY,IAAI,gBAAgB;AAAA,IACpE,UAAW,OAAO,YAAY;AAC1B,uBAAiB;AACjB,YAAM,QAAQ,cAAc,MAAM,IAAI;AACtC,sBAAgB,MAAM,IAAI,KAAK;AAC/B,iBAAW,QAAQ,OAAM;AACrB,mBAAW,QAAQ,IAAI;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ,CAAC,CAAC;AACN;AACA,SAAS,qBAAqB,MAAM;AAChC,QAAM,SAAS,sBAAsB,IAAI;AACzC,MAAI,WAAW;AACf,SAAO,OAAO,YAAY,IAAI,gBAAgB;AAAA,IAC1C,UAAW,MAAM,YAAY;AACzB,UAAI,CAAC,UAAU;AACX,cAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,mBAAW,QAAQ,IAAI;AACvB,mBAAW;AAAA,MACf,OAAO;AACH,cAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,mBAAW,QAAQ,KAAK;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ,CAAC,CAAC;AACN;AAGI,SAAS,qBAAqB,iBAAiB;AAC/C,QAAM,gBAAgB,oBAAI,IAAI;AAG5B,WAAS,UAAU;AACjB,WAAO,MAAM,KAAK,cAAc,OAAO,CAAC,EAAE,MAAM,CAAC,MAAI,EAAE,MAAM;AAAA,EACjE;AAGE,WAAS,yBAAyB;AAChC,QAAI;AACJ,UAAM,SAAS,IAAI,eAAe;AAAA,MAC9B,MAAO,YAAY;AACf,6BAAqB;AAAA,MACzB;AAAA,IACJ,CAAC;AACD,UAAM,mBAAmB;AAAA,MACrB,SAAS,CAAC,MAAI,mBAAmB,QAAQ,CAAC;AAAA,MAC1C,OAAO,MAAI;AACP,2BAAmB,MAAM;AACzB,cAAM;AACN,YAAI,QAAQ,GAAG;AACX,0BAAgB,MAAM;AAAA,QAC1B;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,MACR,mBAAmB,MAAI;AACnB,cAAM,SAAS,OAAO,UAAU;AAChC,eAAO,aAAa,QAAQ,MAAI;AAC5B,iBAAO,YAAY;AACnB,2BAAiB,MAAM;AAAA,QAC3B,CAAC;AAAA,MACL;AAAA,MACA,OAAO,CAAC,WAAS;AACb,2BAAmB,MAAM,MAAM;AAC/B,cAAM;AAAA,MACV;AAAA,IACJ;AACA,aAAS,QAAQ;AACb,aAAO,OAAO,kBAAkB;AAAA,QAC5B,QAAQ;AAAA,QACR,OAAO,MAAI;AAAA,QAEX;AAAA,QACA,SAAS,MAAI;AAAA,QAEb;AAAA,QACA,mBAAmB;AAAA,QACnB,OAAO,MAAI;AAAA,QAEX;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAGE,WAAS,YAAY,SAAS;AAC5B,QAAI,IAAI,cAAc,IAAI,OAAO;AACjC,QAAI,CAAC,GAAG;AACJ,UAAI,uBAAuB;AAC3B,oBAAc,IAAI,SAAS,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AAGE,WAAS,UAAU,QAAQ;AACzB,eAAW,cAAc,cAAc,OAAO,GAAE;AAC5C,iBAAW,MAAM,MAAM;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAII,eAAe,oBAAoB,MAAM;AACzC,QAAM,EAAE,cAAc,CAAC,MAAI,EAAE,IAAI;AACjC,MAAI,SAAS,qBAAqB,KAAK,IAAI;AAC3C,MAAI,aAAa;AACb,aAAS,OAAO,YAAY,IAAI,gBAAgB;AAAA,MAC5C,UAAW,OAAO,YAAY;AAC1B,mBAAW,QAAQ,YAAY,KAAK,CAAC;AAAA,MACzC;AAAA,IACJ,CAAC,CAAC;AAAA,EACN;AACA,MAAI,eAAe,eAAe;AAClC,QAAM,gBAAgB,qBAAqB,KAAK,eAAe;AAC/D,WAAS,sBAAsB,OAAO;AAClC,UAAM,CAAC,OAAO,MAAM,OAAO,IAAI;AAC/B,UAAM,aAAa,cAAc,YAAY,OAAO;AACpD,YAAO,MAAK;AAAA,MACR,KAAK,0BACD;AACI,eAAO,IAAI,YAAU;AA1ezC,cAAAC;AA2ewB,gBAAM,MAAM;AAAA,YACR,OAAO,CAAC;AAAA,YACR,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AACA,cAAI;AACA,kBAAM,SAAS,4BAA4B,KAAK,WAAW,kBAAkB,GAAG,KAAK;AACrF;AACA,kBAAM,EAAE,OAAAC,OAAM,IAAI,MAAM,OAAO,KAAK;AACpC,kBAAM,CAAC,UAAU,QAAQ,IAAI,IAAIA;AACjC,oBAAO,QAAO;AAAA,cACV,KAAK;AACD,uBAAO,OAAO,IAAI;AAAA,cACtB,KAAK;AACD,wBAAMD,MAAA,KAAK,gBAAL,gBAAAA,IAAA,WAAmB;AAAA,kBACrB,OAAO;AAAA,gBACX,OAAM,IAAI,WAAW,IAAI;AAAA,YACjC;AAAA,UACJ,SAAS,GAAG;AACR,gBAAI,QAAQ;AACZ,gBAAI,WAAW;AAAA,UACnB,UAAE;AACE,kCAAsB,GAAG;AAAA,UAC7B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACJ,KAAK,iCACD;AACI,eAAO,IAAI,mBAAkB;AAvgBjD,cAAAA;AAwgBwB,gBAAM,MAAM;AAAA,YACR,OAAO,CAAC;AAAA,YACR,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AACA,cAAI;AACA,kBAAM,SAAS,4BAA4B,KAAK,WAAW,kBAAkB,GAAG,KAAK;AACrF;AACA,mBAAM,MAAK;AACP,oBAAM,EAAE,OAAAC,OAAM,IAAI,MAAM,OAAO,KAAK;AACpC,oBAAM,CAAC,UAAU,QAAQ,IAAI,IAAIA;AACjC,sBAAO,QAAO;AAAA,gBACV,KAAK;AACD,wBAAM,OAAO,IAAI;AACjB;AAAA,gBACJ,KAAK;AACD,yBAAO,OAAO,IAAI;AAAA,gBACtB,KAAK;AACD,0BAAMD,MAAA,KAAK,gBAAL,gBAAAA,IAAA,WAAmB;AAAA,oBACrB,OAAO;AAAA,kBACX,OAAM,IAAI,WAAW,IAAI;AAAA,cACjC;AAAA,YACJ;AAAA,UACJ,SAAS,GAAG;AACR,gBAAI,QAAQ;AACZ,gBAAI,WAAW;AAAA,UACnB,UAAE;AACE,kCAAsB,GAAG;AAAA,UAC7B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACR;AAAA,EACJ;AACA,WAAS,OAAO,OAAO;AACnB,UAAM,CAAC,CAAC,IAAI,GAAG,GAAG,UAAU,IAAI;AAChC,eAAWC,UAAS,YAAW;AAC3B,YAAM,CAAC,GAAG,IAAIA;AACd,YAAM,UAAU,sBAAsBA,MAAK;AAC3C,UAAI,QAAQ,MAAM;AACd,eAAO;AAAA,MACX;AACA,WAAK,GAAG,IAAI;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AACA,QAAM,eAAe,CAAC,WAAS;AAC3B,iDAAc,OAAO;AACrB,kBAAc,UAAU,MAAM;AAAA,EAClC;AACA,SAAO,OAAO,IAAI,eAAe;AAAA,IAC7B,MAAO,aAAa;AAChB,UAAI,cAAc;AACd,cAAM,OAAO;AACb,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAE;AACnD,gBAAM,SAAS,OAAO,KAAK;AAC3B,eAAK,GAAG,IAAI;AAAA,QAChB;AACA,qBAAa,QAAQ,IAAI;AACzB,uBAAe;AACf;AAAA,MACJ;AACA,YAAM,QAAQ;AACd,YAAM,CAAC,GAAG,IAAI;AACd,YAAM,aAAa,cAAc,YAAY,GAAG;AAChD,iBAAW,QAAQ,KAAK;AAAA,IAC5B;AAAA,IACA,OAAO,MAAI,aAAa,IAAI,MAAM,eAAe,CAAC;AAAA,IAClD,OAAO;AAAA,EACX,CAAC,GAAG;AAAA,IACA,QAAQ,KAAK,gBAAgB;AAAA,EACjC,CAAC,EAAE,MAAM,CAAC,UAAQ;AA9kBtB,QAAAD;AA+kBQ,KAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,MACX;AAAA,IACJ;AACA,iBAAa,KAAK;AAAA,EACtB,CAAC;AACD,SAAO;AAAA,IACH,MAAM,aAAa;AAAA,IACnB;AAAA,EACJ;AACJ;;;ACxlBA,IAAM,gBAAgB,OAAO;;;ACW7B,SAASE,6BAA4B,KAAK,OAAO,OAAO;AACpD,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI,SAAS;AACb,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IACvC;AACA,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAC9B,UAAI,MAAO,SAAQ;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAO,WAAU,WAAW;AAC5B,UAAI;AACA,cAAM,KAAK,IAAI;AAAA,MACnB,SAAS,GAAG;AACR,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B;AAAA,IACJ;AACA,QAAI,MAAM,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,WAAW,OAAO;AACd,QAAI,MAAM,KAAK;AAAA,MACX,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAASC,uBAAsB,KAAK;AAChC,MAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAS,OAAO,YAAY,SAAS;AAClH,QAAI,IAAI,IAAI,MAAM,OAAO;AACzB,WAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AAAA,EACnF;AACA,UAAQA,yBAAwB,SAASA,uBAAsBC,MAAK;AAChE,aAAS,KAAK,GAAG;AACb,MAAAA,KAAI,QAAQA,KAAI,WAAW,IAAI,iBAAiB,GAAGA,KAAI,OAAO,0CAA0C,IAAI;AAC5G,MAAAA,KAAI,WAAW;AAAA,IACnB;AACA,QAAI,GAAG,IAAI;AACX,aAAS,OAAO;AACZ,aAAM,IAAIA,KAAI,MAAM,IAAI,GAAE;AACtB,YAAI;AACA,cAAI,CAAC,EAAE,SAAS,MAAM,EAAG,QAAO,IAAI,GAAGA,KAAI,MAAM,KAAK,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,IAAI;AACrF,cAAI,EAAE,SAAS;AACX,gBAAI,SAAS,EAAE,QAAQ,KAAK,EAAE,KAAK;AACnC,gBAAI,EAAE,MAAO,QAAO,KAAK,GAAG,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AACvE,mBAAK,CAAC;AACN,qBAAO,KAAK;AAAA,YAChB,CAAC;AAAA,UACL,MAAO,MAAK;AAAA,QAChB,SAAS,GAAG;AACR,eAAK,CAAC;AAAA,QACV;AAAA,MACJ;AACA,UAAI,MAAM,EAAG,QAAOA,KAAI,WAAW,QAAQ,OAAOA,KAAI,KAAK,IAAI,QAAQ,QAAQ;AAC/E,UAAIA,KAAI,SAAU,OAAMA,KAAI;AAAA,IAChC;AACA,WAAO,KAAK;AAAA,EAChB,GAAG,GAAG;AACV;AACA,IAAM,aAAa;AACnB,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAsGrB,eAAe,YAAY,MAAM;AAC7B,QAAM,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,EACd;AACA,MAAI;AACA,UAAM,iBAAiBC,6BAA4B,KAAK,cAAc,KAAK,SAAS,GAAG,KAAK;AAC5F;AACA,UAAM,MAAM,MAAM,UAAU,KAAK;AAAA,MAC7B,KAAK;AAAA,MACL,eAAe,MAAM;AAAA,IACzB,CAAC;AACD,QAAI,QAAQ,8BAA8B;AACtC,aAAO,MAAM,KAAK,UAAU;AAAA,IAChC;AACA,WAAO;AAAA,EACX,SAAS,GAAG;AACR,QAAI,QAAQ;AACZ,QAAI,WAAW;AAAA,EACnB,UAAE;AACE,IAAAC,uBAAsB,GAAG;AAAA,EAC7B;AACJ;AAGI,SAAS,kBAAkB,MAAM;AACjC,QAAM,EAAE,cAAc,CAAC,MAAI,EAAE,IAAI;AACjC,MAAI,gBAAgB,CAAC;AACrB,QAAM,SAAS,KAAK;AACpB,MAAI,MAAM;AACV,QAAM,eAAe,MAAI,IAAI,eAAe;AAAA,IACpC,MAAM,MAAO,YAAY;AACrB,YAAM,CAAC,KAAK,IAAI,IAAI,MAAM,QAAQ,IAAI;AAAA,QAClC,KAAK,IAAI;AAAA,QACT,KAAK,KAAK;AAAA,MACd,CAAC;AACD,YAAM,cAAc,MAAM,IAAI,KAAK,YAAY,KAAK,IAAI;AACxD,iBAAW,QAAQ;AAAA,QACf,MAAM;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,MACX,CAAC;AACD,kBAAY,iBAAiB,iBAAiB,CAAC,SAAO;AAClD,cAAM,MAAM;AACZ,cAAM,UAAU,KAAK,MAAM,IAAI,IAAI;AACnC,wBAAgB;AAChB,mBAAW,QAAQ;AAAA,UACf,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,kBAAY,iBAAiB,wBAAwB,CAAC,SAAO;AACzD,cAAM,MAAM;AACZ,mBAAW,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,OAAO,YAAY,KAAK,MAAM,IAAI,IAAI,CAAC;AAAA,UACvC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,kBAAY,iBAAiB,YAAY,MAAI;AACzC,mBAAW,QAAQ;AAAA,UACf,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,kBAAY,iBAAiB,cAAc,MAAI;AAC3C,oBAAY,MAAM;AAClB,mBAAW,MAAM;AACjB,cAAM;AAAA,MACV,CAAC;AACD,kBAAY,iBAAiB,SAAS,CAAC,UAAQ;AAC3C,YAAI,YAAY,eAAe,YAAY,QAAQ;AAC/C,qBAAW,MAAM,KAAK;AAAA,QAC1B,OAAO;AACH,qBAAW,QAAQ;AAAA,YACf,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AACD,kBAAY,iBAAiB,WAAW,CAAC,SAAO;AAC5C,cAAM,MAAM;AACZ,cAAM,QAAQ,YAAY,KAAK,MAAM,IAAI,IAAI,CAAC;AAC9C,cAAM,MAAM;AAAA,UACR,MAAM;AAAA,QACV;AACA,YAAI,IAAI,aAAa;AACjB,cAAI,KAAK,IAAI;AAAA,QACjB;AACA,mBAAW,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,YAAM,UAAU,MAAI;AAChB,YAAI;AACA,sBAAY,MAAM;AAClB,qBAAW,MAAM;AAAA,QACrB,QAAS;AAAA,QAET;AAAA,MACJ;AACA,UAAI,OAAO,SAAS;AAChB,gBAAQ;AAAA,MACZ,OAAO;AACH,eAAO,iBAAiB,SAAS,OAAO;AAAA,MAC5C;AAAA,IACJ;AAAA,IACA,SAAU;AACN,iCAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL,QAAM,oBAAoB,MAAI;AAC1B,QAAI,SAAS,aAAa;AAC1B,QAAI,SAAS,OAAO,UAAU;AAC9B,mBAAe,UAAU;AACrB,YAAM,OAAO,OAAO;AACpB,YAAM;AAAA,IACV;AACA,WAAO,kBAAkB;AAAA,MACrB,OAAQ;AACJ,eAAO,OAAO,KAAK;AAAA,MACvB;AAAA,MACA,MAAM,WAAY;AACd,cAAM,QAAQ;AACd,iBAAS,aAAa;AACtB,iBAAS,OAAO,UAAU;AAAA,MAC9B;AAAA,IACJ,GAAG,OAAO;AAAA,EACd;AACA,SAAO,IAAI,mBAAkB;AACzB,UAAM,MAAM;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,IACd;AACA,QAAI;AACA,YAAM,SAASD,6BAA4B,KAAK,kBAAkB,GAAG,IAAI;AACzE;AACA,aAAM,MAAK;AACP,YAAI,UAAU,OAAO,KAAK;AAC1B,cAAM,YAAY,cAAc;AAChC,YAAI,WAAW;AACX,oBAAU,YAAY;AAAA,YAClB;AAAA,YACA;AAAA,YACA,WAAW,YAAU;AACjB,oBAAM,MAAM;AAAA,gBACR,OAAO;AAAA,kBACH,MAAM;AAAA,kBACN,IAAI;AAAA,kBACJ,aAAa;AAAA,gBACjB;AAAA,gBACA,MAAM;AAAA,cACV;AAEA,oBAAM,OAAO,SAAS;AACtB,qBAAO;AAAA,YACX;AAAA,UACJ,CAAC;AAAA,QACL;AACA,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,MAAM;AACb,iBAAO,OAAO;AAAA,QAClB;AACA,cAAM,OAAO;AAAA,MACjB;AAAA,IACJ,SAAS,GAAG;AACR,UAAI,QAAQ;AACZ,UAAI,WAAW;AAAA,IACnB,UAAE;AACE,YAAM,SAASC,uBAAsB,GAAG;AACxC,UAAI,OAAQ,OAAM;AAAA,IACtB;AAAA,EACJ,CAAC;AACL;;;ACrWiB,IAAM,mBAAmB;AAGtC,SAAS,0BAA0B;AACnC,WAAS,sBAAsB,aAAa;AACxC,WAAO;AAAA,MACH,cAAc;AAAA,MACd,cAAe,uBAAuB;AAClC,cAAM,kBAAkB,kBAAkB,wBAAwB,sBAAsB,eAAe;AAAA,UACnG;AAAA,QACJ;AACA,eAAO,sBAAsB;AAAA,UACzB,GAAG;AAAA,UACH,GAAG;AAAA,QACP,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,iBAAiB,IAAI;AAC1B,WAAO,sBAAsB;AAAA,MACzB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAWI,SAAS,sBAAsB,OAAO;AACtC,QAAM,kBAAkB,eAAe,yBAAyB,MAAM;AAClE,QAAI;AACJ,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI;AACA,oBAAc,MAAM,MAAM,QAAQ;AAAA,IACtC,SAAS,OAAO;AACZ,YAAM,IAAI,UAAU;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,UAAM,gBAAgB,SAAS,KAAK,KAAK,KAAK,SAAS,WAAW,IAAI;AAAA,MAClE,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACP,IAAI;AACJ,WAAO,KAAK,KAAK;AAAA,MACb,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,kBAAgB,QAAQ;AACxB,SAAO;AACX;AAGI,SAAS,uBAAuB,OAAO;AACvC,QAAM,mBAAmB,eAAe,0BAA0B,EAAE,KAAK,GAAG;AACxE,UAAM,SAAS,MAAM,KAAK;AAC1B,QAAI,CAAC,OAAO,IAAI;AAEZ,aAAO;AAAA,IACX;AACA,QAAI;AACA,YAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AACpC,aAAO;AAAA,QACH,GAAG;AAAA,QACH;AAAA,MACJ;AAAA,IACJ,SAAS,OAAO;AACZ,YAAM,IAAI,UAAU;AAAA,QAChB,SAAS;AAAA,QACT,MAAM;AAAA,QACN;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,mBAAiB,QAAQ;AACzB,SAAO;AACX;;;ACvFA,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AAC+C,IAAM,wBAAN,cAAoC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnF,YAAY,QAAO;AAlBzB,QAAAC;AAmBQ,WAAMA,MAAA,OAAO,CAAC,MAAR,gBAAAA,IAAW,OAAO;AAAA,IAA6BD,kBAAiB,MAAM,UAAU,MAAM;AAC5F,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAClB;AACJ;;;ACrBA,SAAS,WAAW,iBAAiB;AACjC,QAAM,SAAS;AACf,QAAM,mBAAmB,eAAe;AACxC,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,WAAW,YAAY;AAErE,WAAO,OAAO,OAAO,KAAK,MAAM;AAAA,EACpC;AACA,MAAI,OAAO,WAAW,cAAc,CAAC,kBAAkB;AAGnD,WAAO;AAAA,EACX;AACA,MAAI,OAAO,OAAO,eAAe,YAAY;AAEzC,WAAO,OAAO,WAAW,KAAK,MAAM;AAAA,EACxC;AACA,MAAI,OAAO,OAAO,UAAU,YAAY;AAGpC,WAAO,OAAO,MAAM,KAAK,MAAM;AAAA,EACnC;AACA,MAAI,OAAO,OAAO,iBAAiB,YAAY;AAE3C,WAAO,OAAO,aAAa,KAAK,MAAM;AAAA,EAC1C;AACA,MAAI,OAAO,OAAO,WAAW,YAAY;AAErC,WAAO,OAAO,OAAO,KAAK,MAAM;AAAA,EACpC;AACA,MAAI,OAAO,OAAO,WAAW,YAAY;AAErC,WAAO,CAAC,UAAQ;AACZ,aAAO,OAAO,KAAK;AACnB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,kBAAkB;AAElB,WAAO,OAAO,UAAQ;AAClB,YAAM,SAAS,MAAM,OAAO,WAAW,EAAE,SAAS,KAAK;AACvD,UAAI,OAAO,QAAQ;AACf,cAAM,IAAI,sBAAsB,OAAO,MAAM;AAAA,MACjD;AACA,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,+BAA+B;AACnD;;;AC5CA,SAAS,iBAAiB,MAAM,MAAM;AAClC,QAAM,EAAE,cAAc,CAAC,GAAG,QAAQ,MAAM,GAAG,KAAK,IAAI;AAEpD,SAAO,cAAc;AAAA,IACjB,GAAG,sBAAsB,MAAM,IAAI;AAAA,IACnC,QAAQ;AAAA,MACJ,GAAG,KAAK;AAAA,MACR,GAAG,UAAU,CAAC;AAAA,IAClB;AAAA,IACA,aAAa;AAAA,MACT,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACP;AAAA,IACA,MAAM,KAAK,QAAQ,OAAO;AAAA,MACtB,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACP,IAAI,QAAQ,KAAK;AAAA,EACrB,CAAC;AACL;AACA,SAAS,cAAc,UAAU,CAAC,GAAG;AACjC,QAAM,OAAO;AAAA,IACT,WAAW;AAAA,IACX,QAAQ,CAAC;AAAA,IACT,aAAa,CAAC;AAAA,IACd,GAAG;AAAA,EACP;AACA,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,MAAO,OAAO;AACV,YAAM,SAAS,WAAW,KAAK;AAC/B,aAAO,iBAAiB,MAAM;AAAA,QAC1B,QAAQ;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,aAAa;AAAA,UACT,sBAAsB,MAAM;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,OAAQ,QAAQ;AACZ,YAAM,SAAS,WAAW,MAAM;AAChC,aAAO,iBAAiB,MAAM;AAAA,QAC1B;AAAA,QACA,aAAa;AAAA,UACT,uBAAuB,MAAM;AAAA,QACjC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAM,MAAM;AACR,aAAO,iBAAiB,MAAM;AAAA,QAC1B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,IAAK,uBAAuB;AAExB,YAAM,cAAc,kBAAkB,wBAAwB,sBAAsB,eAAe;AAAA,QAC/F;AAAA,MACJ;AACA,aAAO,iBAAiB,MAAM;AAAA,QAC1B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,gBAAiBE,UAAS;AACtB,aAAO,iBAAiB,MAAMA,SAAQ,IAAI;AAAA,IAC9C;AAAA,IACA,OAAQA,UAAS;AACb,aAAO,iBAAiB,MAAMA,SAAQ,IAAI;AAAA,IAC9C;AAAA,IACA,MAAO,UAAU;AACb,aAAO,eAAe;AAAA,QAClB,GAAG;AAAA,QACH,MAAM;AAAA,MACV,GAAG,QAAQ;AAAA,IACf;AAAA,IACA,SAAU,UAAU;AAChB,aAAO,eAAe;AAAA,QAClB,GAAG;AAAA,QACH,MAAM;AAAA,MACV,GAAG,QAAQ;AAAA,IACf;AAAA,IACA,aAAc,UAAU;AACpB,aAAO,eAAe;AAAA,QAClB,GAAG;AAAA,QACH,MAAM;AAAA,MACV,GAAG,QAAQ;AAAA,IACf;AAAA,IACA,oBAAqB,QAAQ;AACzB,aAAO,iBAAiB,MAAM;AAAA,QAC1B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,UAAU;AACtC,QAAM,eAAe,iBAAiB,QAAQ;AAAA,IAC1C;AAAA,IACA,aAAa;AAAA,MACT,eAAe,kBAAkB,MAAM;AACnC,cAAM,OAAO,MAAM,SAAS,IAAI;AAChC,eAAO;AAAA,UACH,QAAQ;AAAA,UACR,IAAI;AAAA,UACJ;AAAA,UACA,KAAK,KAAK;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,OAAO;AAAA,IACT,GAAG,aAAa;AAAA,IAChB,MAAM,OAAO;AAAA,IACb,qBAAqB,QAAQ,aAAa,KAAK,MAAM;AAAA,IACrD,MAAM,aAAa,KAAK;AAAA,IACxB,QAAQ;AAAA,EACZ;AACA,QAAM,SAAS,sBAAsB,aAAa,IAAI;AACtD,QAAM,iBAAiB,aAAa,KAAK;AACzC,MAAI,CAAC,gBAAgB;AACjB,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,UAAU,SAAO;AACnC,WAAO,MAAM,eAAe;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACA,gBAAc,OAAO;AACrB,SAAO;AACX;AACA,IAAM,YAAY;AAAA;AAAA;AAAA,EAGhB,KAAK;AAEP,eAAe,cAAc,OAAO,MAAM,MAAM;AAC5C,MAAI;AAEA,UAAM,aAAa,KAAK,YAAY,KAAK;AACzC,UAAM,SAAS,MAAM,WAAW;AAAA,MAC5B,GAAG;AAAA,MACH,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,KAAM,WAAW;AACb,cAAM,WAAW;AACjB,eAAO,cAAc,QAAQ,GAAG,MAAM;AAAA,UAClC,GAAG;AAAA,UACH,MAAK,qCAAU,OAAM;AAAA,YACjB,GAAG,KAAK;AAAA,YACR,GAAG,SAAS;AAAA,UAChB,IAAI,KAAK;AAAA,UACT,OAAO,YAAY,WAAW,WAAW,SAAS,QAAQ,KAAK;AAAA,UAC/D,cAAa,qCAAU,gBAAe,KAAK;AAAA,QAC/C,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,SAAS,OAAO;AACZ,WAAO;AAAA,MACH,IAAI;AAAA,MACJ,OAAO,wBAAwB,KAAK;AAAA,MACpC,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,MAAM;AACjC,iBAAe,UAAU,MAAM;AAE3B,QAAI,CAAC,QAAQ,EAAE,iBAAiB,OAAO;AACnC,YAAM,IAAI,MAAM,SAAS;AAAA,IAC7B;AAEA,UAAM,SAAS,MAAM,cAAc,GAAG,MAAM,IAAI;AAChD,QAAI,CAAC,QAAQ;AACT,YAAM,IAAI,UAAU;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,QAAI,CAAC,OAAO,IAAI;AAEZ,YAAM,OAAO;AAAA,IACjB;AACA,WAAO,OAAO;AAAA,EAClB;AACA,YAAU,OAAO;AACjB,YAAU,YAAY;AACtB,YAAU,OAAO,KAAK;AAEtB,SAAO;AACX;;;ACpMA;AAEI,IAAM,kBAAkB,OAAO,WAAW,eAAe,UAAU;AAAA,EACvE,sBAAW,YAAX,mBAAoB,QAApB,mBAA0B,iBAAgB,UAAU,CAAC,GAAC,sBAAW,YAAX,mBAAoB,QAApB,mBAA0B,sBAAqB,CAAC,GAAC,sBAAW,YAAX,mBAAoB,QAApB,mBAA0B;;;ACIjI,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAIZ,UAAU;AACR,WAAO,IAAI,aAAY;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAIE,OAAO;AACL,WAAO,IAAI,aAAY;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAIE,OAAO,MAAM;AAvBnB,QAAAC;AAwBQ,UAAM,SAAS;AAAA,MACX,GAAG;AAAA,MACH,aAAa,oBAAmB,6BAAM,gBAAe,kBAAkB;AAAA,MACvE,QAAO,6BAAM;AAAA,QACbA,MAAA,WAAW,YAAX,gBAAAA,IAAoB,IAAI,iBAAgB;AAAA,MACxC,uBAAsB,6BAAM,yBAAwB;AAAA,MACpD,iBAAgB,6BAAM,mBAAkB;AAAA,MACxC,WAAU,6BAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAI9B,QAAQ;AAAA,IACV;AACA;AAEI,YAAM,YAAW,6BAAM,aAAY;AACnC,UAAI,CAAC,aAAY,6BAAM,0BAAyB,MAAM;AAClD,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACtH;AAAA,IACJ;AACA,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAIL,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,MAIT,WAAW,cAAc;AAAA,QACnB,MAAM,6BAAM;AAAA,MAChB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAIH,YAAY,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,MAIpC,QAAQ,oBAAoB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,MAIlC;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA,qBAAqB,oBAAoB;AAAA,IAC3C;AAAA,EACJ;AACJ;AAII,IAAM,WAAW,IAAI,YAAY;;;AC3ErC,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAGpB,iBAAiB,SAAS,MAAM,SAAS;AACjD;AACA,SAAS,oBAAoB,KAAK;AAC9B,SAAO,SAAS,GAAG,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,OAAO,IAAI,OAAO,EAAE,MAAM,MAAM,YAAY,OAAO,IAAI,OAAO,EAAE,SAAS,MAAM;AACrI;AACA,SAAS,2BAA2B,KAAK,UAAU;AAC/C,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO;AAAA,EACX;AACA,MAAI,SAAS,GAAG,KAAK,OAAO,IAAI,SAAS,MAAM,UAAU;AACrD,WAAO,IAAI,SAAS;AAAA,EACxB;AACA,SAAO;AACX;AACA,IAAM,kBAAN,MAAM,yBAAwB,MAAM;AAAA,EAChC,OAAO,KAAK,QAAQ,OAAO,CAAC,GAAG;AAC3B,UAAM,QAAQ;AACd,QAAI,kBAAkB,KAAK,GAAG;AAC1B,UAAI,KAAK,MAAM;AAEX,cAAM,OAAO;AAAA,UACT,GAAG,MAAM;AAAA,UACT,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAI,oBAAoB,KAAK,GAAG;AAC5B,aAAO,IAAI,iBAAgB,MAAM,MAAM,SAAS;AAAA,QAC5C,GAAG;AAAA,QACH,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AACA,WAAO,IAAI,iBAAgB,2BAA2B,OAAO,eAAe,GAAG;AAAA,MAC3E,GAAG;AAAA,MACH;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,YAAY,SAAS,MAAK;AAzD9B,QAAAC,KAAAC;AA0DQ,UAAM,QAAQ,6BAAM;AAGpB,UAAM,SAAS;AAAA,MACX;AAAA,IACJ,CAAC;AAAA;AAAA,IAEDF,kBAAiB,MAAM,SAAS,MAAM,GAAGA,kBAAiB,MAAM,SAAS,MAAM,GAAGA,kBAAiB,MAAM,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA,IAGzHA,kBAAiB,MAAM,QAAQ,MAAM;AACnC,SAAK,OAAO,6BAAM;AAClB,SAAK,QAAQ;AACb,SAAK,SAAQC,MAAA,6BAAM,WAAN,gBAAAA,IAAc;AAC3B,SAAK,QAAOC,MAAA,6BAAM,WAAN,gBAAAA,IAAc,MAAM;AAChC,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,iBAAgB,SAAS;AAAA,EACzD;AACJ;;;ACxEA,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAM,oBAAN,MAAwB;AAAA,EACpB,SAAS,MAAM;AACX,UAAM,SAAS,YAAY;AAAA,MACvB,OAAO,KAAK;AAAA,MACZ,IAAI;AAAA,QACA,GAAG;AAAA,QACH,SAAS,KAAK,WAAW,CAAC;AAAA,QAC1B,IAAI,EAAE,KAAK;AAAA,MACf;AAAA,IACJ,CAAC;AACD,WAAO,OAAO,KAAK,MAAM,CAAC;AAAA,EAC9B;AAAA,EACA,MAAM,iBAAiB,MAAM;AACzB,QAAI;AACA,YAAM,OAAO,KAAK,SAAS,IAAI;AAC/B,YAAM,WAAW,MAAM,oBAAoB,IAAI;AAC/C,YAAM,OAAO,SAAS,OAAO;AAC7B,aAAO;AAAA,IACX,SAAS,KAAK;AACV,YAAM,gBAAgB,KAAK,GAAG;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,OAAO,MAAM;AACrB,WAAO,KAAK,iBAAiB;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,SAAS,6BAAM;AAAA,MACf,QAAQ,6BAAM;AAAA,IAClB,CAAC;AAAA,EACL;AAAA,EACA,SAAS,MAAM,OAAO,MAAM;AACxB,WAAO,KAAK,iBAAiB;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,SAAS,6BAAM;AAAA,MACf,QAAQ,6BAAM;AAAA,IAClB,CAAC;AAAA,EACL;AAAA,EACA,aAAa,MAAM,OAAO,MAAM;AAC5B,UAAM,cAAc,KAAK,SAAS;AAAA,MAC9B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACjB,CAAC;AACD,WAAO,YAAY,UAAU;AAAA,MACzB,KAAM,UAAU;AAlE5B,YAAAC,KAAAC,KAAAC,KAAAC;AAmEgB,gBAAO,SAAS,OAAO,MAAK;AAAA,UACxB,KAAK,SACD;AACI,aAAAH,MAAA,KAAK,4BAAL,gBAAAA,IAAA,WAA+B,SAAS;AACxC;AAAA,UACJ;AAAA,UACJ,KAAK,WACD;AACI,aAAAC,MAAA,KAAK,cAAL,gBAAAA,IAAA,WAAiB;AAAA,cACb,SAAS,SAAS;AAAA,YACtB;AACA;AAAA,UACJ;AAAA,UACJ,KAAK,WACD;AACI,aAAAC,MAAA,KAAK,cAAL,gBAAAA,IAAA;AACA;AAAA,UACJ;AAAA,UACJ,KAAK;AAAA,UACL,KAAK,QACD;AACI,aAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,SAAS,OAAO;AAC9B;AAAA,UACJ;AAAA,QACR;AAAA,MACJ;AAAA,MACA,MAAO,KAAK;AA7FxB,YAAAH;AA8FgB,SAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,MACnB;AAAA,MACA,WAAY;AAhGxB,YAAAA;AAiGgB,SAAAA,MAAA,KAAK,eAAL,gBAAAA,IAAA;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,YAAY,MAAK;AACb,IAAAD,kBAAiB,MAAM,SAAS,MAAM;AACtC,IAAAA,kBAAiB,MAAM,WAAW,MAAM;AACxC,IAAAA,kBAAiB,MAAM,aAAa,MAAM;AAC1C,SAAK,YAAY;AACjB,SAAK,UAAU,CAAC;AAEhB,SAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,SAAO,KAAK,KAAK,OAAO,CAAC;AAAA,EAC1D;AACJ;;;AC5GA,SAAS,wBAAwB,MAAM;AACnC,SAAO,IAAI,kBAAkB,IAAI;AACrC;;;ACDA,IAAM,sBAAsB,OAAO,IAAI,oBAAoB;AAC3D,IAAM,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AACf;AACiB,IAAM,gCAAgC,CAAC,mBAAiB;AACrE,SAAO,kBAAkB,cAAc;AAC3C;AAGI,SAAS,sBAAsB,QAAQ;AACvC,QAAM,QAAQ,qBAAqB,CAAC,EAAE,MAAM,KAAK,MAAI;AACjD,UAAM,WAAW;AAAA,MACb,GAAG;AAAA,IACP;AACA,UAAM,gBAAgB,8BAA8B,SAAS,IAAI,CAAC;AAClE,UAAM,WAAW,SAAS,KAAK,GAAG;AAClC,WAAO,OAAO,aAAa,EAAE,UAAU,GAAG,IAAI;AAAA,EAClD,CAAC;AACD,SAAO,gBAAgB,CAAC,QAAM;AAC1B,QAAI,QAAQ,qBAAqB;AAC7B,aAAO;AAAA,IACX;AACA,WAAO,MAAM,GAAG;AAAA,EACpB,CAAC;AACL;AACA,SAAS,iBAAiB,MAAM;AAC5B,QAAM,SAAS,IAAI,kBAAkB,IAAI;AACzC,QAAM,QAAQ,sBAAsB,MAAM;AAC1C,SAAO;AACX;AAII,SAAS,iBAAiB,QAAQ;AAClC,SAAO,OAAO,mBAAmB;AACrC;;;ACxCA,IAAMK,cAAa,CAAC,OAAK,OAAO,OAAO;AACvC,SAAS,SAAS,iBAAiB;AAC/B,MAAI,iBAAiB;AACjB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,eAAeA,YAAW,OAAO,KAAK,GAAG;AAC3D,WAAO,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,eAAe,eAAeA,YAAW,WAAW,KAAK,GAAG;AACnE,WAAO,WAAW;AAAA,EACtB;AACA,QAAM,IAAI,MAAM,+BAA+B;AACnD;;;ACZA,SAAS,YAAY,OAAO;AACxB,SAAO,iBAAiB;AAAA,EACxB,iBAAiB;AACrB;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,iBAAiB;AAC5B;AACA,SAAS,sBAAsB,OAAO;AAClC,SAAO,YAAY,KAAK,KAAK,WAAW,KAAK;AACjD;;;ACPI,IAAM,kBAAkB,MAAI;AAC5B,QAAM,IAAI,MAAM,yFAAyF;AAC7G;AAKI,SAAS,WAAW,aAAa;AACjC,MAAI,eAAe;AACnB,MAAI,gBAAgB;AACpB,QAAM,8BAA8B,MAAI;AACpC,iBAAa,aAAa;AAC1B,oBAAgB;AAChB,mBAAe;AAAA,EACnB;AAGE,WAAS,WAAW,OAAO;AAnBjC,QAAAC,KAAAC;AAoBQ,UAAM,eAAe;AAAA,MACjB,CAAC;AAAA,IACL;AACA,QAAI,QAAQ;AACZ,WAAM,MAAK;AACP,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AACA,YAAM,YAAY,aAAa,aAAa,SAAS,CAAC;AACtD,UAAI,KAAK,SAAS;AAEd,SAAAD,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,SAAS;AACjC;AACA;AAAA,MACJ;AACA,YAAM,UAAU,YAAY,SAAS,UAAU,OAAO,IAAI,EAAE,IAAI,CAAC,OAAK,GAAG,GAAG,CAAC;AAC7E,UAAI,SAAS;AACT,kBAAU,KAAK,IAAI;AACnB;AACA;AAAA,MACJ;AACA,UAAI,UAAU,WAAW,GAAG;AACxB,SAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,wCAAwC;AAChE;AACA;AAAA,MACJ;AAEA,mBAAa,KAAK,CAAC,CAAC;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW;AAChB,UAAM,eAAe,WAAW,YAAY;AAC5C,gCAA4B;AAE5B,eAAW,SAAS,cAAa;AAC7B,UAAI,CAAC,MAAM,QAAQ;AACf;AAAA,MACJ;AACA,YAAM,QAAQ;AAAA,QACV;AAAA,MACJ;AACA,iBAAW,QAAQ,OAAM;AACrB,aAAK,QAAQ;AAAA,MACjB;AACA,YAAM,UAAU,YAAY,MAAM,MAAM,MAAM,IAAI,CAAC,UAAQ,MAAM,GAAG,CAAC;AACrE,cAAQ,KAAK,OAAO,WAAS;AAnEzC,YAAAD;AAoEgB,cAAM,QAAQ,IAAI,OAAO,IAAI,OAAO,gBAAgB,UAAQ;AApE5E,cAAAA,KAAAC;AAqEoB,gBAAM,OAAO,MAAM,MAAM,KAAK;AAC9B,cAAI;AACA,kBAAM,QAAQ,MAAM,QAAQ,QAAQ,cAAc;AAClD,aAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,UACnB,SAAS,OAAO;AACZ,aAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc;AAAA,UAClB;AACA,eAAK,QAAQ;AACb,eAAK,SAAS;AACd,eAAK,UAAU;AAAA,QACnB,CAAC,CAAC;AACF,mBAAW,QAAQ,MAAM,OAAM;AAC3B,WAAAD,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,gBAAgB;AACxC,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ,CAAC,EAAE,MAAM,CAAC,UAAQ;AApF9B,YAAAA;AAqFgB,mBAAW,QAAQ,MAAM,OAAM;AAC3B,WAAAA,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc;AACd,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,WAAS,KAAK,KAAK;AACf,UAAM,OAAO;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AACA,UAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAS;AAC3C,WAAK,SAAS;AACd,WAAK,UAAU;AACf,uBAAiB,eAAe,CAAC;AACjC,mBAAa,KAAK,IAAI;AAAA,IAC1B,CAAC;AACD,sBAAkB,gBAAgB,WAAW,QAAQ;AACrD,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;;;AC5GI,SAAS,mBAAmB,SAAS;AACrC,QAAM,KAAK,IAAI,gBAAgB;AAC/B,QAAM,QAAQ,QAAQ;AACtB,MAAI,eAAe;AACnB,QAAM,UAAU,MAAI;AAChB,QAAI,EAAE,iBAAiB,OAAO;AAC1B,SAAG,MAAM;AAAA,IACb;AAAA,EACJ;AACA,aAAW,UAAU,SAAQ;AACzB,QAAI,iCAAQ,SAAS;AACjB,cAAQ;AAAA,IACZ,OAAO;AACH,uCAAQ,iBAAiB,SAAS,SAAS;AAAA,QACvC,MAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,GAAG;AACd;AAMI,SAAS,oBAAoB,SAAS;AACtC,QAAM,KAAK,IAAI,gBAAgB;AAC/B,aAAW,UAAU,SAAQ;AACzB,QAAI,iCAAQ,SAAS;AACjB,SAAG,MAAM;AAAA,IACb,OAAO;AACH,uCAAQ,iBAAiB,SAAS,MAAI,GAAG,MAAM,GAAG;AAAA,QAC9C,MAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,GAAG;AACd;;;ACrCI,SAAS,eAAe,aAAa;AACrC,QAAM,eAAe;AACrB,MAAI,CAAC,cAAc;AACf,WAAO;AAAA,MACH,OAAO;AAAA,QACH,WAAW,CAAC,SAAO;AAAA,QACnB,aAAa,CAAC,SAAO;AAAA,MACzB;AAAA,MACA,QAAQ;AAAA,QACJ,WAAW,CAAC,SAAO;AAAA,QACnB,aAAa,CAAC,SAAO;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,WAAW,cAAc;AACzB,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AACJ;;;ACtBA,SAAS,uBAAuB,MAAM;AAClC,SAAO;AAAA,IACH,KAAK,KAAK,IAAI,SAAS;AAAA,IACvB,OAAO,KAAK;AAAA,IACZ,aAAa,eAAe,KAAK,WAAW;AAAA,IAC5C,gBAAgB,KAAK;AAAA,EACzB;AACJ;AAEA,SAAS,YAAY,OAAO;AACxB,QAAM,OAAO,CAAC;AACd,WAAQ,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAQ;AAC7C,UAAM,UAAU,MAAM,KAAK;AAC3B,SAAK,KAAK,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AAClB;AACA,SAAS,SAAS,MAAM;AACpB,SAAO,WAAW,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,KAAK,IAAI,YAAY,KAAK,OAAO,IAAI,CAAC,WAAS,KAAK,YAAY,MAAM,UAAU,MAAM,CAAC,CAAC;AAC3J;AACA,IAAM,SAAS,CAAC,SAAO;AACnB,QAAM,QAAQ,KAAK,IAAI,MAAM,GAAG;AAChC,QAAM,OAAO,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE;AACvC,MAAI,MAAM,OAAO,MAAM,KAAK;AAC5B,QAAM,aAAa,CAAC;AACpB,MAAI,MAAM,CAAC,GAAG;AACV,eAAW,KAAK,MAAM,CAAC,CAAC;AAAA,EAC5B;AACA,MAAI,YAAY,MAAM;AAClB,eAAW,KAAK,SAAS;AAAA,EAC7B;AACA,MAAI,KAAK,SAAS,WAAW,KAAK,SAAS,gBAAgB;AACvD,UAAM,QAAQ,SAAS,IAAI;AAC3B,QAAI,UAAU,UAAa,KAAK,mBAAmB,QAAQ;AACvD,iBAAW,KAAK,SAAS,mBAAmB,KAAK,UAAU,KAAK,CAAC,CAAC,EAAE;AAAA,IACxE;AAAA,EACJ;AACA,MAAI,WAAW,QAAQ;AACnB,WAAO,MAAM,WAAW,KAAK,GAAG;AAAA,EACpC;AACA,SAAO;AACX;AACA,IAAM,UAAU,CAAC,SAAO;AACpB,MAAI,KAAK,SAAS,WAAW,KAAK,mBAAmB,QAAQ;AACzD,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,SAAS,IAAI;AAC3B,SAAO,UAAU,SAAY,KAAK,UAAU,KAAK,IAAI;AACzD;AACA,IAAM,oBAAoB,CAAC,SAAO;AAC9B,SAAO,YAAY;AAAA,IACf,GAAG;AAAA,IACH,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAGI,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC/B,cAAa;AACT,UAAM,OAAO;AACb,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACnB;AACJ;AAKI,IAAM,iBAAiB,CAAC,WAAS;AA/ErC,MAAAE;AAgFI,MAAI,EAAC,iCAAQ,UAAS;AAClB;AAAA,EACJ;AAEA,GAAAA,MAAA,OAAO,mBAAP,gBAAAA,IAAA;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACrC,UAAM,IAAI,aAAa,cAAc,YAAY;AAAA,EACrD;AAEA,QAAM,IAAI,WAAW;AACzB;AACA,eAAe,kBAAkB,MAAM;AACnC,iBAAe,KAAK,MAAM;AAC1B,QAAM,MAAM,KAAK,OAAO,IAAI;AAC5B,QAAM,OAAO,KAAK,QAAQ,IAAI;AAC9B,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,kBAAkB,OAAO,YAAU;AACrC,UAAM,QAAQ,MAAM,KAAK,QAAQ;AACjC,QAAI,OAAO,YAAY,OAAO;AAC1B,aAAO,OAAO,YAAY,KAAK;AAAA,IACnC;AACA,WAAO;AAAA,EACX,GAAG;AACH,QAAM,UAAU;AAAA,IACZ,GAAG,KAAK,oBAAoB;AAAA,MACxB,gBAAgB,KAAK;AAAA,IACzB,IAAI,CAAC;AAAA,IACL,GAAG,KAAK,mBAAmB;AAAA,MACvB,eAAe,KAAK;AAAA,IACxB,IAAI;AAAA,IACJ,GAAG;AAAA,EACP;AACA,SAAO,SAAS,KAAK,KAAK,EAAE,KAAK;AAAA,IAC7B,QAAQ,KAAK,kBAAkB,OAAO,IAAI;AAAA,IAC1C,QAAQ,KAAK;AAAA,IACb;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AACA,eAAe,YAAY,MAAM;AAC7B,QAAM,OAAO,CAAC;AACd,QAAM,MAAM,MAAM,kBAAkB,IAAI;AACxC,OAAK,WAAW;AAChB,QAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,OAAK,eAAe;AACpB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;;;ACzHI,SAAS,cAAc,MAAM;AAC7B,QAAM,eAAe,uBAAuB,IAAI;AAChD,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,WAAW,KAAK,YAAY;AAClC,SAAO,MAAI;AACP,UAAM,cAAc,CAAC,SAAO;AACxB,aAAO;AAAA,QACH,SAAU,UAAU;AAChB,cAAI,iBAAiB,YAAY,aAAa,UAAU;AAEpD,mBAAO;AAAA,UACX;AACA,cAAI,SAAS,SAAS,UAAU;AAC5B,mBAAO;AAAA,UACX;AACA,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAK,GAAG,IAAI,EAAE,KAAK,GAAG;AACjD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAK,GAAG,KAAK;AAC1C,gBAAM,MAAM,OAAO;AAAA,YACf,GAAG;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC;AACD,iBAAO,IAAI,UAAU;AAAA,QACzB;AAAA,QACA,MAAM,MAAO,UAAU;AACnB,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAK,GAAG,IAAI,EAAE,KAAK,GAAG;AACjD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAK,GAAG,KAAK;AAC1C,gBAAM,SAAS,gBAAgB,GAAG,SAAS,IAAI,CAAC,OAAK,GAAG,MAAM,CAAC;AAC/D,gBAAM,MAAM,MAAM,kBAAkB;AAAA,YAChC,GAAG;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAW;AACP,kBAAI,CAAC,KAAK,SAAS;AACf,uBAAO,CAAC;AAAA,cACZ;AACA,kBAAI,OAAO,KAAK,YAAY,YAAY;AACpC,uBAAO,KAAK,QAAQ;AAAA,kBAChB,QAAQ;AAAA,gBACZ,CAAC;AAAA,cACL;AACA,qBAAO,KAAK;AAAA,YAChB;AAAA,YACA;AAAA,UACJ,CAAC;AACD,gBAAM,UAAU,MAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,OAAO,SAAS,IAAI,MAAI,IAAI,IAAI;AAC9E,gBAAM,SAAS,QAAQ,IAAI,CAAC,UAAQ;AAAA,YAC5B,MAAM,IAAI;AAAA,YACV,MAAM;AAAA,UACV,EAAE;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,QAAQ,WAAW,YAAY,OAAO,CAAC;AAC7C,UAAM,WAAW,WAAW,YAAY,UAAU,CAAC;AACnD,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,WAAO,CAAC,EAAE,GAAG,MAAI;AACb,aAAO,WAAW,CAAC,aAAW;AACY,YAAI,GAAG,SAAS,gBAAgB;AAClE,gBAAM,IAAI,MAAM,sFAAsF;AAAA,QAC1G;AACA,cAAM,SAAS,QAAQ,GAAG,IAAI;AAC9B,cAAM,UAAU,OAAO,KAAK,EAAE;AAC9B,YAAI,OAAO;AACX,gBAAQ,KAAK,CAAC,QAAM;AAChB,iBAAO;AACP,gBAAM,cAAc,gBAAgB,IAAI,MAAM,aAAa,YAAY,MAAM;AAC7E,cAAI,CAAC,YAAY,IAAI;AACjB,qBAAS,MAAM,gBAAgB,KAAK,YAAY,OAAO;AAAA,cACnD,MAAM,IAAI;AAAA,YACd,CAAC,CAAC;AACF;AAAA,UACJ;AACA,mBAAS,KAAK;AAAA,YACV,SAAS,IAAI;AAAA,YACb,QAAQ,YAAY;AAAA,UACxB,CAAC;AACD,mBAAS,SAAS;AAAA,QACtB,CAAC,EAAE,MAAM,CAAC,QAAM;AACZ,mBAAS,MAAM,gBAAgB,KAAK,KAAK;AAAA,YACrC,MAAM,6BAAM;AAAA,UAChB,CAAC,CAAC;AAAA,QACN,CAAC;AACD,eAAO,MAAI;AAAA,QAEX;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;AChGI,SAAS,oBAAoB,MAAM;AACnC,QAAM,eAAe,uBAAuB,IAAI;AAChD,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,WAAW,KAAK,YAAY;AAClC,SAAO,MAAI;AACP,UAAM,cAAc,CAAC,SAAO;AACxB,aAAO;AAAA,QACH,SAAU,UAAU;AAChB,cAAI,iBAAiB,YAAY,aAAa,UAAU;AAEpD,mBAAO;AAAA,UACX;AACA,cAAI,SAAS,SAAS,UAAU;AAC5B,mBAAO;AAAA,UACX;AACA,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAK,GAAG,IAAI,EAAE,KAAK,GAAG;AACjD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAK,GAAG,KAAK;AAC1C,gBAAM,MAAM,OAAO;AAAA,YACf,GAAG;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC;AACD,iBAAO,IAAI,UAAU;AAAA,QACzB;AAAA,QACA,MAAM,MAAO,UAAU;AACnB,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAK,GAAG,IAAI,EAAE,KAAK,GAAG;AACjD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAK,GAAG,KAAK;AAC1C,gBAAM,eAAe,gBAAgB,GAAG,SAAS,IAAI,CAAC,OAAK,GAAG,MAAM,CAAC;AACrE,gBAAM,kBAAkB,IAAI,gBAAgB;AAC5C,gBAAM,kBAAkB,kBAAkB;AAAA,YACtC,GAAG;AAAA,YACH,QAAQ,iBAAiB,cAAc,gBAAgB,MAAM;AAAA,YAC7D;AAAA,YACA,mBAAmB;AAAA,YACnB,kBAAkB;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAW;AACP,kBAAI,CAAC,KAAK,SAAS;AACf,uBAAO,CAAC;AAAA,cACZ;AACA,kBAAI,OAAO,KAAK,YAAY,YAAY;AACpC,uBAAO,KAAK,QAAQ;AAAA,kBAChB,QAAQ;AAAA,gBACZ,CAAC;AAAA,cACL;AACA,qBAAO,KAAK;AAAA,YAChB;AAAA,UACJ,CAAC;AACD,gBAAM,MAAM,MAAM;AAClB,gBAAM,CAAC,IAAI,IAAI,MAAM,oBAAoB;AAAA;AAAA,YAErC,MAAM,IAAI;AAAA,YACV,aAAa,aAAa,YAAY,OAAO;AAAA;AAAA,YAE7C,YAAaC,OAAM;AACf,oBAAM,QAAQA,MAAK;AACnB,qBAAO,gBAAgB,KAAK;AAAA,gBACxB;AAAA,cACJ,CAAC;AAAA,YACL;AAAA,YACA;AAAA,UACJ,CAAC;AACD,gBAAM,WAAW,OAAO,KAAK,QAAQ,EAAE,IAAI,OAAO,QAAM;AACpD,gBAAI,OAAO,MAAM,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAC1C,gBAAI,YAAY,MAAM;AAI1B,oBAAM,SAAS,MAAM,QAAQ,QAAQ,KAAK,MAAM;AACxC,qBAAO;AAAA,gBACH,QAAQ;AAAA,kBACJ,MAAM,MAAM,QAAQ,QAAQ,OAAO,IAAI;AAAA,gBAC3C;AAAA,cACJ;AAAA,YACJ;AACA,mBAAO;AAAA,cACH;AAAA,cACA,MAAM;AAAA,gBACF,UAAU;AAAA,cACd;AAAA,YACJ;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,QAAQ,WAAW,YAAY,OAAO,CAAC;AAC7C,UAAM,WAAW,WAAW,YAAY,UAAU,CAAC;AACnD,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,WAAO,CAAC,EAAE,GAAG,MAAI;AACb,aAAO,WAAW,CAAC,aAAW;AACY,YAAI,GAAG,SAAS,gBAAgB;AAClE,gBAAM,IAAI,MAAM,iGAAiG;AAAA,QACrH;AACA,cAAM,SAAS,QAAQ,GAAG,IAAI;AAC9B,cAAM,UAAU,OAAO,KAAK,EAAE;AAC9B,YAAI,OAAO;AACX,gBAAQ,KAAK,CAAC,QAAM;AAChB,iBAAO;AACP,cAAI,WAAW,IAAI,MAAM;AACrB,qBAAS,MAAM,gBAAgB,KAAK,IAAI,MAAM;AAAA,cAC1C,MAAM,IAAI;AAAA,YACd,CAAC,CAAC;AACF;AAAA,UACJ,WAAW,YAAY,IAAI,MAAM;AAC7B,qBAAS,KAAK;AAAA,cACV,SAAS,IAAI;AAAA,cACb,QAAQ,IAAI,KAAK;AAAA,YACrB,CAAC;AACD,qBAAS,SAAS;AAClB;AAAA,UACJ;AACA,mBAAS,SAAS;AAAA,QACtB,CAAC,EAAE,MAAM,CAAC,QAAM;AACZ,mBAAS,MAAM,gBAAgB,KAAK,KAAK;AAAA,YACrC,MAAM,6BAAM;AAAA,UAChB,CAAC,CAAC;AAAA,QACN,CAAC;AACD,eAAO,MAAI;AAAA,QAEX;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAGI,IAAM,+BAA+B;;;AC1IzC,IAAM,qBAAqB,CAAC,SAAO;AAC/B,MAAI,WAAW,MAAM;AACjB,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,WAAW,KAAK,GAAG;AACnB,UAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,QAAQ;AAC5D,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC9D;AACA,aAAO,YAAY;AAAA,QACf,GAAG;AAAA;AAAA,QAEH,mBAAmB;AAAA,QACnB;AAAA,QACA,SAAS,MAAI;AAAA,MACjB,CAAC;AAAA,IACL;AACA,QAAI,YAAY,KAAK,GAAG;AACpB,UAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,QAAQ;AAC5D,cAAM,IAAI,MAAM,kDAAkD;AAAA,MACtE;AACA,aAAO,YAAY;AAAA,QACf,GAAG;AAAA,QACH,mBAAmB;AAAA,QACnB;AAAA,QACA,SAAS,MAAI;AAAA,MACjB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO,kBAAkB,IAAI;AACjC;AAGI,SAAS,SAAS,MAAM;AACxB,QAAM,eAAe,uBAAuB,IAAI;AAChD,SAAO,MAAI;AACP,WAAO,CAAC,EAAE,GAAG,MAAI;AACb,aAAO,WAAW,CAAC,aAAW;AAC1B,cAAM,EAAE,MAAM,OAAO,KAAK,IAAI;AACQ,YAAI,SAAS,gBAAgB;AAC/D,gBAAM,IAAI,MAAM,sFAAsF;AAAA,QAC1G;AACA,cAAM,UAAU,mBAAmB;AAAA,UAC/B,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ,GAAG;AAAA,UACX,UAAW;AACP,gBAAI,CAAC,KAAK,SAAS;AACf,qBAAO,CAAC;AAAA,YACZ;AACA,gBAAI,OAAO,KAAK,YAAY,YAAY;AACpC,qBAAO,KAAK,QAAQ;AAAA,gBAChB;AAAA,cACJ,CAAC;AAAA,YACL;AACA,mBAAO,KAAK;AAAA,UAChB;AAAA,QACJ,CAAC;AACD,YAAI,OAAO;AACX,gBAAQ,KAAK,CAAC,QAAM;AAChB,iBAAO,IAAI;AACX,gBAAM,cAAc,gBAAgB,IAAI,MAAM,aAAa,YAAY,MAAM;AAC7E,cAAI,CAAC,YAAY,IAAI;AACjB,qBAAS,MAAM,gBAAgB,KAAK,YAAY,OAAO;AAAA,cACnD;AAAA,YACJ,CAAC,CAAC;AACF;AAAA,UACJ;AACA,mBAAS,KAAK;AAAA,YACV,SAAS,IAAI;AAAA,YACb,QAAQ,YAAY;AAAA,UACxB,CAAC;AACD,mBAAS,SAAS;AAAA,QACtB,CAAC,EAAE,MAAM,CAAC,UAAQ;AACd,mBAAS,MAAM,gBAAgB,KAAK,OAAO;AAAA,YACvC;AAAA,UACJ,CAAC,CAAC;AAAA,QACN,CAAC;AACD,eAAO,MAAI;AAAA,QAEX;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;ACnFA,SAASC,YAAW,OAAO;AACvB,MAAI,OAAO,aAAa,aAAa;AAEjC,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB;AAC5B;AACA,IAAM,WAAW;AAAA,EACb,KAAK;AAAA,IACD,OAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,MACN;AAAA,MACA;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,MACV;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,SAAS;AAAA;AAAA,MAEL,OAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA;AAAA,MAEA,UAAU;AAAA,QACN;AAAA,QACA;AAAA,MACJ;AAAA;AAAA,MAEA,cAAc;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,OAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACN;AAAA,QACA;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,MAAM;AACjC,QAAM,EAAE,WAAW,MAAM,aAAa,MAAM,IAAI,MAAM,IAAI;AAC1D,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,CAAC;AACd,MAAI,KAAK,cAAc,QAAQ;AAC3B,UAAM,KAAK,cAAc,OAAO,OAAO,MAAM,MAAM,IAAI,EAAE,IAAI,IAAI;AAAA,EACrE,WAAW,KAAK,cAAc,QAAQ;AAClC,UAAM,CAAC,cAAc,WAAW,IAAI,SAAS,KAAK,QAAQ,IAAI;AAC9D,UAAM,CAAC,WAAW,QAAQ,IAAI,SAAS,KAAK,KAAK,IAAI;AACrD,UAAM,QAAQ;AACd,UAAM,KAAK,cAAc,OAAO,eAAe,aAAa,cAAc,OAAO,OAAO,MAAM,MAAM,cAAc,OAAO,YAAY,UAAU,IAAI,EAAE,IAAI,MAAM,KAAK;AAAA,EACxK,OAAO;AAEH,UAAM,CAAC,OAAO,IAAI,IAAI,SAAS,IAAI,IAAI;AACvC,UAAM,MAAM;AAAA,yBACK,cAAc,OAAO,QAAQ,IAAI;AAAA,aAC7C,cAAc,OAAO,UAAU,OAAO;AAAA;AAAA;AAG3C,UAAM,KAAK,MAAM,cAAc,OAAO,OAAO,MAAM,MAAM,IAAI,EAAE,IAAI,KAAK,IAAI,MAAM,IAAI;AACtF,SAAK,KAAK,KAAK,GAAG,GAAG,wBAAwB,GAAG,GAAG,wBAAwB;AAAA,EAC/E;AACA,MAAI,cAAc,MAAM;AACpB,SAAK,KAAK,cAAc;AAAA,MACpB;AAAA,MACA,SAAS,KAAK;AAAA,IAClB,IAAI;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,OAAO;AACH,SAAK,KAAK;AAAA,MACN;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,GAAG,eAAe;AAAA,QACd,SAAS,KAAK;AAAA,MAClB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,gBAAgB,CAAC,EAAE,IAAI,SAAS,YAAY,OAAO,YAAY,MAAI,CAAC,UAAQ;AAC1E,QAAM,WAAW,MAAM;AACvB,QAAM,QAAQA,YAAW,QAAQ,IAAI,OAAO,YAAY,QAAQ,IAAI;AACpE,QAAM,EAAE,OAAO,KAAK,IAAI,sBAAsB;AAAA,IAC1C,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,KAAK,MAAM,cAAc,UAAU,MAAM,WAAW,MAAM,kBAAkB,SAAS,WAAW,MAAM,OAAO,UAAU,MAAM,OAAO,OAAO,SAAS,UAAU;AACpK,IAAE,EAAE,EAAE,MAAM,MAAM;AAAA,IACd,MAAM,KAAK,GAAG;AAAA,EAClB,EAAE,OAAO,IAAI,CAAC;AAClB;AAGA,SAAS,WAAW,OAAO,CAAC,GAAG;AAC/B,QAAM,EAAE,UAAU,MAAI,KAAK,IAAI;AAC/B,QAAM,YAAY,KAAK,cAAc,OAAO,WAAW,cAAc,SAAS;AAC9E,QAAM,cAAc,KAAK,eAAe,cAAc;AACtD,QAAM,EAAE,SAAS,cAAc;AAAA,IAC3B,GAAG,KAAK;AAAA,IACR;AAAA,IACA;AAAA,EACJ,CAAC,EAAE,IAAI;AACP,SAAO,MAAI;AACP,WAAO,CAAC,EAAE,IAAI,KAAK,MAAI;AACnB,aAAO,WAAW,CAAC,aAAW;AAE1B,YAAI,QAAQ;AAAA,UACR,GAAG;AAAA,UACH,WAAW;AAAA,QACf,CAAC,GAAG;AACA,iBAAO;AAAA,YACH,GAAG;AAAA,YACH,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AACA,cAAM,mBAAmB,KAAK,IAAI;AAClC,iBAAS,UAAU,QAAQ;AACvB,gBAAM,YAAY,KAAK,IAAI,IAAI;AAC/B,cAAI,QAAQ;AAAA,YACR,GAAG;AAAA,YACH,WAAW;AAAA,YACX;AAAA,UACJ,CAAC,GAAG;AACA,mBAAO;AAAA,cACH,GAAG;AAAA,cACH,WAAW;AAAA,cACX;AAAA,cACA;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAO,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,UACrB,KAAM,QAAQ;AACV,sBAAU,MAAM;AAAA,UACpB;AAAA,UACA,MAAO,QAAQ;AACX,sBAAU,MAAM;AAAA,UACpB;AAAA,QACJ,CAAC,CAAC,EAAE,UAAU,QAAQ;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;AC1KA,SAAS,QAAQ,OAAO;AACpB,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ;AAAA,IAClC;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,MAAM;AACrB,SAAO,CAAC,YAAU;AACd,UAAM,MAAM,QAAQ,KAAK,IAAI,EAAE,IAAI,CAAC,SAAO,KAAK,OAAO,CAAC;AACxD,UAAM,KAAK,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC,SAAO,KAAK,OAAO,CAAC;AACxD,WAAO,CAAC,UAAQ;AACZ,aAAO,WAAW,CAAC,aAAW;AAC1B,cAAM,QAAQ,KAAK,UAAU,MAAM,EAAE,IAAI,MAAM;AAC/C,eAAO,YAAY;AAAA,UACf,IAAI,MAAM;AAAA,UACV;AAAA,QACJ,CAAC,EAAE,UAAU,QAAQ;AAAA,MACzB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;ACtBA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,oBAAoB;AAAA,EACtB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,YAAY;AAChB;AAKI,IAAM,qBAAqB,CAAC,iBAAe;AAC3C,SAAO,iBAAiB,IAAI,IAAI,KAAK,IAAI,MAAO,KAAK,cAAc,GAAK;AAC5E;;;ACZI,IAAM,WAAW,CAAC,UAAU,SAAO;AACnC,SAAO,OAAO,UAAU,aAAa,MAAM,GAAG,IAAI,IAAI;AAC1D;;;ACHA,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAM,2BAAN,MAAM,kCAAiC,MAAM;AAAA,EACzC,YAAY,MAAK;AACb,UAAM,KAAK,SAAS;AAAA,MAChB,OAAO,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;AAII,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAItB,QAAQ;AACN,QAAI,CAAC,KAAK,QAAS;AACnB,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU,WAAW,KAAK,WAAW,KAAK,SAAS;AAAA,EAC5D;AAAA,EACA,QAAQ;AACJ,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU,WAAW,KAAK,WAAW,KAAK,SAAS;AAAA,EAC5D;AAAA,EACA,OAAO;AACH,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,YAAY,WAAW,WAAU;AAC7B,IAAAA,kBAAiB,MAAM,aAAa,MAAM;AAC1C,IAAAA,kBAAiB,MAAM,aAAa,MAAM;AAC1C,IAAAA,kBAAiB,MAAM,WAAW,MAAM;AACxC,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AACJ;AAEA,SAASC,iBAAgB;AACrB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAM;AACpC,cAAU;AACV,aAAS;AAAA,EACb,CAAC;AAED,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAKI,eAAe,WAAW,YAAY;AACtC,QAAM,MAAM,MAAM,SAAS,WAAW,GAAG;AACzC,MAAI,CAAC,WAAW,iBAAkB,QAAO;AAEzC,QAAM,SAAS,IAAI,SAAS,GAAG,IAAI,MAAM;AACzC,QAAM,mBAAmB,GAAG,MAAM;AAClC,SAAO,MAAM;AACjB;AACA,eAAe,uBAAuB,kBAAkB;AACpD,QAAM,UAAU;AAAA,IACZ,QAAQ;AAAA,IACR,MAAM,MAAM,SAAS,gBAAgB;AAAA,EACzC;AACA,SAAO,KAAK,UAAU,OAAO;AACjC;;;ACnFA,SAASC,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AAQI,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnB,SAAS,SAAS,WAAW;AAC3B,UAAM,EAAE,SAAS,KAAK,QAAQ,IAAIC,eAAc;AAChD,SAAK,iBAAiB,KAAK;AAAA,MACvB,IAAI,OAAO,QAAQ,EAAE;AAAA,MACrB;AAAA,MACA;AAAA,MACA,WAAW;AAAA,QACP,MAAM,UAAU;AAAA,QAChB,UAAU,MAAI;AACV,oBAAU,SAAS;AACnB,kBAAQ;AAAA,QACZ;AAAA,QACA,OAAO,CAAC,MAAI;AACR,oBAAU,MAAM,CAAC;AACjB,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO,MAAI;AACP,WAAK,OAAO,QAAQ,EAAE;AACtB,gBAAU,SAAS;AACnB,cAAQ;AAAA,IACZ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAGE,OAAO,WAAW;AAChB,QAAI,cAAc,KAAM;AACxB,SAAK,mBAAmB,KAAK,iBAAiB,OAAO,CAAC,EAAE,GAAG,MAAI,OAAO,OAAO,SAAS,CAAC;AACvF,WAAO,KAAK,gBAAgB,OAAO,SAAS,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,QAAQ;AACN,UAAM,WAAW,KAAK;AACtB,SAAK,mBAAmB,CAAC;AACzB,eAAW,WAAW,UAAS;AAC3B,WAAK,gBAAgB,QAAQ,EAAE,IAAI;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIE,qBAAqB;AACnB,WAAO,OAAO,OAAO,KAAK,eAAe;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAGE,kBAAkB,WAAW;AAC3B,QAAI,cAAc,KAAM,QAAO;AAC/B,WAAO,KAAK,gBAAgB,OAAO,SAAS,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAGE,sBAAsB;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,cAAc;AACZ,WAAO;AAAA,MACH,GAAG,KAAK,oBAAoB,EAAE,IAAI,CAAC,aAAW;AAAA,QACtC,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,QACjB,KAAK,QAAQ;AAAA,QACb,WAAW,QAAQ;AAAA,MACvB,EAAE;AAAA,MACN,GAAG,KAAK,mBAAmB,EAAE,IAAI,CAAC,aAAW;AAAA,QACrC,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,QACjB,KAAK,QAAQ;AAAA,QACb,WAAW,QAAQ;AAAA,MACvB,EAAE;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAGE,qBAAqB;AACnB,WAAO,KAAK,mBAAmB,EAAE,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAGE,0BAA0B;AACxB,WAAO,KAAK,mBAAmB,EAAE,KAAK,CAAC,YAAU,QAAQ,QAAQ,WAAW,cAAc;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA,EAGE,sBAAsB;AACpB,WAAO,KAAK,iBAAiB,SAAS;AAAA,EAC1C;AAAA,EACA,cAAa;AAGX,IAAAD,kBAAiB,MAAM,oBAAoB,IAAI,MAAM,CAAC;AAKtD,IAAAA,kBAAiB,MAAM,mBAAmB,CAAC,CAAC;AAAA,EAC9C;AACJ;;;ACvIA,SAASE,kBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AAKI,SAAS,YAAY,IAAI;AACzB,QAAM,EAAE,SAAS,SAAS,OAAO,IAAIC,eAAc;AACnD,KAAG,iBAAiB,QAAQ,MAAI;AAC5B,OAAG,oBAAoB,SAAS,MAAM;AACtC,YAAQ;AAAA,EACZ,CAAC;AACD,KAAG,iBAAiB,SAAS,MAAM;AACnC,SAAO;AACX;AAYI,SAAS,kBAAkB,IAAI,EAAE,YAAY,cAAc,GAAG;AAC9D,MAAI;AACJ,MAAI;AACJ,WAAS,QAAQ;AACb,kBAAc,WAAW,MAAI;AACzB,SAAG,KAAK,MAAM;AACd,oBAAc,WAAW,MAAI;AACzB,WAAG,MAAM;AAAA,MACb,GAAG,aAAa;AAAA,IACpB,GAAG,UAAU;AAAA,EACjB;AACA,WAAS,QAAQ;AACb,iBAAa,WAAW;AACxB,UAAM;AAAA,EACV;AACA,WAAS,OAAO;AACZ,iBAAa,WAAW;AACxB,UAAM;AAAA,EACV;AACA,KAAG,iBAAiB,QAAQ,KAAK;AACjC,KAAG,iBAAiB,WAAW,CAAC,EAAE,KAAK,MAAI;AACvC,iBAAa,WAAW;AACxB,UAAM;AACN,QAAI,SAAS,QAAQ;AACjB,WAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACD,KAAG,iBAAiB,SAAS,MAAI;AAC7B,iBAAa,WAAW;AACxB,iBAAa,WAAW;AAAA,EAC5B,CAAC;AACL;AAII,IAAM,eAAN,MAAM,cAAa;AAAA,EACnB,IAAI,KAAK;AACL,WAAO,KAAK,aAAa,IAAI;AAAA,EACjC;AAAA,EACA,IAAI,GAAG,IAAI;AACP,SAAK,aAAa,KAAK,EAAE;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAGE,SAAS;AACP,WAAO,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,eAAe,KAAK,kBAAkB,QAAQ,CAAC,KAAK;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAGE,WAAW;AACT,WAAO,CAAC,CAAC,KAAK,OAAO,KAAK,GAAG,eAAe,KAAK,kBAAkB,WAAW,KAAK,GAAG,eAAe,KAAK,kBAAkB;AAAA,EAChI;AAAA,EACA,MAAM,OAAO;AACT,QAAI,KAAK,YAAa,QAAO,KAAK;AAClC,SAAK,KAAK,EAAE,cAAa;AACzB,UAAM,YAAY,WAAW,KAAK,UAAU,EAAE,KAAK,CAAC,QAAM,IAAI,KAAK,kBAAkB,GAAG,CAAC;AACzF,SAAK,cAAc,UAAU,KAAK,OAAO,OAAK;AAC1C,WAAK,KAAK;AAEV,SAAG,iBAAiB,WAAW,SAAS,EAAE,KAAK,GAAG;AAC9C,YAAI,SAAS,QAAQ;AACjB,eAAK,KAAK,MAAM;AAAA,QACpB;AAAA,MACJ,CAAC;AACD,UAAI,KAAK,cAAc,SAAS;AAC5B,0BAAkB,IAAI,KAAK,aAAa;AAAA,MAC5C;AACA,SAAG,iBAAiB,SAAS,MAAI;AAC7B,YAAI,KAAK,OAAO,IAAI;AAChB,eAAK,KAAK;AAAA,QACd;AAAA,MACJ,CAAC;AACD,YAAM,YAAY,EAAE;AACpB,UAAI,KAAK,WAAW,kBAAkB;AAClC,WAAG,KAAK,MAAM,uBAAuB,KAAK,WAAW,gBAAgB,CAAC;AAAA,MAC1E;AAAA,IACJ,CAAC;AACD,QAAI;AACA,YAAM,KAAK;AAAA,IACf,UAAE;AACE,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAIE,MAAM,QAAQ;AA9HpB,QAAAC;AA+HQ,QAAI;AACA,YAAM,KAAK;AAAA,IACf,UAAE;AACE,OAAAA,MAAA,KAAK,OAAL,gBAAAA,IAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,YAAY,MAAK;AACb,IAAAF,kBAAiB,MAAM,MAAM,EAAE,cAAa,YAAY;AACxD,IAAAA,kBAAiB,MAAM,qBAAqB,MAAM;AAClD,IAAAA,kBAAiB,MAAM,cAAc,MAAM;AAC3C,IAAAA,kBAAiB,MAAM,iBAAiB,MAAM;AAC9C,IAAAA,kBAAiB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC;AAS9D,IAAAA,kBAAiB,MAAM,eAAe,IAAI;AACxC,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,QAAI,CAAC,KAAK,mBAAmB;AACzB,YAAM,IAAI,MAAM,8IAA8I;AAAA,IAClK;AACA,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAAA,EAC9B;AACJ;AACAA,kBAAiB,cAAc,gBAAgB,CAAC;AAG5C,SAAS,sBAAsB,YAAY;AAC3C,MAAI,WAAW,OAAO,GAAG;AACrB,WAAO;AAAA,MACH,IAAI,WAAW;AAAA,MACf,OAAO;AAAA,MACP,IAAI,WAAW;AAAA,IACnB;AAAA,EACJ;AACA,MAAI,WAAW,SAAS,GAAG;AACvB,WAAO;AAAA,MACH,IAAI,WAAW;AAAA,MACf,OAAO;AAAA,MACP,IAAI,WAAW;AAAA,IACnB;AAAA,EACJ;AACA,MAAI,CAAC,WAAW,IAAI;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,IAAI,WAAW;AAAA,IACf,OAAO;AAAA,IACP,IAAI,WAAW;AAAA,EACnB;AACJ;;;AC9KA,SAASG,mBAAiB,KAAK,KAAK,OAAO;AACvC,MAAI,OAAO,KAAK;AACZ,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AAAA,EACL,OAAO;AACH,QAAI,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACX;AAII,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAIb,MAAM,OAAO;AACX,SAAK,iBAAiB;AACtB,QAAI,KAAK,gBAAgB,IAAI,EAAE,UAAU,cAAc;AACnD,WAAK,gBAAgB,KAAK;AAAA,QACtB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI;AACA,YAAM,KAAK,iBAAiB,KAAK;AAAA,IACrC,SAAS,OAAO;AACZ,WAAK,UAAU,IAAI,yBAAyB;AAAA,QACxC,SAAS;AAAA,QACT,OAAO;AAAA,MACX,CAAC,CAAC;AACF,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAIE,MAAM,QAAQ;AACZ,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,KAAK;AAC5B,UAAM,kBAAkB,CAAC;AACzB,eAAW,WAAW,KAAK,eAAe,YAAY,GAAE;AACpD,UAAI,QAAQ,QAAQ,WAAW,gBAAgB;AAC3C,gBAAQ,UAAU,SAAS;AAAA,MAC/B,WAAW,QAAQ,UAAU,YAAY;AACrC,gBAAQ,UAAU,MAAM,gBAAgB,KAAK,IAAI,yBAAyB;AAAA,UACtE,SAAS;AAAA,QACb,CAAC,CAAC,CAAC;AAAA,MACP,OAAO;AACH,wBAAgB,KAAK,QAAQ,GAAG;AAAA,MACpC;AAAA,IACJ;AACA,UAAM,QAAQ,IAAI,eAAe,EAAE,MAAM,MAAI,IAAI;AACjD,UAAM,KAAK,iBAAiB,MAAM,EAAE,MAAM,MAAI,IAAI;AAClD,SAAK,gBAAgB,KAAK;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUE,QAAQ,EAAE,IAAI,EAAE,IAAI,MAAM,MAAM,OAAO,OAAO,GAAG,aAAa,YAAY,GAAG;AAC3E,WAAO,WAAW,CAAC,aAAW;AAC1B,YAAM,QAAQ,KAAK,UAAU;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,QACR,QAAQ;AAAA,UACJ,OAAO,YAAY,MAAM,UAAU,KAAK;AAAA,UACxC;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,GAAG;AAAA,QACH,KAAM,OAAO;AACT,gBAAM,cAAc,gBAAgB,OAAO,YAAY,MAAM;AAC7D,cAAI,CAAC,YAAY,IAAI;AACjB,qBAAS,MAAM,gBAAgB,KAAK,YAAY,KAAK,CAAC;AACtD;AAAA,UACJ;AACA,mBAAS,KAAK;AAAA,YACV,QAAQ,YAAY;AAAA,UACxB,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AACD,aAAO,MAAI;AACP,cAAM;AACN,YAAI,SAAS,kBAAkB,KAAK,iBAAiB,OAAO,GAAG;AAC3D,eAAK,KAAK;AAAA,YACN;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL;AACA,yCAAQ,oBAAoB,SAAS;AAAA,MACzC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,IAAI,aAAa;AACb,WAAO,sBAAsB,KAAK,gBAAgB;AAAA,EACtD;AAAA,EACA,UAAU,aAAa;AACnB,SAAK,gBAAgB,KAAK;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO,gBAAgB,KAAK,WAAW;AAAA,IAC3C,CAAC;AACD,QAAI,KAAK,aAAc;AACvB,UAAM,eAAe,OAAO,iBAAe;AACvC,UAAI;AACA,cAAM,MAAM,KAAK,oBAAoB,YAAY,CAAC;AAClD,YAAI,KAAK,gBAAgB;AACrB,gBAAM,KAAK,iBAAiB,MAAM;AAClC,gBAAM,KAAK,iBAAiB,KAAK;AACjC,cAAI,KAAK,eAAe,mBAAmB,GAAG;AAC1C,iBAAK,KAAK,KAAK,eAAe,mBAAmB,EAAE,IAAI,CAAC,EAAE,QAAQ,MAAI,OAAO,CAAC;AAAA,UAClF;AAAA,QACJ;AACA,aAAK,eAAe;AAAA,MACxB,QAAS;AACL,cAAM,aAAa,eAAe,CAAC;AAAA,MACvC;AAAA,IACJ;AACA,SAAK,eAAe,aAAa,CAAC;AAAA,EACtC;AAAA,EACA,wBAAwB,IAAI;AACxB,UAAM,qBAAqB,CAAC,UAAQ;AAChC,YAAM,OAAO,KAAK,eAAe,mBAAmB;AACpD,iBAAW,EAAE,SAAS,UAAU,KAAK,MAAK;AACtC,YAAI,QAAQ,WAAW,eAAgB;AACvC,kBAAU,MAAM,gBAAgB,KAAK,SAAS,IAAI,yBAAyB;AAAA,UACvE,SAAS;AAAA,UACT;AAAA,QACJ,CAAC,CAAC,CAAC;AACH,aAAK,eAAe,OAAO,QAAQ,EAAE;AAAA,MACzC;AAAA,IACJ;AACA,OAAG,iBAAiB,QAAQ,MAAI;AAC5B,UAAI,YAAU;AA7J1B,YAAAC,KAAAC;AA8JgB,YAAI,KAAK,UAAU;AACf,eAAK,kBAAkB,MAAM;AAAA,QACjC;AACA,SAAAA,OAAAD,MAAA,KAAK,WAAU,WAAf,gBAAAC,IAAA,KAAAD;AACA,aAAK,gBAAgB,KAAK;AAAA,UACtB,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC,EAAE,MAAM,CAAC,UAAQ;AACd,WAAG,MAAM,GAAI;AACb,2BAAmB,KAAK;AAAA,MAC5B,CAAC;AAAA,IACL,CAAC;AACD,OAAG,iBAAiB,WAAW,CAAC,EAAE,KAAK,MAAI;AACvC,WAAK,kBAAkB,MAAM;AAC7B,UAAI,OAAO,SAAS,YAAY;AAAA,QAC5B;AAAA,QACA;AAAA,MACJ,EAAE,SAAS,IAAI,EAAG;AAClB,YAAM,kBAAkB,KAAK,MAAM,IAAI;AACvC,UAAI,YAAY,iBAAiB;AAC7B,aAAK,sBAAsB,eAAe;AAC1C;AAAA,MACJ;AACA,WAAK,sBAAsB,eAAe;AAAA,IAC9C,CAAC;AACD,OAAG,iBAAiB,SAAS,CAAC,UAAQ;AAzL9C,UAAAA,KAAAC;AA0LY,yBAAmB,KAAK;AACxB,OAAAA,OAAAD,MAAA,KAAK,WAAU,YAAf,gBAAAC,IAAA,KAAAD,KAAyB;AACzB,UAAI,CAAC,KAAK,YAAY,KAAK,eAAe,wBAAwB,GAAG;AACjE,aAAK,UAAU,IAAI,yBAAyB;AAAA,UACxC,SAAS;AAAA,UACT,OAAO;AAAA,QACX,CAAC,CAAC;AAAA,MACN;AAAA,IACJ,CAAC;AACD,OAAG,iBAAiB,SAAS,CAAC,UAAQ;AAnM9C,UAAAA,KAAAC;AAoMY,yBAAmB,KAAK;AACxB,OAAAA,OAAAD,MAAA,KAAK,WAAU,YAAf,gBAAAC,IAAA,KAAAD,KAAyB;AACzB,WAAK,UAAU,IAAI,yBAAyB;AAAA,QACxC,SAAS;AAAA,QACT,OAAO;AAAA,MACX,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB,SAAS;AAC3B,UAAM,UAAU,KAAK,eAAe,kBAAkB,QAAQ,EAAE;AAChE,QAAI,CAAC,QAAS;AACd,YAAQ,UAAU,KAAK,OAAO;AAC9B,QAAI,YAAY;AAChB,QAAI,YAAY,WAAW,QAAQ,QAAQ,WAAW,gBAAgB;AAClE,UAAI,QAAQ,OAAO,SAAS,QAAQ;AAChC,gBAAQ,QAAQ,OAAO,cAAc,QAAQ,OAAO;AAAA,MACxD;AACA,UAAI,QAAQ,OAAO,SAAS,WAAW;AACnC,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,WAAW;AACX,cAAQ,UAAU,SAAS;AAC3B,WAAK,eAAe,OAAO,QAAQ,EAAE;AAAA,IACzC;AAAA,EACJ;AAAA,EACA,sBAAsB,SAAS;AAC3B,QAAI,QAAQ,WAAW,aAAa;AAChC,WAAK,UAAU,IAAI,yBAAyB;AAAA,QACxC,SAAS;AAAA,MACb,CAAC,CAAC;AAAA,IACN;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAGE,KAAK,mBAAmB;AACtB,QAAI,CAAC,KAAK,iBAAiB,OAAO,GAAG;AACjC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD;AACA,UAAM,WAAW,6BAA6B,QAAQ,oBAAoB;AAAA,MACtE;AAAA,IACJ;AACA,SAAK,iBAAiB,GAAG,KAAK,KAAK,UAAU,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI,QAAQ,CAAC;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,UAAU,SAAS,WAAW;AAC5B,SAAK,kBAAkB,MAAM;AAC7B,QAAI,YAAU;AACV,UAAI,CAAC,KAAK,iBAAiB,OAAO,GAAG;AACjC,cAAM,KAAK,KAAK;AAAA,MACpB;AACA,YAAM,MAAM,CAAC;AACb,UAAI,CAAC,KAAK,eAAe,oBAAoB,EAAG;AAChD,WAAK,KAAK,KAAK,eAAe,MAAM,EAAE,IAAI,CAAC,EAAE,SAAAE,SAAQ,MAAIA,QAAO,CAAC;AAAA,IACrE,CAAC,EAAE,MAAM,CAAC,QAAM;AACZ,WAAK,eAAe,OAAO,QAAQ,EAAE;AACrC,gBAAU,MAAM,gBAAgB,KAAK,GAAG,CAAC;AAAA,IAC7C,CAAC;AACD,WAAO,KAAK,eAAe,SAAS,SAAS,SAAS;AAAA,EAC1D;AAAA,EACA,YAAY,MAAK;AAGf,IAAAH,mBAAiB,MAAM,mBAAmB,MAAM;AAC9C,IAAAA,mBAAiB,MAAM,kBAAkB,KAAK;AAC9C,IAAAA,mBAAiB,MAAM,kBAAkB,IAAI,eAAe,CAAC;AAC7D,IAAAA,mBAAiB,MAAM,oBAAoB,MAAM;AACjD,IAAAA,mBAAiB,MAAM,uBAAuB,MAAM;AACpD,IAAAA,mBAAiB,MAAM,qBAAqB,MAAM;AAClD,IAAAA,mBAAiB,MAAM,aAAa,MAAM;AAC1C,IAAAA,mBAAiB,MAAM,YAAY,MAAM;AAK3C,IAAAA,mBAAiB,MAAM,gBAAgB,IAAI;AAEzC,SAAK,YAAY;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,IAClB;AACA,UAAM,cAAc;AAAA,MAChB,GAAG;AAAA,MACH,GAAG,KAAK;AAAA,IACZ;AAEA,SAAK,oBAAoB,IAAI,kBAAkB,MAAI;AAC/C,UAAI,KAAK,eAAe,oBAAoB,KAAK,KAAK,eAAe,mBAAmB,GAAG;AACvF,aAAK,kBAAkB,MAAM;AAC7B;AAAA,MACJ;AACA,WAAK,MAAM,EAAE,MAAM,MAAI,IAAI;AAAA,IAC/B,GAAG,YAAY,OAAO;AAEtB,SAAK,mBAAmB,IAAI,aAAa;AAAA,MACrC,mBAAmB,KAAK;AAAA,MACxB,YAAY;AAAA,MACZ,WAAW;AAAA,QACP,GAAG;AAAA,QACH,GAAG,KAAK;AAAA,MACZ;AAAA,IACJ,CAAC;AACD,SAAK,iBAAiB,aAAa,UAAU;AAAA,MACzC,MAAM,CAAC,OAAK;AACR,YAAI,CAAC,GAAI;AACT,aAAK,wBAAwB,EAAE;AAAA,MACnC;AAAA,IACJ,CAAC;AACD,SAAK,sBAAsB,KAAK,gBAAgB;AAChD,SAAK,WAAW,YAAY;AAC5B,SAAK,kBAAkB,gBAAgB;AAAA,MACnC,MAAM;AAAA,MACN,OAAO,YAAY,UAAU,SAAS;AAAA,MACtC,OAAO;AAAA,IACX,CAAC;AAED,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,KAAK,EAAE,MAAM,MAAI,IAAI;AAAA,IAC9B;AAAA,EACJ;AACJ;;;AC9TA,SAAS,eAAe,MAAM;AAC1B,SAAO,IAAI,SAAS,IAAI;AAC5B;;;ACAA,SAAS,OAAO,MAAM;AAClB,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,cAAc,eAAe,KAAK,WAAW;AACnD,SAAO,MAAI;AACP,WAAO,CAAC,EAAE,GAAG,MAAI;AACb,aAAO,WAAW,CAAC,aAAW;AAC1B,cAAM,wBAAwB,GAAG,SAAS,iBAAiB,OAAO,gBAAgB,UAAU;AAAA,UACxF,KAAM,QAAQ;AACV,qBAAS,KAAK;AAAA,cACV;AAAA,cACA,SAAS,GAAG;AAAA,YAChB,CAAC;AAAA,UACL;AAAA,QACJ,CAAC,IAAI;AACL,cAAM,sBAAsB,OAAO,QAAQ;AAAA,UACvC;AAAA,UACA;AAAA,QACJ,CAAC,EAAE,UAAU,QAAQ;AACrB,eAAO,MAAI;AACP,8BAAoB,YAAY;AAChC,yEAAuB;AAAA,QAC3B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;AC7BA,SAAS,wBAAwB,OAAO,aAAa;AACjD,MAAI,CAAC,aAAa;AACd,WAAO;AAAA,EACX;AACA,MAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;AAC5C,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,GAAG,SAAS,CAAC;AAAA,IACb;AAAA,EACJ;AACJ;;;ACDA,eAAe,wBAAwB,MAAM;AACzC,MAAI,MAAM,MAAM,SAAS,KAAK,GAAG;AACjC,MAAI,KAAK,kBAAkB;AACvB,UAAM,SAAS,MAAM,SAAS,KAAK,gBAAgB;AACnD,UAAM,SAAS,IAAI,SAAS,GAAG,IAAI,MAAM;AACzC,WAAO,SAAS,sBAAsB,mBAAmB,KAAK,UAAU,MAAM,CAAC;AAAA,EACnF;AACA,SAAO;AACX;AAII,IAAM,WAAW;AAAA,EACjB,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,wBAAwB;AAC5B;AAGI,SAAS,qBAAqB,MAAM;AACpC,QAAM,cAAc,eAAe,KAAK,WAAW;AACnD,SAAO,MAAI;AACP,WAAO,CAAC,EAAE,GAAG,MAAI;AACb,aAAO,WAAW,CAAC,aAAW;AAC1B,cAAM,EAAE,MAAM,MAAM,MAAM,IAAI;AACQ,YAAI,SAAS,gBAAgB;AAC/D,gBAAM,IAAI,MAAM,kDAAkD;AAAA,QACtE;AACA,YAAI,cAAc;AAClB,cAAM,KAAK,IAAI,gBAAgB;AAC/B,cAAM,SAAS,iBAAiB,GAAG,QAAQ,GAAG,MAAM;AACpD,cAAM,oBAAoB,kBAAkB;AAAA,UACxC,KAAK,YAAU,OAAO;AAAA,YACd;AAAA,YACA,KAAK,MAAM,wBAAwB,IAAI;AAAA,YACvC,OAAO,wBAAwB,OAAO,WAAW;AAAA,YACjD;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC;AAAA,UACL,MAAM,MAAI,SAAS,KAAK,oBAAoB;AAAA,YACpC;AAAA,UACJ,CAAC;AAAA,UACL;AAAA,UACA,aAAa,YAAY,OAAO;AAAA,UAChC,aAAa,KAAK,eAAe,WAAW;AAAA,QAChD,CAAC;AACD,cAAM,kBAAkB,gBAAgB;AAAA,UACpC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACX,CAAC;AACD,cAAM,gBAAgB,gBAAgB,UAAU;AAAA,UAC5C,KAAM,OAAO;AACT,qBAAS,KAAK;AAAA,cACV,QAAQ;AAAA,YACZ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD,YAAI,YAAU;AACV,2BAAiB,SAAS,mBAAkB;AACxC,oBAAO,MAAM,MAAK;AAAA,cACd,KAAK;AACD;AAAA,cACJ,KAAK;AACD,sBAAM,YAAY,MAAM;AACxB,oBAAI;AACJ,oBAAI,UAAU,IAAI;AAEd,gCAAc,UAAU;AACxB,2BAAS;AAAA,oBACL,IAAI,UAAU;AAAA,oBACd,MAAM;AAAA,kBACV;AAAA,gBACJ,OAAO;AACH,2BAAS;AAAA,oBACL,MAAM,UAAU;AAAA,kBACpB;AAAA,gBACJ;AACA,yBAAS,KAAK;AAAA,kBACV;AAAA,kBACA,SAAS;AAAA,oBACL,aAAa,MAAM;AAAA,kBACvB;AAAA,gBACJ,CAAC;AACD;AAAA,cACJ,KAAK,aACD;AACI,yBAAS,KAAK;AAAA,kBACV,QAAQ;AAAA,oBACJ,MAAM;AAAA,kBACV;AAAA,kBACA,SAAS;AAAA,oBACL,aAAa,MAAM;AAAA,kBACvB;AAAA,gBACJ,CAAC;AACD,gCAAgB,KAAK;AAAA,kBACjB,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,OAAO;AAAA,gBACX,CAAC;AACD;AAAA,cACJ;AAAA,cACJ,KAAK,oBACD;AACI,sBAAM,QAAQ,gBAAgB,KAAK;AAAA,kBAC/B,OAAO,MAAM;AAAA,gBACjB,CAAC;AACD,oBAAI,SAAS,SAAS,MAAM,MAAM,IAAI,GAAG;AAErC,kCAAgB,KAAK;AAAA,oBACjB,MAAM;AAAA,oBACN,OAAO;AAAA,oBACP;AAAA,kBACJ,CAAC;AACD;AAAA,gBACJ;AAGA,sBAAM;AAAA,cACV;AAAA,cACJ,KAAK,cACD;AACI,sBAAM,YAAY,gBAAgB,IAAI;AACtC,sBAAM,QAAQ,MAAM,SAAS,gBAAgB,KAAK,MAAM,KAAK;AAC7D,oBAAI,CAAC,SAAS,UAAU,UAAU,cAAc;AAC5C;AAAA,gBACJ;AACA,gCAAgB,KAAK;AAAA,kBACjB,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP;AAAA,gBACJ,CAAC;AACD;AAAA,cACJ;AAAA,cACJ,KAAK,WACD;AACI,gCAAgB,KAAK;AAAA,kBACjB,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,OAAO,IAAI,gBAAgB,cAAc,MAAM,EAAE,yCAAyC;AAAA,gBAC9F,CAAC;AAAA,cACL;AAAA,YACR;AAAA,UACJ;AACA,mBAAS,KAAK;AAAA,YACV,QAAQ;AAAA,cACJ,MAAM;AAAA,YACV;AAAA,UACJ,CAAC;AACD,0BAAgB,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACX,CAAC;AACD,mBAAS,SAAS;AAAA,QACtB,CAAC,EAAE,MAAM,CAAC,UAAQ;AACd,mBAAS,MAAM,gBAAgB,KAAK,KAAK,CAAC;AAAA,QAC9C,CAAC;AACD,eAAO,MAAI;AACP,mBAAS,SAAS;AAClB,aAAG,MAAM;AACT,wBAAc,YAAY;AAAA,QAC9B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAGI,IAAM,gCAAgC;;;AC/KtC,SAAS,UAAU,MAAM;AAEzB,SAAO,MAAI;AAEP,WAAO,CAAC,aAAW;AAEf,aAAO,WAAW,CAAC,aAAW;AAC1B,YAAI;AACJ,YAAI,kBAAkB;AACtB,YAAI,cAAc;AAClB,gBAAQ,CAAC;AACT,iBAAS,oBAAoB;AACzB,gBAAM,KAAK,SAAS;AACpB,cAAI,CAAC,aAAa;AACd,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,YACH,GAAG;AAAA,YACH,OAAO,wBAAwB,GAAG,OAAO,WAAW;AAAA,UACxD;AAAA,QACJ;AACA,iBAAS,QAAQ,UAAU;AACvB,gBAAM,KAAK,kBAAkB;AAC7B,kBAAQ,SAAS,KAAK,EAAE,EAAE,UAAU;AAAA,YAChC,MAAO,OAAO;AA9BtC,kBAAAI;AA+B4B,oBAAM,cAAc,KAAK,MAAM;AAAA,gBAC3B;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ,CAAC;AACD,kBAAI,CAAC,aAAa;AACd,yBAAS,MAAM,KAAK;AACpB;AAAA,cACJ;AACA,oBAAM,YAAUA,MAAA,KAAK,iBAAL,gBAAAA,IAAA,WAAoB,cAAa;AACjD,kBAAI,WAAW,GAAG;AACd,wBAAQ,WAAW,CAAC;AACpB;AAAA,cACJ;AACA,gCAAkB,WAAW,MAAI,QAAQ,WAAW,CAAC,GAAG,OAAO;AAAA,YACnE;AAAA,YACA,KAAM,UAAU;AAEZ,mBAAK,CAAC,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,WAAW,SAAS,OAAO,IAAI;AAElF,8BAAc,SAAS,OAAO;AAAA,cAClC;AACA,uBAAS,KAAK,QAAQ;AAAA,YAC1B;AAAA,YACA,WAAY;AACR,uBAAS,SAAS;AAAA,YACtB;AAAA,UACJ,CAAC;AAAA,QACL;AACA,eAAO,MAAI;AACP,gBAAM,YAAY;AAClB,uBAAa,eAAe;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;", "names": ["_a", "observable", "_a", "lazy", "router", "key", "_a", "_define_property", "_define_property", "_ts_dispose_resources", "env", "_define_property", "_a", "value", "_ts_add_disposable_resource", "_ts_dispose_resources", "env", "_ts_add_disposable_resource", "_ts_dispose_resources", "_define_property", "_a", "builder", "_a", "_define_property", "_a", "_b", "_define_property", "_a", "_b", "_c", "_d", "isFunction", "_a", "_b", "_a", "opts", "isFormData", "_define_property", "withResolvers", "_define_property", "withResolvers", "_define_property", "withResolvers", "_a", "_define_property", "_a", "_b", "message", "_a"]}