// node_modules/@trpc/server/dist/observable/observable.mjs
function observable(subscribe) {
  const self = {
    subscribe(observer) {
      let teardownRef = null;
      let isDone = false;
      let unsubscribed = false;
      let teardownImmediately = false;
      function unsubscribe() {
        if (teardownRef === null) {
          teardownImmediately = true;
          return;
        }
        if (unsubscribed) {
          return;
        }
        unsubscribed = true;
        if (typeof teardownRef === "function") {
          teardownRef();
        } else if (teardownRef) {
          teardownRef.unsubscribe();
        }
      }
      teardownRef = subscribe({
        next(value) {
          var _a2;
          if (isDone) {
            return;
          }
          (_a2 = observer.next) == null ? void 0 : _a2.call(observer, value);
        },
        error(err) {
          var _a2;
          if (isDone) {
            return;
          }
          isDone = true;
          (_a2 = observer.error) == null ? void 0 : _a2.call(observer, err);
          unsubscribe();
        },
        complete() {
          var _a2;
          if (isDone) {
            return;
          }
          isDone = true;
          (_a2 = observer.complete) == null ? void 0 : _a2.call(observer);
          unsubscribe();
        }
      });
      if (teardownImmediately) {
        unsubscribe();
      }
      return {
        unsubscribe
      };
    },
    pipe(...operations) {
      return operations.reduce(pipeReducer, self);
    }
  };
  return self;
}
function pipeReducer(prev, fn) {
  return fn(prev);
}
function observableToPromise(observable2) {
  const ac = new AbortController();
  const promise = new Promise((resolve, reject) => {
    let isDone = false;
    function onDone() {
      if (isDone) {
        return;
      }
      isDone = true;
      obs$.unsubscribe();
    }
    ac.signal.addEventListener("abort", () => {
      reject(ac.signal.reason);
    });
    const obs$ = observable2.subscribe({
      next(data) {
        isDone = true;
        resolve(data);
        onDone();
      },
      error(data) {
        reject(data);
      },
      complete() {
        ac.abort();
        onDone();
      }
    });
  });
  return promise;
}

// node_modules/@trpc/server/dist/observable/operators.mjs
function share(_opts) {
  return (source) => {
    let refCount = 0;
    let subscription = null;
    const observers = [];
    function startIfNeeded() {
      if (subscription) {
        return;
      }
      subscription = source.subscribe({
        next(value) {
          var _a2;
          for (const observer of observers) {
            (_a2 = observer.next) == null ? void 0 : _a2.call(observer, value);
          }
        },
        error(error) {
          var _a2;
          for (const observer of observers) {
            (_a2 = observer.error) == null ? void 0 : _a2.call(observer, error);
          }
        },
        complete() {
          var _a2;
          for (const observer of observers) {
            (_a2 = observer.complete) == null ? void 0 : _a2.call(observer);
          }
        }
      });
    }
    function resetIfNeeded() {
      if (refCount === 0 && subscription) {
        const _sub = subscription;
        subscription = null;
        _sub.unsubscribe();
      }
    }
    return observable((subscriber) => {
      refCount++;
      observers.push(subscriber);
      startIfNeeded();
      return {
        unsubscribe() {
          refCount--;
          resetIfNeeded();
          const index = observers.findIndex((v) => v === subscriber);
          if (index > -1) {
            observers.splice(index, 1);
          }
        }
      };
    });
  };
}
function tap(observer) {
  return (source) => {
    return observable((destination) => {
      return source.subscribe({
        next(value) {
          var _a2;
          (_a2 = observer.next) == null ? void 0 : _a2.call(observer, value);
          destination.next(value);
        },
        error(error) {
          var _a2;
          (_a2 = observer.error) == null ? void 0 : _a2.call(observer, error);
          destination.error(error);
        },
        complete() {
          var _a2;
          (_a2 = observer.complete) == null ? void 0 : _a2.call(observer);
          destination.complete();
        }
      });
    });
  };
}
var distinctUnsetMarker = Symbol();

// node_modules/@trpc/server/dist/observable/behaviorSubject.mjs
function behaviorSubject(initialValue) {
  let value = initialValue;
  const observerList = [];
  const addObserver = (observer) => {
    if (value !== void 0) {
      observer.next(value);
    }
    observerList.push(observer);
  };
  const removeObserver = (observer) => {
    observerList.splice(observerList.indexOf(observer), 1);
  };
  const obs = observable((observer) => {
    addObserver(observer);
    return () => {
      removeObserver(observer);
    };
  });
  obs.next = (nextValue) => {
    if (value === nextValue) {
      return;
    }
    value = nextValue;
    for (const observer of observerList) {
      observer.next(nextValue);
    }
  };
  obs.get = () => value;
  return obs;
}

// node_modules/@trpc/client/dist/links/internals/createChain.mjs
function createChain(opts) {
  return observable((observer) => {
    function execute(index = 0, op = opts.op) {
      const next = opts.links[index];
      if (!next) {
        throw new Error("No more links to execute - did you forget to add an ending link?");
      }
      const subscription = next({
        op,
        next(nextOp) {
          const nextObserver = execute(index + 1, nextOp);
          return nextObserver;
        }
      });
      return subscription;
    }
    const obs$ = execute();
    return obs$.subscribe(observer);
  });
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/createProxy.mjs
var noop = () => {
};
var freezeIfAvailable = (obj) => {
  if (Object.freeze) {
    Object.freeze(obj);
  }
};
function createInnerProxy(callback, path, memo) {
  var _memo, _cacheKey;
  const cacheKey = path.join(".");
  (_memo = memo)[_cacheKey = cacheKey] ?? (_memo[_cacheKey] = new Proxy(noop, {
    get(_obj, key) {
      if (typeof key !== "string" || key === "then") {
        return void 0;
      }
      return createInnerProxy(callback, [
        ...path,
        key
      ], memo);
    },
    apply(_1, _2, args) {
      const lastOfPath = path[path.length - 1];
      let opts = {
        args,
        path
      };
      if (lastOfPath === "call") {
        opts = {
          args: args.length >= 2 ? [
            args[1]
          ] : [],
          path: path.slice(0, -1)
        };
      } else if (lastOfPath === "apply") {
        opts = {
          args: args.length >= 2 ? args[1] : [],
          path: path.slice(0, -1)
        };
      }
      freezeIfAvailable(opts.args);
      freezeIfAvailable(opts.path);
      return callback(opts);
    }
  }));
  return memo[cacheKey];
}
var createRecursiveProxy = (callback) => createInnerProxy(callback, [], /* @__PURE__ */ Object.create(null));
var createFlatProxy = (callback) => {
  return new Proxy(noop, {
    get(_obj, name) {
      if (name === "then") {
        return void 0;
      }
      return callback(name);
    }
  });
};

// node_modules/@trpc/server/dist/unstable-core-do-not-import/error/formatter.mjs
var defaultFormatter = ({ shape }) => {
  return shape;
};

// node_modules/@trpc/server/dist/unstable-core-do-not-import/rpc/codes.mjs
var TRPC_ERROR_CODES_BY_KEY = {
  /**
  * Invalid JSON was received by the server.
  * An error occurred on the server while parsing the JSON text.
  */
  PARSE_ERROR: -32700,
  /**
  * The JSON sent is not a valid Request object.
  */
  BAD_REQUEST: -32600,
  // Internal JSON-RPC error
  INTERNAL_SERVER_ERROR: -32603,
  NOT_IMPLEMENTED: -32603,
  BAD_GATEWAY: -32603,
  SERVICE_UNAVAILABLE: -32603,
  GATEWAY_TIMEOUT: -32603,
  // Implementation specific errors
  UNAUTHORIZED: -32001,
  PAYMENT_REQUIRED: -32002,
  FORBIDDEN: -32003,
  NOT_FOUND: -32004,
  METHOD_NOT_SUPPORTED: -32005,
  TIMEOUT: -32008,
  CONFLICT: -32009,
  PRECONDITION_FAILED: -32012,
  PAYLOAD_TOO_LARGE: -32013,
  UNSUPPORTED_MEDIA_TYPE: -32015,
  UNPROCESSABLE_CONTENT: -32022,
  TOO_MANY_REQUESTS: -32029,
  CLIENT_CLOSED_REQUEST: -32099
};

// node_modules/@trpc/server/dist/unstable-core-do-not-import/utils.mjs
var unsetMarker = Symbol();
function mergeWithoutOverrides(obj1, ...objs) {
  const newObj = Object.assign(/* @__PURE__ */ Object.create(null), obj1);
  for (const overrides of objs) {
    for (const key in overrides) {
      if (key in newObj && newObj[key] !== overrides[key]) {
        throw new Error(`Duplicate key ${key}`);
      }
      newObj[key] = overrides[key];
    }
  }
  return newObj;
}
function isObject(value) {
  return !!value && !Array.isArray(value) && typeof value === "object";
}
function isFunction(fn) {
  return typeof fn === "function";
}
function omitPrototype(obj) {
  return Object.assign(/* @__PURE__ */ Object.create(null), obj);
}
var asyncIteratorsSupported = typeof Symbol === "function" && !!Symbol.asyncIterator;
function isAsyncIterable(value) {
  return asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value;
}
var run = (fn) => fn();
function sleep(ms = 0) {
  return new Promise((res) => setTimeout(res, ms));
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/error/TRPCError.mjs
function _define_property(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var UnknownCauseError = class extends Error {
};
function getCauseFromUnknown(cause) {
  if (cause instanceof Error) {
    return cause;
  }
  const type = typeof cause;
  if (type === "undefined" || type === "function" || cause === null) {
    return void 0;
  }
  if (type !== "object") {
    return new Error(String(cause));
  }
  if (isObject(cause)) {
    return Object.assign(new UnknownCauseError(), cause);
  }
  return void 0;
}
function getTRPCErrorFromUnknown(cause) {
  if (cause instanceof TRPCError) {
    return cause;
  }
  if (cause instanceof Error && cause.name === "TRPCError") {
    return cause;
  }
  const trpcError = new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    cause
  });
  if (cause instanceof Error && cause.stack) {
    trpcError.stack = cause.stack;
  }
  return trpcError;
}
var TRPCError = class extends Error {
  constructor(opts) {
    const cause = getCauseFromUnknown(opts.cause);
    const message = opts.message ?? (cause == null ? void 0 : cause.message) ?? opts.code;
    super(message, {
      cause
    }), // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore override doesn't work in all environments due to "This member cannot have an 'override' modifier because it is not declared in the base class 'Error'"
    _define_property(this, "cause", void 0), _define_property(this, "code", void 0);
    this.code = opts.code;
    this.name = "TRPCError";
    this.cause ?? (this.cause = cause);
  }
};

// node_modules/@trpc/server/dist/unstable-core-do-not-import/transformer.mjs
function getDataTransformer(transformer) {
  if ("input" in transformer) {
    return transformer;
  }
  return {
    input: transformer,
    output: transformer
  };
}
var defaultTransformer = {
  input: {
    serialize: (obj) => obj,
    deserialize: (obj) => obj
  },
  output: {
    serialize: (obj) => obj,
    deserialize: (obj) => obj
  }
};
function transformResultInner(response, transformer) {
  if ("error" in response) {
    const error = transformer.deserialize(response.error);
    return {
      ok: false,
      error: {
        ...response,
        error
      }
    };
  }
  const result = {
    ...response.result,
    ...(!response.result.type || response.result.type === "data") && {
      type: "data",
      data: transformer.deserialize(response.result.data)
    }
  };
  return {
    ok: true,
    result
  };
}
var TransformResultError = class extends Error {
  constructor() {
    super("Unable to transform response from server");
  }
};
function transformResult(response, transformer) {
  let result;
  try {
    result = transformResultInner(response, transformer);
  } catch {
    throw new TransformResultError();
  }
  if (!result.ok && (!isObject(result.error.error) || typeof result.error.error["code"] !== "number")) {
    throw new TransformResultError();
  }
  if (result.ok && !isObject(result.result)) {
    throw new TransformResultError();
  }
  return result;
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/router.mjs
var lazySymbol = Symbol("lazy");
function once(fn) {
  const uncalled = Symbol();
  let result = uncalled;
  return () => {
    if (result === uncalled) {
      result = fn();
    }
    return result;
  };
}
function isLazy(input) {
  return typeof input === "function" && lazySymbol in input;
}
function isRouter(value) {
  return isObject(value) && isObject(value["_def"]) && "router" in value["_def"];
}
var emptyRouter = {
  _ctx: null,
  _errorShape: null,
  _meta: null,
  queries: {},
  mutations: {},
  subscriptions: {},
  errorFormatter: defaultFormatter,
  transformer: defaultTransformer
};
var reservedWords = [
  /**
  * Then is a reserved word because otherwise we can't return a promise that returns a Proxy
  * since JS will think that `.then` is something that exists
  */
  "then",
  /**
  * `fn.call()` and `fn.apply()` are reserved words because otherwise we can't call a function using `.call` or `.apply`
  */
  "call",
  "apply"
];
function createRouterFactory(config) {
  function createRouterInner(input) {
    const reservedWordsUsed = new Set(Object.keys(input).filter((v) => reservedWords.includes(v)));
    if (reservedWordsUsed.size > 0) {
      throw new Error("Reserved words used in `router({})` call: " + Array.from(reservedWordsUsed).join(", "));
    }
    const procedures = omitPrototype({});
    const lazy2 = omitPrototype({});
    function createLazyLoader(opts) {
      return {
        ref: opts.ref,
        load: once(async () => {
          const router2 = await opts.ref();
          const lazyPath = [
            ...opts.path,
            opts.key
          ];
          const lazyKey = lazyPath.join(".");
          opts.aggregate[opts.key] = step(router2._def.record, lazyPath);
          delete lazy2[lazyKey];
          for (const [nestedKey, nestedItem] of Object.entries(router2._def.lazy)) {
            const nestedRouterKey = [
              ...lazyPath,
              nestedKey
            ].join(".");
            lazy2[nestedRouterKey] = createLazyLoader({
              ref: nestedItem.ref,
              path: lazyPath,
              key: nestedKey,
              aggregate: opts.aggregate[opts.key]
            });
          }
        })
      };
    }
    function step(from, path = []) {
      const aggregate = omitPrototype({});
      for (const [key, item] of Object.entries(from ?? {})) {
        if (isLazy(item)) {
          lazy2[[
            ...path,
            key
          ].join(".")] = createLazyLoader({
            path,
            ref: item,
            key,
            aggregate
          });
          continue;
        }
        if (isRouter(item)) {
          aggregate[key] = step(item._def.record, [
            ...path,
            key
          ]);
          continue;
        }
        if (!isProcedure(item)) {
          aggregate[key] = step(item, [
            ...path,
            key
          ]);
          continue;
        }
        const newPath = [
          ...path,
          key
        ].join(".");
        if (procedures[newPath]) {
          throw new Error(`Duplicate key: ${newPath}`);
        }
        procedures[newPath] = item;
        aggregate[key] = item;
      }
      return aggregate;
    }
    const record = step(input);
    const _def = {
      _config: config,
      router: true,
      procedures,
      lazy: lazy2,
      ...emptyRouter,
      record
    };
    const router = {
      ...record,
      _def,
      createCaller: createCallerFactory()({
        _def
      })
    };
    return router;
  }
  return createRouterInner;
}
function isProcedure(procedureOrRouter) {
  return typeof procedureOrRouter === "function";
}
async function getProcedureAtPath(router, path) {
  const { _def } = router;
  let procedure = _def.procedures[path];
  while (!procedure) {
    const key = Object.keys(_def.lazy).find((key2) => path.startsWith(key2));
    if (!key) {
      return null;
    }
    const lazyRouter = _def.lazy[key];
    await lazyRouter.load();
    procedure = _def.procedures[path];
  }
  return procedure;
}
function createCallerFactory() {
  return function createCallerInner(router) {
    const { _def } = router;
    return function createCaller(ctxOrCallback, opts) {
      return createRecursiveProxy(async ({ path, args }) => {
        var _a2;
        const fullPath = path.join(".");
        if (path.length === 1 && path[0] === "_def") {
          return _def;
        }
        const procedure = await getProcedureAtPath(router, fullPath);
        let ctx = void 0;
        try {
          if (!procedure) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `No procedure found on path "${path}"`
            });
          }
          ctx = isFunction(ctxOrCallback) ? await Promise.resolve(ctxOrCallback()) : ctxOrCallback;
          return await procedure({
            path: fullPath,
            getRawInput: async () => args[0],
            ctx,
            type: procedure._def.type,
            signal: opts == null ? void 0 : opts.signal
          });
        } catch (cause) {
          (_a2 = opts == null ? void 0 : opts.onError) == null ? void 0 : _a2.call(opts, {
            ctx,
            error: getTRPCErrorFromUnknown(cause),
            input: args[0],
            path: fullPath,
            type: (procedure == null ? void 0 : procedure._def.type) ?? "unknown"
          });
          throw cause;
        }
      });
    };
  };
}
function mergeRouters(...routerList) {
  var _a2;
  const record = mergeWithoutOverrides({}, ...routerList.map((r) => r._def.record));
  const errorFormatter = routerList.reduce((currentErrorFormatter, nextRouter) => {
    if (nextRouter._def._config.errorFormatter && nextRouter._def._config.errorFormatter !== defaultFormatter) {
      if (currentErrorFormatter !== defaultFormatter && currentErrorFormatter !== nextRouter._def._config.errorFormatter) {
        throw new Error("You seem to have several error formatters");
      }
      return nextRouter._def._config.errorFormatter;
    }
    return currentErrorFormatter;
  }, defaultFormatter);
  const transformer = routerList.reduce((prev, current) => {
    if (current._def._config.transformer && current._def._config.transformer !== defaultTransformer) {
      if (prev !== defaultTransformer && prev !== current._def._config.transformer) {
        throw new Error("You seem to have several transformers");
      }
      return current._def._config.transformer;
    }
    return prev;
  }, defaultTransformer);
  const router = createRouterFactory({
    errorFormatter,
    transformer,
    isDev: routerList.every((r) => r._def._config.isDev),
    allowOutsideOfServer: routerList.every((r) => r._def._config.allowOutsideOfServer),
    isServer: routerList.every((r) => r._def._config.isServer),
    $types: (_a2 = routerList[0]) == null ? void 0 : _a2._def._config.$types
  })(record);
  return router;
}

// node_modules/@trpc/server/dist/vendor/unpromise/unpromise.mjs
function _define_property2(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var _computedKey;
var subscribableCache = /* @__PURE__ */ new WeakMap();
var NOOP = () => {
};
_computedKey = Symbol.toStringTag;
var _computedKey1 = _computedKey;
var Unpromise = class _Unpromise {
  /** Create a promise that mitigates uncontrolled subscription to a long-lived
  * Promise via .then() and .catch() - otherwise a source of memory leaks.
  *
  * The returned promise has an `unsubscribe()` method which can be called when
  * the Promise is no longer being tracked by application logic, and which
  * ensures that there is no reference chain from the original promise to the
  * new one, and therefore no memory leak.
  *
  * If original promise has not yet settled, this adds a new unique promise
  * that listens to then/catch events, along with an `unsubscribe()` method to
  * detach it.
  *
  * If original promise has settled, then creates a new Promise.resolve() or
  * Promise.reject() and provided unsubscribe is a noop.
  *
  * If you call `unsubscribe()` before the returned Promise has settled, it
  * will never settle.
  */
  subscribe() {
    let promise;
    let unsubscribe;
    const { settlement } = this;
    if (settlement === null) {
      if (this.subscribers === null) {
        throw new Error("Unpromise settled but still has subscribers");
      }
      const subscriber = withResolvers();
      this.subscribers = listWithMember(this.subscribers, subscriber);
      promise = subscriber.promise;
      unsubscribe = () => {
        if (this.subscribers !== null) {
          this.subscribers = listWithoutMember(this.subscribers, subscriber);
        }
      };
    } else {
      const { status } = settlement;
      if (status === "fulfilled") {
        promise = Promise.resolve(settlement.value);
      } else {
        promise = Promise.reject(settlement.reason);
      }
      unsubscribe = NOOP;
    }
    return Object.assign(promise, {
      unsubscribe
    });
  }
  /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */
  then(onfulfilled, onrejected) {
    const subscribed = this.subscribe();
    const { unsubscribe } = subscribed;
    return Object.assign(subscribed.then(onfulfilled, onrejected), {
      unsubscribe
    });
  }
  catch(onrejected) {
    const subscribed = this.subscribe();
    const { unsubscribe } = subscribed;
    return Object.assign(subscribed.catch(onrejected), {
      unsubscribe
    });
  }
  finally(onfinally) {
    const subscribed = this.subscribe();
    const { unsubscribe } = subscribed;
    return Object.assign(subscribed.finally(onfinally), {
      unsubscribe
    });
  }
  /** Unpromise STATIC METHODS */
  /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime
  * of the provided Promise reference) */
  static proxy(promise) {
    const cached = _Unpromise.getSubscribablePromise(promise);
    return typeof cached !== "undefined" ? cached : _Unpromise.createSubscribablePromise(promise);
  }
  /** Create and store an Unpromise keyed by an original Promise. */
  static createSubscribablePromise(promise) {
    const created = new _Unpromise(promise);
    subscribableCache.set(promise, created);
    subscribableCache.set(created, created);
    return created;
  }
  /** Retrieve a previously-created Unpromise keyed by an original Promise. */
  static getSubscribablePromise(promise) {
    return subscribableCache.get(promise);
  }
  /** Promise STATIC METHODS */
  /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from
  * it (that can be later unsubscribed to eliminate Memory leaks) */
  static resolve(value) {
    const promise = typeof value === "object" && value !== null && "then" in value && typeof value.then === "function" ? value : Promise.resolve(value);
    return _Unpromise.proxy(promise).subscribe();
  }
  static async any(values) {
    const valuesArray = Array.isArray(values) ? values : [
      ...values
    ];
    const subscribedPromises = valuesArray.map(_Unpromise.resolve);
    try {
      return await Promise.any(subscribedPromises);
    } finally {
      subscribedPromises.forEach(({ unsubscribe }) => {
        unsubscribe();
      });
    }
  }
  static async race(values) {
    const valuesArray = Array.isArray(values) ? values : [
      ...values
    ];
    const subscribedPromises = valuesArray.map(_Unpromise.resolve);
    try {
      return await Promise.race(subscribedPromises);
    } finally {
      subscribedPromises.forEach(({ unsubscribe }) => {
        unsubscribe();
      });
    }
  }
  /** Create a race of SubscribedPromises that will fulfil to a single winning
  * Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises
  * accumulating .then() and .catch() subscribers. Allows simple logic to
  * consume the result, like...
  * ```ts
  * const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);
  * if(winner === promiseB){
  *   const result = await promiseB;
  *   // do the thing
  * }
  * ```
  * */
  static async raceReferences(promises) {
    const selfPromises = promises.map(resolveSelfTuple);
    try {
      return await Promise.race(selfPromises);
    } finally {
      for (const promise of selfPromises) {
        promise.unsubscribe();
      }
    }
  }
  constructor(arg) {
    _define_property2(this, "promise", void 0);
    _define_property2(this, "subscribers", []);
    _define_property2(this, "settlement", null);
    _define_property2(this, _computedKey1, "Unpromise");
    if (typeof arg === "function") {
      this.promise = new Promise(arg);
    } else {
      this.promise = arg;
    }
    const thenReturn = this.promise.then((value) => {
      const { subscribers } = this;
      this.subscribers = null;
      this.settlement = {
        status: "fulfilled",
        value
      };
      subscribers == null ? void 0 : subscribers.forEach(({ resolve }) => {
        resolve(value);
      });
    });
    if ("catch" in thenReturn) {
      thenReturn.catch((reason) => {
        const { subscribers } = this;
        this.subscribers = null;
        this.settlement = {
          status: "rejected",
          reason
        };
        subscribers == null ? void 0 : subscribers.forEach(({ reject }) => {
          reject(reason);
        });
      });
    }
  }
};
function resolveSelfTuple(promise) {
  return Unpromise.proxy(promise).then(() => [
    promise
  ]);
}
function withResolvers() {
  let resolve;
  let reject;
  const promise = new Promise((_resolve, _reject) => {
    resolve = _resolve;
    reject = _reject;
  });
  return {
    promise,
    resolve,
    reject
  };
}
function listWithMember(arr, member) {
  return [
    ...arr,
    member
  ];
}
function listWithoutIndex(arr, index) {
  return [
    ...arr.slice(0, index),
    ...arr.slice(index + 1)
  ];
}
function listWithoutMember(arr, member) {
  const index = arr.indexOf(member);
  if (index !== -1) {
    return listWithoutIndex(arr, index);
  }
  return arr;
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/utils/disposable.mjs
var _Symbol;
var _Symbol1;
(_Symbol = Symbol).dispose ?? (_Symbol.dispose = Symbol());
(_Symbol1 = Symbol).asyncDispose ?? (_Symbol1.asyncDispose = Symbol());
function makeResource(thing, dispose) {
  const it = thing;
  const existing = it[Symbol.dispose];
  it[Symbol.dispose] = () => {
    dispose();
    existing == null ? void 0 : existing();
  };
  return it;
}
function makeAsyncResource(thing, dispose) {
  const it = thing;
  const existing = it[Symbol.asyncDispose];
  it[Symbol.asyncDispose] = async () => {
    await dispose();
    await (existing == null ? void 0 : existing());
  };
  return it;
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/utils/timerResource.mjs
var disposablePromiseTimerResult = Symbol();
function timerResource(ms) {
  let timer = null;
  return makeResource({
    start() {
      if (timer) {
        throw new Error("Timer already started");
      }
      const promise = new Promise((resolve) => {
        timer = setTimeout(() => resolve(disposablePromiseTimerResult), ms);
      });
      return promise;
    }
  }, () => {
    if (timer) {
      clearTimeout(timer);
    }
  });
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/utils/createDeferred.mjs
function createDeferred() {
  let resolve;
  let reject;
  const promise = new Promise((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return {
    promise,
    resolve,
    reject
  };
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/utils/withPing.mjs
var PING_SYM = Symbol("ping");

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/jsonl.mjs
function _define_property3(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _ts_add_disposable_resource(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose, inner;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
      if (async) inner = dispose;
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    if (inner) dispose = function() {
      try {
        inner.call(this);
      } catch (e) {
        return Promise.reject(e);
      }
    };
    env.stack.push({
      value,
      dispose,
      async
    });
  } else if (async) {
    env.stack.push({
      async: true
    });
  }
  return value;
}
function _ts_dispose_resources(env) {
  var _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
  };
  return (_ts_dispose_resources = function _ts_dispose_resources3(env2) {
    function fail(e) {
      env2.error = env2.hasError ? new _SuppressedError(e, env2.error, "An error was suppressed during disposal.") : e;
      env2.hasError = true;
    }
    var r, s = 0;
    function next() {
      while (r = env2.stack.pop()) {
        try {
          if (!r.async && s === 1) return s = 0, env2.stack.push(r), Promise.resolve().then(next);
          if (r.dispose) {
            var result = r.dispose.call(r.value);
            if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {
              fail(e);
              return next();
            });
          } else s |= 1;
        } catch (e) {
          fail(e);
        }
      }
      if (s === 1) return env2.hasError ? Promise.reject(env2.error) : Promise.resolve();
      if (env2.hasError) throw env2.error;
    }
    return next();
  })(env);
}
var CHUNK_VALUE_TYPE_PROMISE = 0;
var CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;
var PROMISE_STATUS_FULFILLED = 0;
var PROMISE_STATUS_REJECTED = 1;
var ASYNC_ITERABLE_STATUS_RETURN = 0;
var ASYNC_ITERABLE_STATUS_YIELD = 1;
var ASYNC_ITERABLE_STATUS_ERROR = 2;
var AsyncError = class extends Error {
  constructor(data) {
    super("Received error from server"), _define_property3(this, "data", void 0), this.data = data;
  }
};
var nodeJsStreamToReaderEsque = (source) => {
  return {
    getReader() {
      const stream = new ReadableStream({
        start(controller) {
          source.on("data", (chunk) => {
            controller.enqueue(chunk);
          });
          source.on("end", () => {
            controller.close();
          });
          source.on("error", (error) => {
            controller.error(error);
          });
        }
      });
      return stream.getReader();
    }
  };
};
function createLineAccumulator(from) {
  const reader = "getReader" in from ? from.getReader() : nodeJsStreamToReaderEsque(from).getReader();
  let lineAggregate = "";
  return new ReadableStream({
    async pull(controller) {
      const { done, value } = await reader.read();
      if (done) {
        controller.close();
      } else {
        controller.enqueue(value);
      }
    },
    cancel() {
      return reader.cancel();
    }
  }).pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({
    transform(chunk, controller) {
      lineAggregate += chunk;
      const parts = lineAggregate.split("\n");
      lineAggregate = parts.pop() ?? "";
      for (const part of parts) {
        controller.enqueue(part);
      }
    }
  }));
}
function createConsumerStream(from) {
  const stream = createLineAccumulator(from);
  let sentHead = false;
  return stream.pipeThrough(new TransformStream({
    transform(line, controller) {
      if (!sentHead) {
        const head = JSON.parse(line);
        controller.enqueue(head);
        sentHead = true;
      } else {
        const chunk = JSON.parse(line);
        controller.enqueue(chunk);
      }
    }
  }));
}
function createStreamsManager(abortController) {
  const controllerMap = /* @__PURE__ */ new Map();
  function isEmpty() {
    return Array.from(controllerMap.values()).every((c) => c.closed);
  }
  function createStreamController() {
    let originalController;
    const stream = new ReadableStream({
      start(controller) {
        originalController = controller;
      }
    });
    const streamController = {
      enqueue: (v) => originalController.enqueue(v),
      close: () => {
        originalController.close();
        clear();
        if (isEmpty()) {
          abortController.abort();
        }
      },
      closed: false,
      getReaderResource: () => {
        const reader = stream.getReader();
        return makeResource(reader, () => {
          reader.releaseLock();
          streamController.close();
        });
      },
      error: (reason) => {
        originalController.error(reason);
        clear();
      }
    };
    function clear() {
      Object.assign(streamController, {
        closed: true,
        close: () => {
        },
        enqueue: () => {
        },
        getReaderResource: null,
        error: () => {
        }
      });
    }
    return streamController;
  }
  function getOrCreate(chunkId) {
    let c = controllerMap.get(chunkId);
    if (!c) {
      c = createStreamController();
      controllerMap.set(chunkId, c);
    }
    return c;
  }
  function cancelAll(reason) {
    for (const controller of controllerMap.values()) {
      controller.error(reason);
    }
  }
  return {
    getOrCreate,
    isEmpty,
    cancelAll
  };
}
async function jsonlStreamConsumer(opts) {
  const { deserialize = (v) => v } = opts;
  let source = createConsumerStream(opts.from);
  if (deserialize) {
    source = source.pipeThrough(new TransformStream({
      transform(chunk, controller) {
        controller.enqueue(deserialize(chunk));
      }
    }));
  }
  let headDeferred = createDeferred();
  const streamManager = createStreamsManager(opts.abortController);
  function decodeChunkDefinition(value) {
    const [_path, type, chunkId] = value;
    const controller = streamManager.getOrCreate(chunkId);
    switch (type) {
      case CHUNK_VALUE_TYPE_PROMISE: {
        return run(async () => {
          var _a2;
          const env = {
            stack: [],
            error: void 0,
            hasError: false
          };
          try {
            const reader = _ts_add_disposable_resource(env, controller.getReaderResource(), false);
            ;
            const { value: value2 } = await reader.read();
            const [_chunkId, status, data] = value2;
            switch (status) {
              case PROMISE_STATUS_FULFILLED:
                return decode(data);
              case PROMISE_STATUS_REJECTED:
                throw ((_a2 = opts.formatError) == null ? void 0 : _a2.call(opts, {
                  error: data
                })) ?? new AsyncError(data);
            }
          } catch (e) {
            env.error = e;
            env.hasError = true;
          } finally {
            _ts_dispose_resources(env);
          }
        });
      }
      case CHUNK_VALUE_TYPE_ASYNC_ITERABLE: {
        return run(async function* () {
          var _a2;
          const env = {
            stack: [],
            error: void 0,
            hasError: false
          };
          try {
            const reader = _ts_add_disposable_resource(env, controller.getReaderResource(), false);
            ;
            while (true) {
              const { value: value2 } = await reader.read();
              const [_chunkId, status, data] = value2;
              switch (status) {
                case ASYNC_ITERABLE_STATUS_YIELD:
                  yield decode(data);
                  break;
                case ASYNC_ITERABLE_STATUS_RETURN:
                  return decode(data);
                case ASYNC_ITERABLE_STATUS_ERROR:
                  throw ((_a2 = opts.formatError) == null ? void 0 : _a2.call(opts, {
                    error: data
                  })) ?? new AsyncError(data);
              }
            }
          } catch (e) {
            env.error = e;
            env.hasError = true;
          } finally {
            _ts_dispose_resources(env);
          }
        });
      }
    }
  }
  function decode(value) {
    const [[data], ...asyncProps] = value;
    for (const value2 of asyncProps) {
      const [key] = value2;
      const decoded = decodeChunkDefinition(value2);
      if (key === null) {
        return decoded;
      }
      data[key] = decoded;
    }
    return data;
  }
  const closeOrAbort = (reason) => {
    headDeferred == null ? void 0 : headDeferred.reject(reason);
    streamManager.cancelAll(reason);
  };
  source.pipeTo(new WritableStream({
    write(chunkOrHead) {
      if (headDeferred) {
        const head = chunkOrHead;
        for (const [key, value] of Object.entries(chunkOrHead)) {
          const parsed = decode(value);
          head[key] = parsed;
        }
        headDeferred.resolve(head);
        headDeferred = null;
        return;
      }
      const chunk = chunkOrHead;
      const [idx] = chunk;
      const controller = streamManager.getOrCreate(idx);
      controller.enqueue(chunk);
    },
    close: () => closeOrAbort(new Error("Stream closed")),
    abort: closeOrAbort
  }), {
    signal: opts.abortController.signal
  }).catch((error) => {
    var _a2;
    (_a2 = opts.onError) == null ? void 0 : _a2.call(opts, {
      error
    });
    closeOrAbort(error);
  });
  return [
    await headDeferred.promise,
    streamManager
  ];
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/tracked.mjs
var trackedSymbol = Symbol();

// node_modules/@trpc/server/dist/unstable-core-do-not-import/stream/sse.mjs
function _ts_add_disposable_resource2(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose, inner;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
      if (async) inner = dispose;
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    if (inner) dispose = function() {
      try {
        inner.call(this);
      } catch (e) {
        return Promise.reject(e);
      }
    };
    env.stack.push({
      value,
      dispose,
      async
    });
  } else if (async) {
    env.stack.push({
      async: true
    });
  }
  return value;
}
function _ts_dispose_resources2(env) {
  var _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
  };
  return (_ts_dispose_resources2 = function _ts_dispose_resources3(env2) {
    function fail(e) {
      env2.error = env2.hasError ? new _SuppressedError(e, env2.error, "An error was suppressed during disposal.") : e;
      env2.hasError = true;
    }
    var r, s = 0;
    function next() {
      while (r = env2.stack.pop()) {
        try {
          if (!r.async && s === 1) return s = 0, env2.stack.push(r), Promise.resolve().then(next);
          if (r.dispose) {
            var result = r.dispose.call(r.value);
            if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {
              fail(e);
              return next();
            });
          } else s |= 1;
        } catch (e) {
          fail(e);
        }
      }
      if (s === 1) return env2.hasError ? Promise.reject(env2.error) : Promise.resolve();
      if (env2.hasError) throw env2.error;
    }
    return next();
  })(env);
}
var PING_EVENT = "ping";
var SERIALIZED_ERROR_EVENT = "serialized-error";
var CONNECTED_EVENT = "connected";
var RETURN_EVENT = "return";
async function withTimeout(opts) {
  const env = {
    stack: [],
    error: void 0,
    hasError: false
  };
  try {
    const timeoutPromise = _ts_add_disposable_resource2(env, timerResource(opts.timeoutMs), false);
    ;
    const res = await Unpromise.race([
      opts.promise,
      timeoutPromise.start()
    ]);
    if (res === disposablePromiseTimerResult) {
      return await opts.onTimeout();
    }
    return res;
  } catch (e) {
    env.error = e;
    env.hasError = true;
  } finally {
    _ts_dispose_resources2(env);
  }
}
function sseStreamConsumer(opts) {
  const { deserialize = (v) => v } = opts;
  let clientOptions = {};
  const signal = opts.signal;
  let _es = null;
  const createStream = () => new ReadableStream({
    async start(controller) {
      const [url, init] = await Promise.all([
        opts.url(),
        opts.init()
      ]);
      const eventSource = _es = new opts.EventSource(url, init);
      controller.enqueue({
        type: "connecting",
        eventSource: _es,
        event: null
      });
      eventSource.addEventListener(CONNECTED_EVENT, (_msg) => {
        const msg = _msg;
        const options = JSON.parse(msg.data);
        clientOptions = options;
        controller.enqueue({
          type: "connected",
          options,
          eventSource
        });
      });
      eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg) => {
        const msg = _msg;
        controller.enqueue({
          type: "serialized-error",
          error: deserialize(JSON.parse(msg.data)),
          eventSource
        });
      });
      eventSource.addEventListener(PING_EVENT, () => {
        controller.enqueue({
          type: "ping",
          eventSource
        });
      });
      eventSource.addEventListener(RETURN_EVENT, () => {
        eventSource.close();
        controller.close();
        _es = null;
      });
      eventSource.addEventListener("error", (event) => {
        if (eventSource.readyState === eventSource.CLOSED) {
          controller.error(event);
        } else {
          controller.enqueue({
            type: "connecting",
            eventSource,
            event
          });
        }
      });
      eventSource.addEventListener("message", (_msg) => {
        const msg = _msg;
        const chunk = deserialize(JSON.parse(msg.data));
        const def = {
          data: chunk
        };
        if (msg.lastEventId) {
          def.id = msg.lastEventId;
        }
        controller.enqueue({
          type: "data",
          data: def,
          eventSource
        });
      });
      const onAbort = () => {
        try {
          eventSource.close();
          controller.close();
        } catch {
        }
      };
      if (signal.aborted) {
        onAbort();
      } else {
        signal.addEventListener("abort", onAbort);
      }
    },
    cancel() {
      _es == null ? void 0 : _es.close();
    }
  });
  const getStreamResource = () => {
    let stream = createStream();
    let reader = stream.getReader();
    async function dispose() {
      await reader.cancel();
      _es = null;
    }
    return makeAsyncResource({
      read() {
        return reader.read();
      },
      async recreate() {
        await dispose();
        stream = createStream();
        reader = stream.getReader();
      }
    }, dispose);
  };
  return run(async function* () {
    const env = {
      stack: [],
      error: void 0,
      hasError: false
    };
    try {
      const stream = _ts_add_disposable_resource2(env, getStreamResource(), true);
      ;
      while (true) {
        let promise = stream.read();
        const timeoutMs = clientOptions.reconnectAfterInactivityMs;
        if (timeoutMs) {
          promise = withTimeout({
            promise,
            timeoutMs,
            onTimeout: async () => {
              const res = {
                value: {
                  type: "timeout",
                  ms: timeoutMs,
                  eventSource: _es
                },
                done: false
              };
              await stream.recreate();
              return res;
            }
          });
        }
        const result = await promise;
        if (result.done) {
          return result.value;
        }
        yield result.value;
      }
    } catch (e) {
      env.error = e;
      env.hasError = true;
    } finally {
      const result = _ts_dispose_resources2(env);
      if (result) await result;
    }
  });
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/middleware.mjs
var middlewareMarker = "middlewareMarker";
function createMiddlewareFactory() {
  function createMiddlewareInner(middlewares) {
    return {
      _middlewares: middlewares,
      unstable_pipe(middlewareBuilderOrFn) {
        const pipedMiddleware = "_middlewares" in middlewareBuilderOrFn ? middlewareBuilderOrFn._middlewares : [
          middlewareBuilderOrFn
        ];
        return createMiddlewareInner([
          ...middlewares,
          ...pipedMiddleware
        ]);
      }
    };
  }
  function createMiddleware(fn) {
    return createMiddlewareInner([
      fn
    ]);
  }
  return createMiddleware;
}
function createInputMiddleware(parse) {
  const inputMiddleware = async function inputValidatorMiddleware(opts) {
    let parsedInput;
    const rawInput = await opts.getRawInput();
    try {
      parsedInput = await parse(rawInput);
    } catch (cause) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        cause
      });
    }
    const combinedInput = isObject(opts.input) && isObject(parsedInput) ? {
      ...opts.input,
      ...parsedInput
    } : parsedInput;
    return opts.next({
      input: combinedInput
    });
  };
  inputMiddleware._type = "input";
  return inputMiddleware;
}
function createOutputMiddleware(parse) {
  const outputMiddleware = async function outputValidatorMiddleware({ next }) {
    const result = await next();
    if (!result.ok) {
      return result;
    }
    try {
      const data = await parse(result.data);
      return {
        ...result,
        data
      };
    } catch (cause) {
      throw new TRPCError({
        message: "Output validation failed",
        code: "INTERNAL_SERVER_ERROR",
        cause
      });
    }
  };
  outputMiddleware._type = "output";
  return outputMiddleware;
}

// node_modules/@trpc/server/dist/vendor/standard-schema-v1/error.mjs
function _define_property4(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var StandardSchemaV1Error = class extends Error {
  /**
  * Creates a schema error with useful information.
  *
  * @param issues The schema issues.
  */
  constructor(issues) {
    var _a2;
    super((_a2 = issues[0]) == null ? void 0 : _a2.message), /** The schema issues. */
    _define_property4(this, "issues", void 0);
    this.name = "SchemaError";
    this.issues = issues;
  }
};

// node_modules/@trpc/server/dist/unstable-core-do-not-import/parser.mjs
function getParseFn(procedureParser) {
  const parser = procedureParser;
  const isStandardSchema = "~standard" in parser;
  if (typeof parser === "function" && typeof parser.assert === "function") {
    return parser.assert.bind(parser);
  }
  if (typeof parser === "function" && !isStandardSchema) {
    return parser;
  }
  if (typeof parser.parseAsync === "function") {
    return parser.parseAsync.bind(parser);
  }
  if (typeof parser.parse === "function") {
    return parser.parse.bind(parser);
  }
  if (typeof parser.validateSync === "function") {
    return parser.validateSync.bind(parser);
  }
  if (typeof parser.create === "function") {
    return parser.create.bind(parser);
  }
  if (typeof parser.assert === "function") {
    return (value) => {
      parser.assert(value);
      return value;
    };
  }
  if (isStandardSchema) {
    return async (value) => {
      const result = await parser["~standard"].validate(value);
      if (result.issues) {
        throw new StandardSchemaV1Error(result.issues);
      }
      return result.value;
    };
  }
  throw new Error("Could not find a validator fn");
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/procedureBuilder.mjs
function createNewBuilder(def1, def2) {
  const { middlewares = [], inputs, meta, ...rest } = def2;
  return createBuilder({
    ...mergeWithoutOverrides(def1, rest),
    inputs: [
      ...def1.inputs,
      ...inputs ?? []
    ],
    middlewares: [
      ...def1.middlewares,
      ...middlewares
    ],
    meta: def1.meta && meta ? {
      ...def1.meta,
      ...meta
    } : meta ?? def1.meta
  });
}
function createBuilder(initDef = {}) {
  const _def = {
    procedure: true,
    inputs: [],
    middlewares: [],
    ...initDef
  };
  const builder = {
    _def,
    input(input) {
      const parser = getParseFn(input);
      return createNewBuilder(_def, {
        inputs: [
          input
        ],
        middlewares: [
          createInputMiddleware(parser)
        ]
      });
    },
    output(output) {
      const parser = getParseFn(output);
      return createNewBuilder(_def, {
        output,
        middlewares: [
          createOutputMiddleware(parser)
        ]
      });
    },
    meta(meta) {
      return createNewBuilder(_def, {
        meta
      });
    },
    use(middlewareBuilderOrFn) {
      const middlewares = "_middlewares" in middlewareBuilderOrFn ? middlewareBuilderOrFn._middlewares : [
        middlewareBuilderOrFn
      ];
      return createNewBuilder(_def, {
        middlewares
      });
    },
    unstable_concat(builder2) {
      return createNewBuilder(_def, builder2._def);
    },
    concat(builder2) {
      return createNewBuilder(_def, builder2._def);
    },
    query(resolver) {
      return createResolver({
        ..._def,
        type: "query"
      }, resolver);
    },
    mutation(resolver) {
      return createResolver({
        ..._def,
        type: "mutation"
      }, resolver);
    },
    subscription(resolver) {
      return createResolver({
        ..._def,
        type: "subscription"
      }, resolver);
    },
    experimental_caller(caller) {
      return createNewBuilder(_def, {
        caller
      });
    }
  };
  return builder;
}
function createResolver(_defIn, resolver) {
  const finalBuilder = createNewBuilder(_defIn, {
    resolver,
    middlewares: [
      async function resolveMiddleware(opts) {
        const data = await resolver(opts);
        return {
          marker: middlewareMarker,
          ok: true,
          data,
          ctx: opts.ctx
        };
      }
    ]
  });
  const _def = {
    ...finalBuilder._def,
    type: _defIn.type,
    experimental_caller: Boolean(finalBuilder._def.caller),
    meta: finalBuilder._def.meta,
    $types: null
  };
  const invoke = createProcedureCaller(finalBuilder._def);
  const callerOverride = finalBuilder._def.caller;
  if (!callerOverride) {
    return invoke;
  }
  const callerWrapper = async (...args) => {
    return await callerOverride({
      args,
      invoke,
      _def
    });
  };
  callerWrapper._def = _def;
  return callerWrapper;
}
var codeblock = `
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();
async function callRecursive(index, _def, opts) {
  try {
    const middleware = _def.middlewares[index];
    const result = await middleware({
      ...opts,
      meta: _def.meta,
      input: opts.input,
      next(_nextOpts) {
        const nextOpts = _nextOpts;
        return callRecursive(index + 1, _def, {
          ...opts,
          ctx: (nextOpts == null ? void 0 : nextOpts.ctx) ? {
            ...opts.ctx,
            ...nextOpts.ctx
          } : opts.ctx,
          input: nextOpts && "input" in nextOpts ? nextOpts.input : opts.input,
          getRawInput: (nextOpts == null ? void 0 : nextOpts.getRawInput) ?? opts.getRawInput
        });
      }
    });
    return result;
  } catch (cause) {
    return {
      ok: false,
      error: getTRPCErrorFromUnknown(cause),
      marker: middlewareMarker
    };
  }
}
function createProcedureCaller(_def) {
  async function procedure(opts) {
    if (!opts || !("getRawInput" in opts)) {
      throw new Error(codeblock);
    }
    const result = await callRecursive(0, _def, opts);
    if (!result) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "No result from middlewares - did you forget to `return next()`?"
      });
    }
    if (!result.ok) {
      throw result.error;
    }
    return result.data;
  }
  procedure._def = _def;
  procedure.procedure = true;
  procedure.meta = _def.meta;
  return procedure;
}

// node_modules/@trpc/server/dist/unstable-core-do-not-import/rootConfig.mjs
var _a, _b, _c, _d, _e, _f;
var isServerDefault = typeof window === "undefined" || "Deno" in window || // eslint-disable-next-line @typescript-eslint/dot-notation
((_b = (_a = globalThis.process) == null ? void 0 : _a.env) == null ? void 0 : _b["NODE_ENV"]) === "test" || !!((_d = (_c = globalThis.process) == null ? void 0 : _c.env) == null ? void 0 : _d["JEST_WORKER_ID"]) || !!((_f = (_e = globalThis.process) == null ? void 0 : _e.env) == null ? void 0 : _f["VITEST_WORKER_ID"]);

// node_modules/@trpc/server/dist/unstable-core-do-not-import/initTRPC.mjs
var TRPCBuilder = class _TRPCBuilder {
  /**
  * Add a context shape as a generic to the root object
  * @see https://trpc.io/docs/v11/server/context
  */
  context() {
    return new _TRPCBuilder();
  }
  /**
  * Add a meta shape as a generic to the root object
  * @see https://trpc.io/docs/v11/quickstart
  */
  meta() {
    return new _TRPCBuilder();
  }
  /**
  * Create the root object
  * @see https://trpc.io/docs/v11/server/routers#initialize-trpc
  */
  create(opts) {
    var _a2;
    const config = {
      ...opts,
      transformer: getDataTransformer((opts == null ? void 0 : opts.transformer) ?? defaultTransformer),
      isDev: (opts == null ? void 0 : opts.isDev) ?? // eslint-disable-next-line @typescript-eslint/dot-notation
      ((_a2 = globalThis.process) == null ? void 0 : _a2.env["NODE_ENV"]) !== "production",
      allowOutsideOfServer: (opts == null ? void 0 : opts.allowOutsideOfServer) ?? false,
      errorFormatter: (opts == null ? void 0 : opts.errorFormatter) ?? defaultFormatter,
      isServer: (opts == null ? void 0 : opts.isServer) ?? isServerDefault,
      /**
      * These are just types, they can't be used at runtime
      * @internal
      */
      $types: null
    };
    {
      const isServer = (opts == null ? void 0 : opts.isServer) ?? isServerDefault;
      if (!isServer && (opts == null ? void 0 : opts.allowOutsideOfServer) !== true) {
        throw new Error(`You're trying to use @trpc/server in a non-server environment. This is not supported by default.`);
      }
    }
    return {
      /**
      * Your router config
      * @internal
      */
      _config: config,
      /**
      * Builder object for creating procedures
      * @see https://trpc.io/docs/v11/server/procedures
      */
      procedure: createBuilder({
        meta: opts == null ? void 0 : opts.defaultMeta
      }),
      /**
      * Create reusable middlewares
      * @see https://trpc.io/docs/v11/server/middlewares
      */
      middleware: createMiddlewareFactory(),
      /**
      * Create a router
      * @see https://trpc.io/docs/v11/server/routers
      */
      router: createRouterFactory(config),
      /**
      * Merge Routers
      * @see https://trpc.io/docs/v11/server/merging-routers
      */
      mergeRouters,
      /**
      * Create a server-side caller for a router
      * @see https://trpc.io/docs/v11/server/server-side-calls
      */
      createCallerFactory: createCallerFactory()
    };
  }
};
var initTRPC = new TRPCBuilder();

// node_modules/@trpc/client/dist/TRPCClientError.mjs
function _define_property5(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function isTRPCClientError(cause) {
  return cause instanceof TRPCClientError || /**
  * @deprecated
  * Delete in next major
  */
  cause instanceof Error && cause.name === "TRPCClientError";
}
function isTRPCErrorResponse(obj) {
  return isObject(obj) && isObject(obj["error"]) && typeof obj["error"]["code"] === "number" && typeof obj["error"]["message"] === "string";
}
function getMessageFromUnknownError(err, fallback) {
  if (typeof err === "string") {
    return err;
  }
  if (isObject(err) && typeof err["message"] === "string") {
    return err["message"];
  }
  return fallback;
}
var TRPCClientError = class _TRPCClientError extends Error {
  static from(_cause, opts = {}) {
    const cause = _cause;
    if (isTRPCClientError(cause)) {
      if (opts.meta) {
        cause.meta = {
          ...cause.meta,
          ...opts.meta
        };
      }
      return cause;
    }
    if (isTRPCErrorResponse(cause)) {
      return new _TRPCClientError(cause.error.message, {
        ...opts,
        result: cause
      });
    }
    return new _TRPCClientError(getMessageFromUnknownError(cause, "Unknown error"), {
      ...opts,
      cause
    });
  }
  constructor(message, opts) {
    var _a2, _b2;
    const cause = opts == null ? void 0 : opts.cause;
    super(message, {
      cause
    }), // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore override doesn't work in all environments due to "This member cannot have an 'override' modifier because it is not declared in the base class 'Error'"
    _define_property5(this, "cause", void 0), _define_property5(this, "shape", void 0), _define_property5(this, "data", void 0), /**
    * Additional meta data about the error
    * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here
    */
    _define_property5(this, "meta", void 0);
    this.meta = opts == null ? void 0 : opts.meta;
    this.cause = cause;
    this.shape = (_a2 = opts == null ? void 0 : opts.result) == null ? void 0 : _a2.error;
    this.data = (_b2 = opts == null ? void 0 : opts.result) == null ? void 0 : _b2.error.data;
    this.name = "TRPCClientError";
    Object.setPrototypeOf(this, _TRPCClientError.prototype);
  }
};

// node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs
function _define_property6(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var TRPCUntypedClient = class {
  $request(opts) {
    const chain$ = createChain({
      links: this.links,
      op: {
        ...opts,
        context: opts.context ?? {},
        id: ++this.requestId
      }
    });
    return chain$.pipe(share());
  }
  async requestAsPromise(opts) {
    try {
      const req$ = this.$request(opts);
      const envelope = await observableToPromise(req$);
      const data = envelope.result.data;
      return data;
    } catch (err) {
      throw TRPCClientError.from(err);
    }
  }
  query(path, input, opts) {
    return this.requestAsPromise({
      type: "query",
      path,
      input,
      context: opts == null ? void 0 : opts.context,
      signal: opts == null ? void 0 : opts.signal
    });
  }
  mutation(path, input, opts) {
    return this.requestAsPromise({
      type: "mutation",
      path,
      input,
      context: opts == null ? void 0 : opts.context,
      signal: opts == null ? void 0 : opts.signal
    });
  }
  subscription(path, input, opts) {
    const observable$ = this.$request({
      type: "subscription",
      path,
      input,
      context: opts.context,
      signal: opts.signal
    });
    return observable$.subscribe({
      next(envelope) {
        var _a2, _b2, _c2, _d2;
        switch (envelope.result.type) {
          case "state": {
            (_a2 = opts.onConnectionStateChange) == null ? void 0 : _a2.call(opts, envelope.result);
            break;
          }
          case "started": {
            (_b2 = opts.onStarted) == null ? void 0 : _b2.call(opts, {
              context: envelope.context
            });
            break;
          }
          case "stopped": {
            (_c2 = opts.onStopped) == null ? void 0 : _c2.call(opts);
            break;
          }
          case "data":
          case void 0: {
            (_d2 = opts.onData) == null ? void 0 : _d2.call(opts, envelope.result.data);
            break;
          }
        }
      },
      error(err) {
        var _a2;
        (_a2 = opts.onError) == null ? void 0 : _a2.call(opts, err);
      },
      complete() {
        var _a2;
        (_a2 = opts.onComplete) == null ? void 0 : _a2.call(opts);
      }
    });
  }
  constructor(opts) {
    _define_property6(this, "links", void 0);
    _define_property6(this, "runtime", void 0);
    _define_property6(this, "requestId", void 0);
    this.requestId = 0;
    this.runtime = {};
    this.links = opts.links.map((link) => link(this.runtime));
  }
};

// node_modules/@trpc/client/dist/createTRPCUntypedClient.mjs
function createTRPCUntypedClient(opts) {
  return new TRPCUntypedClient(opts);
}

// node_modules/@trpc/client/dist/createTRPCClient.mjs
var untypedClientSymbol = Symbol.for("trpc_untypedClient");
var clientCallTypeMap = {
  query: "query",
  mutate: "mutation",
  subscribe: "subscription"
};
var clientCallTypeToProcedureType = (clientCallType) => {
  return clientCallTypeMap[clientCallType];
};
function createTRPCClientProxy(client) {
  const proxy = createRecursiveProxy(({ path, args }) => {
    const pathCopy = [
      ...path
    ];
    const procedureType = clientCallTypeToProcedureType(pathCopy.pop());
    const fullPath = pathCopy.join(".");
    return client[procedureType](fullPath, ...args);
  });
  return createFlatProxy((key) => {
    if (key === untypedClientSymbol) {
      return client;
    }
    return proxy[key];
  });
}
function createTRPCClient(opts) {
  const client = new TRPCUntypedClient(opts);
  const proxy = createTRPCClientProxy(client);
  return proxy;
}
function getUntypedClient(client) {
  return client[untypedClientSymbol];
}

// node_modules/@trpc/client/dist/getFetch.mjs
var isFunction2 = (fn) => typeof fn === "function";
function getFetch(customFetchImpl) {
  if (customFetchImpl) {
    return customFetchImpl;
  }
  if (typeof window !== "undefined" && isFunction2(window.fetch)) {
    return window.fetch;
  }
  if (typeof globalThis !== "undefined" && isFunction2(globalThis.fetch)) {
    return globalThis.fetch;
  }
  throw new Error("No fetch implementation found");
}

// node_modules/@trpc/client/dist/links/internals/contentTypes.mjs
function isOctetType(input) {
  return input instanceof Uint8Array || // File extends from Blob but is only available in nodejs from v20
  input instanceof Blob;
}
function isFormData(input) {
  return input instanceof FormData;
}
function isNonJsonSerializable(input) {
  return isOctetType(input) || isFormData(input);
}

// node_modules/@trpc/client/dist/internals/dataLoader.mjs
var throwFatalError = () => {
  throw new Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new");
};
function dataLoader(batchLoader) {
  let pendingItems = null;
  let dispatchTimer = null;
  const destroyTimerAndPendingItems = () => {
    clearTimeout(dispatchTimer);
    dispatchTimer = null;
    pendingItems = null;
  };
  function groupItems(items) {
    var _a2, _b2;
    const groupedItems = [
      []
    ];
    let index = 0;
    while (true) {
      const item = items[index];
      if (!item) {
        break;
      }
      const lastGroup = groupedItems[groupedItems.length - 1];
      if (item.aborted) {
        (_a2 = item.reject) == null ? void 0 : _a2.call(item, new Error("Aborted"));
        index++;
        continue;
      }
      const isValid = batchLoader.validate(lastGroup.concat(item).map((it) => it.key));
      if (isValid) {
        lastGroup.push(item);
        index++;
        continue;
      }
      if (lastGroup.length === 0) {
        (_b2 = item.reject) == null ? void 0 : _b2.call(item, new Error("Input is too big for a single dispatch"));
        index++;
        continue;
      }
      groupedItems.push([]);
    }
    return groupedItems;
  }
  function dispatch() {
    const groupedItems = groupItems(pendingItems);
    destroyTimerAndPendingItems();
    for (const items of groupedItems) {
      if (!items.length) {
        continue;
      }
      const batch = {
        items
      };
      for (const item of items) {
        item.batch = batch;
      }
      const promise = batchLoader.fetch(batch.items.map((_item) => _item.key));
      promise.then(async (result) => {
        var _a2;
        await Promise.all(result.map(async (valueOrPromise, index) => {
          var _a3, _b2;
          const item = batch.items[index];
          try {
            const value = await Promise.resolve(valueOrPromise);
            (_a3 = item.resolve) == null ? void 0 : _a3.call(item, value);
          } catch (cause) {
            (_b2 = item.reject) == null ? void 0 : _b2.call(item, cause);
          }
          item.batch = null;
          item.reject = null;
          item.resolve = null;
        }));
        for (const item of batch.items) {
          (_a2 = item.reject) == null ? void 0 : _a2.call(item, new Error("Missing result"));
          item.batch = null;
        }
      }).catch((cause) => {
        var _a2;
        for (const item of batch.items) {
          (_a2 = item.reject) == null ? void 0 : _a2.call(item, cause);
          item.batch = null;
        }
      });
    }
  }
  function load(key) {
    const item = {
      aborted: false,
      key,
      batch: null,
      resolve: throwFatalError,
      reject: throwFatalError
    };
    const promise = new Promise((resolve, reject) => {
      item.reject = reject;
      item.resolve = resolve;
      pendingItems ?? (pendingItems = []);
      pendingItems.push(item);
    });
    dispatchTimer ?? (dispatchTimer = setTimeout(dispatch));
    return promise;
  }
  return {
    load
  };
}

// node_modules/@trpc/client/dist/internals/signals.mjs
function allAbortSignals(...signals) {
  const ac = new AbortController();
  const count = signals.length;
  let abortedCount = 0;
  const onAbort = () => {
    if (++abortedCount === count) {
      ac.abort();
    }
  };
  for (const signal of signals) {
    if (signal == null ? void 0 : signal.aborted) {
      onAbort();
    } else {
      signal == null ? void 0 : signal.addEventListener("abort", onAbort, {
        once: true
      });
    }
  }
  return ac.signal;
}
function raceAbortSignals(...signals) {
  const ac = new AbortController();
  for (const signal of signals) {
    if (signal == null ? void 0 : signal.aborted) {
      ac.abort();
    } else {
      signal == null ? void 0 : signal.addEventListener("abort", () => ac.abort(), {
        once: true
      });
    }
  }
  return ac.signal;
}

// node_modules/@trpc/client/dist/internals/transformer.mjs
function getTransformer(transformer) {
  const _transformer = transformer;
  if (!_transformer) {
    return {
      input: {
        serialize: (data) => data,
        deserialize: (data) => data
      },
      output: {
        serialize: (data) => data,
        deserialize: (data) => data
      }
    };
  }
  if ("input" in _transformer) {
    return _transformer;
  }
  return {
    input: _transformer,
    output: _transformer
  };
}

// node_modules/@trpc/client/dist/links/internals/httpUtils.mjs
function resolveHTTPLinkOptions(opts) {
  return {
    url: opts.url.toString(),
    fetch: opts.fetch,
    transformer: getTransformer(opts.transformer),
    methodOverride: opts.methodOverride
  };
}
function arrayToDict(array) {
  const dict = {};
  for (let index = 0; index < array.length; index++) {
    const element = array[index];
    dict[index] = element;
  }
  return dict;
}
var METHOD = {
  query: "GET",
  mutation: "POST",
  subscription: "PATCH"
};
function getInput(opts) {
  return "input" in opts ? opts.transformer.input.serialize(opts.input) : arrayToDict(opts.inputs.map((_input) => opts.transformer.input.serialize(_input)));
}
var getUrl = (opts) => {
  const parts = opts.url.split("?");
  const base = parts[0].replace(/\/$/, "");
  let url = base + "/" + opts.path;
  const queryParts = [];
  if (parts[1]) {
    queryParts.push(parts[1]);
  }
  if ("inputs" in opts) {
    queryParts.push("batch=1");
  }
  if (opts.type === "query" || opts.type === "subscription") {
    const input = getInput(opts);
    if (input !== void 0 && opts.methodOverride !== "POST") {
      queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);
    }
  }
  if (queryParts.length) {
    url += "?" + queryParts.join("&");
  }
  return url;
};
var getBody = (opts) => {
  if (opts.type === "query" && opts.methodOverride !== "POST") {
    return void 0;
  }
  const input = getInput(opts);
  return input !== void 0 ? JSON.stringify(input) : void 0;
};
var jsonHttpRequester = (opts) => {
  return httpRequest({
    ...opts,
    contentTypeHeader: "application/json",
    getUrl,
    getBody
  });
};
var AbortError = class extends Error {
  constructor() {
    const name = "AbortError";
    super(name);
    this.name = name;
    this.message = name;
  }
};
var throwIfAborted = (signal) => {
  var _a2;
  if (!(signal == null ? void 0 : signal.aborted)) {
    return;
  }
  (_a2 = signal.throwIfAborted) == null ? void 0 : _a2.call(signal);
  if (typeof DOMException !== "undefined") {
    throw new DOMException("AbortError", "AbortError");
  }
  throw new AbortError();
};
async function fetchHTTPResponse(opts) {
  throwIfAborted(opts.signal);
  const url = opts.getUrl(opts);
  const body = opts.getBody(opts);
  const { type } = opts;
  const resolvedHeaders = await (async () => {
    const heads = await opts.headers();
    if (Symbol.iterator in heads) {
      return Object.fromEntries(heads);
    }
    return heads;
  })();
  const headers = {
    ...opts.contentTypeHeader ? {
      "content-type": opts.contentTypeHeader
    } : {},
    ...opts.trpcAcceptHeader ? {
      "trpc-accept": opts.trpcAcceptHeader
    } : void 0,
    ...resolvedHeaders
  };
  return getFetch(opts.fetch)(url, {
    method: opts.methodOverride ?? METHOD[type],
    signal: opts.signal,
    body,
    headers
  });
}
async function httpRequest(opts) {
  const meta = {};
  const res = await fetchHTTPResponse(opts);
  meta.response = res;
  const json = await res.json();
  meta.responseJSON = json;
  return {
    json,
    meta
  };
}

// node_modules/@trpc/client/dist/links/httpBatchLink.mjs
function httpBatchLink(opts) {
  const resolvedOpts = resolveHTTPLinkOptions(opts);
  const maxURLLength = opts.maxURLLength ?? Infinity;
  const maxItems = opts.maxItems ?? Infinity;
  return () => {
    const batchLoader = (type) => {
      return {
        validate(batchOps) {
          if (maxURLLength === Infinity && maxItems === Infinity) {
            return true;
          }
          if (batchOps.length > maxItems) {
            return false;
          }
          const path = batchOps.map((op) => op.path).join(",");
          const inputs = batchOps.map((op) => op.input);
          const url = getUrl({
            ...resolvedOpts,
            type,
            path,
            inputs,
            signal: null
          });
          return url.length <= maxURLLength;
        },
        async fetch(batchOps) {
          const path = batchOps.map((op) => op.path).join(",");
          const inputs = batchOps.map((op) => op.input);
          const signal = allAbortSignals(...batchOps.map((op) => op.signal));
          const res = await jsonHttpRequester({
            ...resolvedOpts,
            path,
            inputs,
            type,
            headers() {
              if (!opts.headers) {
                return {};
              }
              if (typeof opts.headers === "function") {
                return opts.headers({
                  opList: batchOps
                });
              }
              return opts.headers;
            },
            signal
          });
          const resJSON = Array.isArray(res.json) ? res.json : batchOps.map(() => res.json);
          const result = resJSON.map((item) => ({
            meta: res.meta,
            json: item
          }));
          return result;
        }
      };
    };
    const query = dataLoader(batchLoader("query"));
    const mutation = dataLoader(batchLoader("mutation"));
    const loaders = {
      query,
      mutation
    };
    return ({ op }) => {
      return observable((observer) => {
        if (op.type === "subscription") {
          throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");
        }
        const loader = loaders[op.type];
        const promise = loader.load(op);
        let _res = void 0;
        promise.then((res) => {
          _res = res;
          const transformed = transformResult(res.json, resolvedOpts.transformer.output);
          if (!transformed.ok) {
            observer.error(TRPCClientError.from(transformed.error, {
              meta: res.meta
            }));
            return;
          }
          observer.next({
            context: res.meta,
            result: transformed.result
          });
          observer.complete();
        }).catch((err) => {
          observer.error(TRPCClientError.from(err, {
            meta: _res == null ? void 0 : _res.meta
          }));
        });
        return () => {
        };
      });
    };
  };
}

// node_modules/@trpc/client/dist/links/httpBatchStreamLink.mjs
function httpBatchStreamLink(opts) {
  const resolvedOpts = resolveHTTPLinkOptions(opts);
  const maxURLLength = opts.maxURLLength ?? Infinity;
  const maxItems = opts.maxItems ?? Infinity;
  return () => {
    const batchLoader = (type) => {
      return {
        validate(batchOps) {
          if (maxURLLength === Infinity && maxItems === Infinity) {
            return true;
          }
          if (batchOps.length > maxItems) {
            return false;
          }
          const path = batchOps.map((op) => op.path).join(",");
          const inputs = batchOps.map((op) => op.input);
          const url = getUrl({
            ...resolvedOpts,
            type,
            path,
            inputs,
            signal: null
          });
          return url.length <= maxURLLength;
        },
        async fetch(batchOps) {
          const path = batchOps.map((op) => op.path).join(",");
          const inputs = batchOps.map((op) => op.input);
          const batchSignals = allAbortSignals(...batchOps.map((op) => op.signal));
          const abortController = new AbortController();
          const responsePromise = fetchHTTPResponse({
            ...resolvedOpts,
            signal: raceAbortSignals(batchSignals, abortController.signal),
            type,
            contentTypeHeader: "application/json",
            trpcAcceptHeader: "application/jsonl",
            getUrl,
            getBody,
            inputs,
            path,
            headers() {
              if (!opts.headers) {
                return {};
              }
              if (typeof opts.headers === "function") {
                return opts.headers({
                  opList: batchOps
                });
              }
              return opts.headers;
            }
          });
          const res = await responsePromise;
          const [head] = await jsonlStreamConsumer({
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            from: res.body,
            deserialize: resolvedOpts.transformer.output.deserialize,
            // onError: console.error,
            formatError(opts2) {
              const error = opts2.error;
              return TRPCClientError.from({
                error
              });
            },
            abortController
          });
          const promises = Object.keys(batchOps).map(async (key) => {
            let json = await Promise.resolve(head[key]);
            if ("result" in json) {
              const result = await Promise.resolve(json.result);
              json = {
                result: {
                  data: await Promise.resolve(result.data)
                }
              };
            }
            return {
              json,
              meta: {
                response: res
              }
            };
          });
          return promises;
        }
      };
    };
    const query = dataLoader(batchLoader("query"));
    const mutation = dataLoader(batchLoader("mutation"));
    const loaders = {
      query,
      mutation
    };
    return ({ op }) => {
      return observable((observer) => {
        if (op.type === "subscription") {
          throw new Error("Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`");
        }
        const loader = loaders[op.type];
        const promise = loader.load(op);
        let _res = void 0;
        promise.then((res) => {
          _res = res;
          if ("error" in res.json) {
            observer.error(TRPCClientError.from(res.json, {
              meta: res.meta
            }));
            return;
          } else if ("result" in res.json) {
            observer.next({
              context: res.meta,
              result: res.json.result
            });
            observer.complete();
            return;
          }
          observer.complete();
        }).catch((err) => {
          observer.error(TRPCClientError.from(err, {
            meta: _res == null ? void 0 : _res.meta
          }));
        });
        return () => {
        };
      });
    };
  };
}
var unstable_httpBatchStreamLink = httpBatchStreamLink;

// node_modules/@trpc/client/dist/links/httpLink.mjs
var universalRequester = (opts) => {
  if ("input" in opts) {
    const { input } = opts;
    if (isFormData(input)) {
      if (opts.type !== "mutation" && opts.methodOverride !== "POST") {
        throw new Error("FormData is only supported for mutations");
      }
      return httpRequest({
        ...opts,
        // The browser will set this automatically and include the boundary= in it
        contentTypeHeader: void 0,
        getUrl,
        getBody: () => input
      });
    }
    if (isOctetType(input)) {
      if (opts.type !== "mutation" && opts.methodOverride !== "POST") {
        throw new Error("Octet type input is only supported for mutations");
      }
      return httpRequest({
        ...opts,
        contentTypeHeader: "application/octet-stream",
        getUrl,
        getBody: () => input
      });
    }
  }
  return jsonHttpRequester(opts);
};
function httpLink(opts) {
  const resolvedOpts = resolveHTTPLinkOptions(opts);
  return () => {
    return ({ op }) => {
      return observable((observer) => {
        const { path, input, type } = op;
        if (type === "subscription") {
          throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");
        }
        const request = universalRequester({
          ...resolvedOpts,
          type,
          path,
          input,
          signal: op.signal,
          headers() {
            if (!opts.headers) {
              return {};
            }
            if (typeof opts.headers === "function") {
              return opts.headers({
                op
              });
            }
            return opts.headers;
          }
        });
        let meta = void 0;
        request.then((res) => {
          meta = res.meta;
          const transformed = transformResult(res.json, resolvedOpts.transformer.output);
          if (!transformed.ok) {
            observer.error(TRPCClientError.from(transformed.error, {
              meta
            }));
            return;
          }
          observer.next({
            context: res.meta,
            result: transformed.result
          });
          observer.complete();
        }).catch((cause) => {
          observer.error(TRPCClientError.from(cause, {
            meta
          }));
        });
        return () => {
        };
      });
    };
  };
}

// node_modules/@trpc/client/dist/links/loggerLink.mjs
function isFormData2(value) {
  if (typeof FormData === "undefined") {
    return false;
  }
  return value instanceof FormData;
}
var palettes = {
  css: {
    query: [
      "72e3ff",
      "3fb0d8"
    ],
    mutation: [
      "c5a3fc",
      "904dfc"
    ],
    subscription: [
      "ff49e1",
      "d83fbe"
    ]
  },
  ansi: {
    regular: {
      // Cyan background, black and white text respectively
      query: [
        "\x1B[30;46m",
        "\x1B[97;46m"
      ],
      // Magenta background, black and white text respectively
      mutation: [
        "\x1B[30;45m",
        "\x1B[97;45m"
      ],
      // Green background, black and white text respectively
      subscription: [
        "\x1B[30;42m",
        "\x1B[97;42m"
      ]
    },
    bold: {
      query: [
        "\x1B[1;30;46m",
        "\x1B[1;97;46m"
      ],
      mutation: [
        "\x1B[1;30;45m",
        "\x1B[1;97;45m"
      ],
      subscription: [
        "\x1B[1;30;42m",
        "\x1B[1;97;42m"
      ]
    }
  }
};
function constructPartsAndArgs(opts) {
  const { direction, type, withContext, path, id, input } = opts;
  const parts = [];
  const args = [];
  if (opts.colorMode === "none") {
    parts.push(direction === "up" ? ">>" : "<<", type, `#${id}`, path);
  } else if (opts.colorMode === "ansi") {
    const [lightRegular, darkRegular] = palettes.ansi.regular[type];
    const [lightBold, darkBold] = palettes.ansi.bold[type];
    const reset = "\x1B[0m";
    parts.push(direction === "up" ? lightRegular : darkRegular, direction === "up" ? ">>" : "<<", type, direction === "up" ? lightBold : darkBold, `#${id}`, path, reset);
  } else {
    const [light, dark] = palettes.css[type];
    const css = `
    background-color: #${direction === "up" ? light : dark};
    color: ${direction === "up" ? "black" : "white"};
    padding: 2px;
  `;
    parts.push("%c", direction === "up" ? ">>" : "<<", type, `#${id}`, `%c${path}%c`, "%O");
    args.push(css, `${css}; font-weight: bold;`, `${css}; font-weight: normal;`);
  }
  if (direction === "up") {
    args.push(withContext ? {
      input,
      context: opts.context
    } : {
      input
    });
  } else {
    args.push({
      input,
      result: opts.result,
      elapsedMs: opts.elapsedMs,
      ...withContext && {
        context: opts.context
      }
    });
  }
  return {
    parts,
    args
  };
}
var defaultLogger = ({ c = console, colorMode = "css", withContext }) => (props) => {
  const rawInput = props.input;
  const input = isFormData2(rawInput) ? Object.fromEntries(rawInput) : rawInput;
  const { parts, args } = constructPartsAndArgs({
    ...props,
    colorMode,
    input,
    withContext
  });
  const fn = props.direction === "down" && props.result && (props.result instanceof Error || "error" in props.result.result && props.result.result.error) ? "error" : "log";
  c[fn].apply(null, [
    parts.join(" ")
  ].concat(args));
};
function loggerLink(opts = {}) {
  const { enabled = () => true } = opts;
  const colorMode = opts.colorMode ?? (typeof window === "undefined" ? "ansi" : "css");
  const withContext = opts.withContext ?? colorMode === "css";
  const { logger = defaultLogger({
    c: opts.console,
    colorMode,
    withContext
  }) } = opts;
  return () => {
    return ({ op, next }) => {
      return observable((observer) => {
        if (enabled({
          ...op,
          direction: "up"
        })) {
          logger({
            ...op,
            direction: "up"
          });
        }
        const requestStartTime = Date.now();
        function logResult(result) {
          const elapsedMs = Date.now() - requestStartTime;
          if (enabled({
            ...op,
            direction: "down",
            result
          })) {
            logger({
              ...op,
              direction: "down",
              elapsedMs,
              result
            });
          }
        }
        return next(op).pipe(tap({
          next(result) {
            logResult(result);
          },
          error(result) {
            logResult(result);
          }
        })).subscribe(observer);
      });
    };
  };
}

// node_modules/@trpc/client/dist/links/splitLink.mjs
function asArray(value) {
  return Array.isArray(value) ? value : [
    value
  ];
}
function splitLink(opts) {
  return (runtime) => {
    const yes = asArray(opts.true).map((link) => link(runtime));
    const no = asArray(opts.false).map((link) => link(runtime));
    return (props) => {
      return observable((observer) => {
        const links = opts.condition(props.op) ? yes : no;
        return createChain({
          op: props.op,
          links
        }).subscribe(observer);
      });
    };
  };
}

// node_modules/@trpc/client/dist/links/wsLink/wsClient/options.mjs
var lazyDefaults = {
  enabled: false,
  closeMs: 0
};
var keepAliveDefaults = {
  enabled: false,
  pongTimeoutMs: 1e3,
  intervalMs: 5e3
};
var exponentialBackoff = (attemptIndex) => {
  return attemptIndex === 0 ? 0 : Math.min(1e3 * 2 ** attemptIndex, 3e4);
};

// node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs
var resultOf = (value, ...args) => {
  return typeof value === "function" ? value(...args) : value;
};

// node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs
function _define_property7(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var TRPCWebSocketClosedError = class _TRPCWebSocketClosedError extends Error {
  constructor(opts) {
    super(opts.message, {
      cause: opts.cause
    });
    this.name = "TRPCWebSocketClosedError";
    Object.setPrototypeOf(this, _TRPCWebSocketClosedError.prototype);
  }
};
var ResettableTimeout = class {
  /**
  * Resets the current timeout, restarting it with the same duration.
  * Does nothing if no timeout is active.
  */
  reset() {
    if (!this.timeout) return;
    clearTimeout(this.timeout);
    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);
  }
  start() {
    clearTimeout(this.timeout);
    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);
  }
  stop() {
    clearTimeout(this.timeout);
    this.timeout = void 0;
  }
  constructor(onTimeout, timeoutMs) {
    _define_property7(this, "onTimeout", void 0);
    _define_property7(this, "timeoutMs", void 0);
    _define_property7(this, "timeout", void 0);
    this.onTimeout = onTimeout;
    this.timeoutMs = timeoutMs;
  }
};
function withResolvers2() {
  let resolve;
  let reject;
  const promise = new Promise((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return {
    promise,
    resolve,
    reject
  };
}
async function prepareUrl(urlOptions) {
  const url = await resultOf(urlOptions.url);
  if (!urlOptions.connectionParams) return url;
  const prefix = url.includes("?") ? "&" : "?";
  const connectionParams = `${prefix}connectionParams=1`;
  return url + connectionParams;
}
async function buildConnectionMessage(connectionParams) {
  const message = {
    method: "connectionParams",
    data: await resultOf(connectionParams)
  };
  return JSON.stringify(message);
}

// node_modules/@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs
function _define_property8(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var RequestManager = class {
  /**
  * Registers a new request by adding it to the outgoing queue and setting up
  * callbacks for lifecycle events such as completion or error.
  *
  * @param message - The outgoing message to be sent.
  * @param callbacks - Callback functions to observe the request's state.
  * @returns A cleanup function to manually remove the request.
  */
  register(message, callbacks) {
    const { promise: end, resolve } = withResolvers2();
    this.outgoingRequests.push({
      id: String(message.id),
      message,
      end,
      callbacks: {
        next: callbacks.next,
        complete: () => {
          callbacks.complete();
          resolve();
        },
        error: (e) => {
          callbacks.error(e);
          resolve();
        }
      }
    });
    return () => {
      this.delete(message.id);
      callbacks.complete();
      resolve();
    };
  }
  /**
  * Deletes a request from both the outgoing and pending collections, if it exists.
  */
  delete(messageId) {
    if (messageId === null) return;
    this.outgoingRequests = this.outgoingRequests.filter(({ id }) => id !== String(messageId));
    delete this.pendingRequests[String(messageId)];
  }
  /**
  * Moves all outgoing requests to the pending state and clears the outgoing queue.
  *
  * The caller is expected to handle the actual sending of the requests
  * (e.g., sending them over the network) after this method is called.
  *
  * @returns The list of requests that were transitioned to the pending state.
  */
  flush() {
    const requests = this.outgoingRequests;
    this.outgoingRequests = [];
    for (const request of requests) {
      this.pendingRequests[request.id] = request;
    }
    return requests;
  }
  /**
  * Retrieves all currently pending requests, which are in flight awaiting responses
  * or handling ongoing subscriptions.
  */
  getPendingRequests() {
    return Object.values(this.pendingRequests);
  }
  /**
  * Retrieves a specific pending request by its message ID.
  */
  getPendingRequest(messageId) {
    if (messageId === null) return null;
    return this.pendingRequests[String(messageId)];
  }
  /**
  * Retrieves all outgoing requests, which are waiting to be sent.
  */
  getOutgoingRequests() {
    return this.outgoingRequests;
  }
  /**
  * Retrieves all requests, both outgoing and pending, with their respective states.
  *
  * @returns An array of all requests with their state ("outgoing" or "pending").
  */
  getRequests() {
    return [
      ...this.getOutgoingRequests().map((request) => ({
        state: "outgoing",
        message: request.message,
        end: request.end,
        callbacks: request.callbacks
      })),
      ...this.getPendingRequests().map((request) => ({
        state: "pending",
        message: request.message,
        end: request.end,
        callbacks: request.callbacks
      }))
    ];
  }
  /**
  * Checks if there are any pending requests, including ongoing subscriptions.
  */
  hasPendingRequests() {
    return this.getPendingRequests().length > 0;
  }
  /**
  * Checks if there are any pending subscriptions
  */
  hasPendingSubscriptions() {
    return this.getPendingRequests().some((request) => request.message.method === "subscription");
  }
  /**
  * Checks if there are any outgoing requests waiting to be sent.
  */
  hasOutgoingRequests() {
    return this.outgoingRequests.length > 0;
  }
  constructor() {
    _define_property8(this, "outgoingRequests", new Array());
    _define_property8(this, "pendingRequests", {});
  }
};

// node_modules/@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs
function _define_property9(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function asyncWsOpen(ws) {
  const { promise, resolve, reject } = withResolvers2();
  ws.addEventListener("open", () => {
    ws.removeEventListener("error", reject);
    resolve();
  });
  ws.addEventListener("error", reject);
  return promise;
}
function setupPingInterval(ws, { intervalMs, pongTimeoutMs }) {
  let pingTimeout;
  let pongTimeout;
  function start() {
    pingTimeout = setTimeout(() => {
      ws.send("PING");
      pongTimeout = setTimeout(() => {
        ws.close();
      }, pongTimeoutMs);
    }, intervalMs);
  }
  function reset() {
    clearTimeout(pingTimeout);
    start();
  }
  function pong() {
    clearTimeout(pongTimeout);
    reset();
  }
  ws.addEventListener("open", start);
  ws.addEventListener("message", ({ data }) => {
    clearTimeout(pingTimeout);
    start();
    if (data === "PONG") {
      pong();
    }
  });
  ws.addEventListener("close", () => {
    clearTimeout(pingTimeout);
    clearTimeout(pongTimeout);
  });
}
var WsConnection = class _WsConnection {
  get ws() {
    return this.wsObservable.get();
  }
  set ws(ws) {
    this.wsObservable.next(ws);
  }
  /**
  * Checks if the WebSocket connection is open and ready to communicate.
  */
  isOpen() {
    return !!this.ws && this.ws.readyState === this.WebSocketPonyfill.OPEN && !this.openPromise;
  }
  /**
  * Checks if the WebSocket connection is closed or in the process of closing.
  */
  isClosed() {
    return !!this.ws && (this.ws.readyState === this.WebSocketPonyfill.CLOSING || this.ws.readyState === this.WebSocketPonyfill.CLOSED);
  }
  async open() {
    if (this.openPromise) return this.openPromise;
    this.id = ++_WsConnection.connectCount;
    const wsPromise = prepareUrl(this.urlOptions).then((url) => new this.WebSocketPonyfill(url));
    this.openPromise = wsPromise.then(async (ws) => {
      this.ws = ws;
      ws.addEventListener("message", function({ data }) {
        if (data === "PING") {
          this.send("PONG");
        }
      });
      if (this.keepAliveOpts.enabled) {
        setupPingInterval(ws, this.keepAliveOpts);
      }
      ws.addEventListener("close", () => {
        if (this.ws === ws) {
          this.ws = null;
        }
      });
      await asyncWsOpen(ws);
      if (this.urlOptions.connectionParams) {
        ws.send(await buildConnectionMessage(this.urlOptions.connectionParams));
      }
    });
    try {
      await this.openPromise;
    } finally {
      this.openPromise = null;
    }
  }
  /**
  * Closes the WebSocket connection gracefully.
  * Waits for any ongoing open operation to complete before closing.
  */
  async close() {
    var _a2;
    try {
      await this.openPromise;
    } finally {
      (_a2 = this.ws) == null ? void 0 : _a2.close();
    }
  }
  constructor(opts) {
    _define_property9(this, "id", ++_WsConnection.connectCount);
    _define_property9(this, "WebSocketPonyfill", void 0);
    _define_property9(this, "urlOptions", void 0);
    _define_property9(this, "keepAliveOpts", void 0);
    _define_property9(this, "wsObservable", behaviorSubject(null));
    _define_property9(this, "openPromise", null);
    this.WebSocketPonyfill = opts.WebSocketPonyfill ?? WebSocket;
    if (!this.WebSocketPonyfill) {
      throw new Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");
    }
    this.urlOptions = opts.urlOptions;
    this.keepAliveOpts = opts.keepAlive;
  }
};
_define_property9(WsConnection, "connectCount", 0);
function backwardCompatibility(connection) {
  if (connection.isOpen()) {
    return {
      id: connection.id,
      state: "open",
      ws: connection.ws
    };
  }
  if (connection.isClosed()) {
    return {
      id: connection.id,
      state: "closed",
      ws: connection.ws
    };
  }
  if (!connection.ws) {
    return null;
  }
  return {
    id: connection.id,
    state: "connecting",
    ws: connection.ws
  };
}

// node_modules/@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs
function _define_property10(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var WsClient = class {
  /**
  * Opens the WebSocket connection. Handles reconnection attempts and updates
  * the connection state accordingly.
  */
  async open() {
    this.allowReconnect = true;
    if (this.connectionState.get().state !== "connecting") {
      this.connectionState.next({
        type: "state",
        state: "connecting",
        error: null
      });
    }
    try {
      await this.activeConnection.open();
    } catch (error) {
      this.reconnect(new TRPCWebSocketClosedError({
        message: "Initialization error",
        cause: error
      }));
      return this.reconnecting;
    }
  }
  /**
  * Closes the WebSocket connection and stops managing requests.
  * Ensures all outgoing and pending requests are properly finalized.
  */
  async close() {
    this.allowReconnect = false;
    this.inactivityTimeout.stop();
    const requestsToAwait = [];
    for (const request of this.requestManager.getRequests()) {
      if (request.message.method === "subscription") {
        request.callbacks.complete();
      } else if (request.state === "outgoing") {
        request.callbacks.error(TRPCClientError.from(new TRPCWebSocketClosedError({
          message: "Closed before connection was established"
        })));
      } else {
        requestsToAwait.push(request.end);
      }
    }
    await Promise.all(requestsToAwait).catch(() => null);
    await this.activeConnection.close().catch(() => null);
    this.connectionState.next({
      type: "state",
      state: "idle",
      error: null
    });
  }
  /**
  * Method to request the server.
  * Handles data transformation, batching of requests, and subscription lifecycle.
  *
  * @param op - The operation details including id, type, path, input and signal
  * @param transformer - Data transformer for serializing requests and deserializing responses
  * @param lastEventId - Optional ID of the last received event for subscriptions
  *
  * @returns An observable that emits operation results and handles cleanup
  */
  request({ op: { id, type, path, input, signal }, transformer, lastEventId }) {
    return observable((observer) => {
      const abort = this.batchSend({
        id,
        method: type,
        params: {
          input: transformer.input.serialize(input),
          path,
          lastEventId
        }
      }, {
        ...observer,
        next(event) {
          const transformed = transformResult(event, transformer.output);
          if (!transformed.ok) {
            observer.error(TRPCClientError.from(transformed.error));
            return;
          }
          observer.next({
            result: transformed.result
          });
        }
      });
      return () => {
        abort();
        if (type === "subscription" && this.activeConnection.isOpen()) {
          this.send({
            id,
            method: "subscription.stop"
          });
        }
        signal == null ? void 0 : signal.removeEventListener("abort", abort);
      };
    });
  }
  get connection() {
    return backwardCompatibility(this.activeConnection);
  }
  reconnect(closedError) {
    this.connectionState.next({
      type: "state",
      state: "connecting",
      error: TRPCClientError.from(closedError)
    });
    if (this.reconnecting) return;
    const tryReconnect = async (attemptIndex) => {
      try {
        await sleep(this.reconnectRetryDelay(attemptIndex));
        if (this.allowReconnect) {
          await this.activeConnection.close();
          await this.activeConnection.open();
          if (this.requestManager.hasPendingRequests()) {
            this.send(this.requestManager.getPendingRequests().map(({ message }) => message));
          }
        }
        this.reconnecting = null;
      } catch {
        await tryReconnect(attemptIndex + 1);
      }
    };
    this.reconnecting = tryReconnect(0);
  }
  setupWebSocketListeners(ws) {
    const handleCloseOrError = (cause) => {
      const reqs = this.requestManager.getPendingRequests();
      for (const { message, callbacks } of reqs) {
        if (message.method === "subscription") continue;
        callbacks.error(TRPCClientError.from(cause ?? new TRPCWebSocketClosedError({
          message: "WebSocket closed",
          cause
        })));
        this.requestManager.delete(message.id);
      }
    };
    ws.addEventListener("open", () => {
      run(async () => {
        var _a2, _b2;
        if (this.lazyMode) {
          this.inactivityTimeout.start();
        }
        (_b2 = (_a2 = this.callbacks).onOpen) == null ? void 0 : _b2.call(_a2);
        this.connectionState.next({
          type: "state",
          state: "pending",
          error: null
        });
      }).catch((error) => {
        ws.close(3e3);
        handleCloseOrError(error);
      });
    });
    ws.addEventListener("message", ({ data }) => {
      this.inactivityTimeout.reset();
      if (typeof data !== "string" || [
        "PING",
        "PONG"
      ].includes(data)) return;
      const incomingMessage = JSON.parse(data);
      if ("method" in incomingMessage) {
        this.handleIncomingRequest(incomingMessage);
        return;
      }
      this.handleResponseMessage(incomingMessage);
    });
    ws.addEventListener("close", (event) => {
      var _a2, _b2;
      handleCloseOrError(event);
      (_b2 = (_a2 = this.callbacks).onClose) == null ? void 0 : _b2.call(_a2, event);
      if (!this.lazyMode || this.requestManager.hasPendingSubscriptions()) {
        this.reconnect(new TRPCWebSocketClosedError({
          message: "WebSocket closed",
          cause: event
        }));
      }
    });
    ws.addEventListener("error", (event) => {
      var _a2, _b2;
      handleCloseOrError(event);
      (_b2 = (_a2 = this.callbacks).onError) == null ? void 0 : _b2.call(_a2, event);
      this.reconnect(new TRPCWebSocketClosedError({
        message: "WebSocket closed",
        cause: event
      }));
    });
  }
  handleResponseMessage(message) {
    const request = this.requestManager.getPendingRequest(message.id);
    if (!request) return;
    request.callbacks.next(message);
    let completed = true;
    if ("result" in message && request.message.method === "subscription") {
      if (message.result.type === "data") {
        request.message.params.lastEventId = message.result.id;
      }
      if (message.result.type !== "stopped") {
        completed = false;
      }
    }
    if (completed) {
      request.callbacks.complete();
      this.requestManager.delete(message.id);
    }
  }
  handleIncomingRequest(message) {
    if (message.method === "reconnect") {
      this.reconnect(new TRPCWebSocketClosedError({
        message: "Server requested reconnect"
      }));
    }
  }
  /**
  * Sends a message or batch of messages directly to the server.
  */
  send(messageOrMessages) {
    if (!this.activeConnection.isOpen()) {
      throw new Error("Active connection is not open");
    }
    const messages = messageOrMessages instanceof Array ? messageOrMessages : [
      messageOrMessages
    ];
    this.activeConnection.ws.send(JSON.stringify(messages.length === 1 ? messages[0] : messages));
  }
  /**
  * Groups requests for batch sending.
  *
  * @returns A function to abort the batched request.
  */
  batchSend(message, callbacks) {
    this.inactivityTimeout.reset();
    run(async () => {
      if (!this.activeConnection.isOpen()) {
        await this.open();
      }
      await sleep(0);
      if (!this.requestManager.hasOutgoingRequests()) return;
      this.send(this.requestManager.flush().map(({ message: message2 }) => message2));
    }).catch((err) => {
      this.requestManager.delete(message.id);
      callbacks.error(TRPCClientError.from(err));
    });
    return this.requestManager.register(message, callbacks);
  }
  constructor(opts) {
    _define_property10(this, "connectionState", void 0);
    _define_property10(this, "allowReconnect", false);
    _define_property10(this, "requestManager", new RequestManager());
    _define_property10(this, "activeConnection", void 0);
    _define_property10(this, "reconnectRetryDelay", void 0);
    _define_property10(this, "inactivityTimeout", void 0);
    _define_property10(this, "callbacks", void 0);
    _define_property10(this, "lazyMode", void 0);
    _define_property10(this, "reconnecting", null);
    this.callbacks = {
      onOpen: opts.onOpen,
      onClose: opts.onClose,
      onError: opts.onError
    };
    const lazyOptions = {
      ...lazyDefaults,
      ...opts.lazy
    };
    this.inactivityTimeout = new ResettableTimeout(() => {
      if (this.requestManager.hasOutgoingRequests() || this.requestManager.hasPendingRequests()) {
        this.inactivityTimeout.reset();
        return;
      }
      this.close().catch(() => null);
    }, lazyOptions.closeMs);
    this.activeConnection = new WsConnection({
      WebSocketPonyfill: opts.WebSocket,
      urlOptions: opts,
      keepAlive: {
        ...keepAliveDefaults,
        ...opts.keepAlive
      }
    });
    this.activeConnection.wsObservable.subscribe({
      next: (ws) => {
        if (!ws) return;
        this.setupWebSocketListeners(ws);
      }
    });
    this.reconnectRetryDelay = opts.retryDelayMs ?? exponentialBackoff;
    this.lazyMode = lazyOptions.enabled;
    this.connectionState = behaviorSubject({
      type: "state",
      state: lazyOptions.enabled ? "idle" : "connecting",
      error: null
    });
    if (!this.lazyMode) {
      this.open().catch(() => null);
    }
  }
};

// node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs
function createWSClient(opts) {
  return new WsClient(opts);
}

// node_modules/@trpc/client/dist/links/wsLink/wsLink.mjs
function wsLink(opts) {
  const { client } = opts;
  const transformer = getTransformer(opts.transformer);
  return () => {
    return ({ op }) => {
      return observable((observer) => {
        const connStateSubscription = op.type === "subscription" ? client.connectionState.subscribe({
          next(result) {
            observer.next({
              result,
              context: op.context
            });
          }
        }) : null;
        const requestSubscription = client.request({
          op,
          transformer
        }).subscribe(observer);
        return () => {
          requestSubscription.unsubscribe();
          connStateSubscription == null ? void 0 : connStateSubscription.unsubscribe();
        };
      });
    };
  };
}

// node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs
function inputWithTrackedEventId(input, lastEventId) {
  if (!lastEventId) {
    return input;
  }
  if (input != null && typeof input !== "object") {
    return input;
  }
  return {
    ...input ?? {},
    lastEventId
  };
}

// node_modules/@trpc/client/dist/links/httpSubscriptionLink.mjs
async function urlWithConnectionParams(opts) {
  let url = await resultOf(opts.url);
  if (opts.connectionParams) {
    const params = await resultOf(opts.connectionParams);
    const prefix = url.includes("?") ? "&" : "?";
    url += prefix + "connectionParams=" + encodeURIComponent(JSON.stringify(params));
  }
  return url;
}
var codes5xx = [
  TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,
  TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,
  TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,
  TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR
];
function httpSubscriptionLink(opts) {
  const transformer = getTransformer(opts.transformer);
  return () => {
    return ({ op }) => {
      return observable((observer) => {
        const { type, path, input } = op;
        if (type !== "subscription") {
          throw new Error("httpSubscriptionLink only supports subscriptions");
        }
        let lastEventId = void 0;
        const ac = new AbortController();
        const signal = raceAbortSignals(op.signal, ac.signal);
        const eventSourceStream = sseStreamConsumer({
          url: async () => getUrl({
            transformer,
            url: await urlWithConnectionParams(opts),
            input: inputWithTrackedEventId(input, lastEventId),
            path,
            type,
            signal: null
          }),
          init: () => resultOf(opts.eventSourceOptions, {
            op
          }),
          signal,
          deserialize: transformer.output.deserialize,
          EventSource: opts.EventSource ?? globalThis.EventSource
        });
        const connectionState = behaviorSubject({
          type: "state",
          state: "connecting",
          error: null
        });
        const connectionSub = connectionState.subscribe({
          next(state) {
            observer.next({
              result: state
            });
          }
        });
        run(async () => {
          for await (const chunk of eventSourceStream) {
            switch (chunk.type) {
              case "ping":
                break;
              case "data":
                const chunkData = chunk.data;
                let result;
                if (chunkData.id) {
                  lastEventId = chunkData.id;
                  result = {
                    id: chunkData.id,
                    data: chunkData
                  };
                } else {
                  result = {
                    data: chunkData.data
                  };
                }
                observer.next({
                  result,
                  context: {
                    eventSource: chunk.eventSource
                  }
                });
                break;
              case "connected": {
                observer.next({
                  result: {
                    type: "started"
                  },
                  context: {
                    eventSource: chunk.eventSource
                  }
                });
                connectionState.next({
                  type: "state",
                  state: "pending",
                  error: null
                });
                break;
              }
              case "serialized-error": {
                const error = TRPCClientError.from({
                  error: chunk.error
                });
                if (codes5xx.includes(chunk.error.code)) {
                  connectionState.next({
                    type: "state",
                    state: "connecting",
                    error
                  });
                  break;
                }
                throw error;
              }
              case "connecting": {
                const lastState = connectionState.get();
                const error = chunk.event && TRPCClientError.from(chunk.event);
                if (!error && lastState.state === "connecting") {
                  break;
                }
                connectionState.next({
                  type: "state",
                  state: "connecting",
                  error
                });
                break;
              }
              case "timeout": {
                connectionState.next({
                  type: "state",
                  state: "connecting",
                  error: new TRPCClientError(`Timeout of ${chunk.ms}ms reached while waiting for a response`)
                });
              }
            }
          }
          observer.next({
            result: {
              type: "stopped"
            }
          });
          connectionState.next({
            type: "state",
            state: "idle",
            error: null
          });
          observer.complete();
        }).catch((error) => {
          observer.error(TRPCClientError.from(error));
        });
        return () => {
          observer.complete();
          ac.abort();
          connectionSub.unsubscribe();
        };
      });
    };
  };
}
var unstable_httpSubscriptionLink = httpSubscriptionLink;

// node_modules/@trpc/client/dist/links/retryLink.mjs
function retryLink(opts) {
  return () => {
    return (callOpts) => {
      return observable((observer) => {
        let next$;
        let callNextTimeout = void 0;
        let lastEventId = void 0;
        attempt(1);
        function opWithLastEventId() {
          const op = callOpts.op;
          if (!lastEventId) {
            return op;
          }
          return {
            ...op,
            input: inputWithTrackedEventId(op.input, lastEventId)
          };
        }
        function attempt(attempts) {
          const op = opWithLastEventId();
          next$ = callOpts.next(op).subscribe({
            error(error) {
              var _a2;
              const shouldRetry = opts.retry({
                op,
                attempts,
                error
              });
              if (!shouldRetry) {
                observer.error(error);
                return;
              }
              const delayMs = ((_a2 = opts.retryDelayMs) == null ? void 0 : _a2.call(opts, attempts)) ?? 0;
              if (delayMs <= 0) {
                attempt(attempts + 1);
                return;
              }
              callNextTimeout = setTimeout(() => attempt(attempts + 1), delayMs);
            },
            next(envelope) {
              if ((!envelope.result.type || envelope.result.type === "data") && envelope.result.id) {
                lastEventId = envelope.result.id;
              }
              observer.next(envelope);
            },
            complete() {
              observer.complete();
            }
          });
        }
        return () => {
          next$.unsubscribe();
          clearTimeout(callNextTimeout);
        };
      });
    };
  };
}

export {
  createRecursiveProxy,
  createFlatProxy,
  isObject,
  isAsyncIterable,
  TRPCClientError,
  TRPCUntypedClient,
  createTRPCUntypedClient,
  clientCallTypeToProcedureType,
  createTRPCClientProxy,
  createTRPCClient,
  getUntypedClient,
  getFetch,
  isOctetType,
  isFormData,
  isNonJsonSerializable,
  httpBatchLink,
  httpBatchStreamLink,
  unstable_httpBatchStreamLink,
  httpLink,
  loggerLink,
  splitLink,
  createWSClient,
  wsLink,
  httpSubscriptionLink,
  unstable_httpSubscriptionLink,
  retryLink
};
/*! Bundled license information:

@trpc/server/dist/unstable-core-do-not-import/rpc/parseTRPCMessage.mjs:
@trpc/server/dist/unstable-core-do-not-import/rpc/parseTRPCMessage.mjs:
@trpc/server/dist/unstable-core-do-not-import/rpc/parseTRPCMessage.mjs:
@trpc/server/dist/unstable-core-do-not-import/rpc/parseTRPCMessage.mjs:
@trpc/server/dist/unstable-core-do-not-import/rpc/parseTRPCMessage.mjs:
  (* istanbul ignore next -- @preserve *)

@trpc/client/dist/links/httpBatchLink.mjs:
@trpc/client/dist/links/httpBatchStreamLink.mjs:
@trpc/client/dist/links/httpLink.mjs:
@trpc/client/dist/links/httpSubscriptionLink.mjs:
  (* istanbul ignore if -- @preserve *)

@trpc/client/dist/links/retryLink.mjs:
  (* istanbul ignore file -- @preserve *)
*/
//# sourceMappingURL=chunk-C2XVHGVQ.js.map
