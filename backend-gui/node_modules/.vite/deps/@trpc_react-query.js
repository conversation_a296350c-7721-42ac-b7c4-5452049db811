import {
  TRPCClientError,
  TRPCUntypedClient,
  clientCallTypeToProcedureType,
  createFlatProxy,
  createRecursiveProxy,
  createTRPCClient,
  createTRPCClientProxy,
  createTRPCUntypedClient,
  createWS<PERSON>lient,
  getFetch,
  getUntypedClient,
  httpBatchLink,
  httpBatchStreamLink,
  httpLink,
  httpSubscriptionLink,
  isAsyncIterable,
  isFormData,
  isNonJsonSerializable,
  isObject,
  isOctetType,
  loggerLink,
  retryLink,
  splitLink,
  unstable_httpBatchStreamLink,
  unstable_httpSubscriptionLink,
  wsLink
} from "./chunk-C2XVHGVQ.js";
import {
  hashKey,
  infiniteQueryOptions,
  queryOptions,
  skipToken,
  useInfiniteQuery,
  useMutation,
  usePrefetchInfiniteQuery,
  usePrefetchQuery,
  useQueries,
  useQuery,
  useSuspenseInfiniteQuery,
  useSuspenseQueries,
  useSuspenseQuery
} from "./chunk-32W3KK5S.js";
import "./chunk-OBHQJEAR.js";
import {
  require_react
} from "./chunk-6L6DU33K.js";
import {
  __toESM
} from "./chunk-4MBMRILA.js";

// node_modules/@trpc/react-query/dist/internals/getQueryKey.mjs
function getQueryKeyInternal(path, input, type) {
  const splitPath = path.flatMap((part) => part.split("."));
  if (!input && (!type || type === "any")) {
    return splitPath.length ? [
      splitPath
    ] : [];
  }
  if (type === "infinite" && isObject(input) && ("direction" in input || "cursor" in input)) {
    const { cursor: _, direction: __, ...inputWithoutCursorAndDirection } = input;
    return [
      splitPath,
      {
        input: inputWithoutCursorAndDirection,
        type: "infinite"
      }
    ];
  }
  return [
    splitPath,
    {
      ...typeof input !== "undefined" && input !== skipToken && {
        input
      },
      ...type && type !== "any" && {
        type
      }
    }
  ];
}
function getMutationKeyInternal(path) {
  return getQueryKeyInternal(path, void 0, "any");
}
function getQueryKey(procedureOrRouter, ..._params) {
  const [input, type] = _params;
  const path = procedureOrRouter._def().path;
  const queryKey = getQueryKeyInternal(path, input, type ?? "any");
  return queryKey;
}
function getMutationKey(procedure) {
  const path = procedure._def().path;
  return getMutationKeyInternal(path);
}

// node_modules/@trpc/react-query/dist/createTRPCReact.mjs
var React4 = __toESM(require_react(), 1);

// node_modules/@trpc/react-query/dist/shared/proxy/decorationProxy.mjs
function createReactDecoration(hooks) {
  return createRecursiveProxy(({ path, args }) => {
    const pathCopy = [
      ...path
    ];
    const lastArg = pathCopy.pop();
    if (lastArg === "useMutation") {
      return hooks[lastArg](pathCopy, ...args);
    }
    if (lastArg === "_def") {
      return {
        path: pathCopy
      };
    }
    const [input, ...rest] = args;
    const opts = rest[0] ?? {};
    return hooks[lastArg](pathCopy, input, opts);
  });
}

// node_modules/@trpc/react-query/dist/internals/context.mjs
var React = __toESM(require_react(), 1);
var contextProps = [
  "client",
  "ssrContext",
  "ssrState",
  "abortOnUnmount"
];
var _a;
var TRPCContext = (_a = React.createContext) == null ? void 0 : _a(null);

// node_modules/@trpc/react-query/dist/shared/proxy/utilsProxy.mjs
var getQueryType = (utilName) => {
  switch (utilName) {
    case "queryOptions":
    case "fetch":
    case "ensureData":
    case "prefetch":
    case "getData":
    case "setData":
    case "setQueriesData":
      return "query";
    case "infiniteQueryOptions":
    case "fetchInfinite":
    case "prefetchInfinite":
    case "getInfiniteData":
    case "setInfiniteData":
      return "infinite";
    case "setMutationDefaults":
    case "getMutationDefaults":
    case "isMutating":
    case "cancel":
    case "invalidate":
    case "refetch":
    case "reset":
      return "any";
  }
};
function createRecursiveUtilsProxy(context) {
  return createRecursiveProxy((opts) => {
    const path = [
      ...opts.path
    ];
    const utilName = path.pop();
    const args = [
      ...opts.args
    ];
    const input = args.shift();
    const queryType = getQueryType(utilName);
    const queryKey = getQueryKeyInternal(path, input, queryType);
    const contextMap = {
      infiniteQueryOptions: () => context.infiniteQueryOptions(path, queryKey, args[0]),
      queryOptions: () => context.queryOptions(path, queryKey, ...args),
      /**
      * DecorateQueryProcedure
      */
      fetch: () => context.fetchQuery(queryKey, ...args),
      fetchInfinite: () => context.fetchInfiniteQuery(queryKey, args[0]),
      prefetch: () => context.prefetchQuery(queryKey, ...args),
      prefetchInfinite: () => context.prefetchInfiniteQuery(queryKey, args[0]),
      ensureData: () => context.ensureQueryData(queryKey, ...args),
      invalidate: () => context.invalidateQueries(queryKey, ...args),
      reset: () => context.resetQueries(queryKey, ...args),
      refetch: () => context.refetchQueries(queryKey, ...args),
      cancel: () => context.cancelQuery(queryKey, ...args),
      setData: () => {
        context.setQueryData(queryKey, args[0], args[1]);
      },
      setQueriesData: () => context.setQueriesData(queryKey, args[0], args[1], args[2]),
      setInfiniteData: () => {
        context.setInfiniteQueryData(queryKey, args[0], args[1]);
      },
      getData: () => context.getQueryData(queryKey),
      getInfiniteData: () => context.getInfiniteQueryData(queryKey),
      /**
      * DecorateMutationProcedure
      */
      setMutationDefaults: () => context.setMutationDefaults(getMutationKeyInternal(path), input),
      getMutationDefaults: () => context.getMutationDefaults(getMutationKeyInternal(path)),
      isMutating: () => context.isMutating({
        mutationKey: getMutationKeyInternal(path)
      })
    };
    return contextMap[utilName]();
  });
}
function createReactQueryUtils(context) {
  const clientProxy = createTRPCClientProxy(context.client);
  const proxy = createRecursiveUtilsProxy(context);
  return createFlatProxy((key) => {
    const contextName = key;
    if (contextName === "client") {
      return clientProxy;
    }
    if (contextProps.includes(contextName)) {
      return context[contextName];
    }
    return proxy[key];
  });
}
function createQueryUtilsProxy(context) {
  return createRecursiveUtilsProxy(context);
}

// node_modules/@trpc/react-query/dist/shared/hooks/createHooksInternal.mjs
var React3 = __toESM(require_react(), 1);

// node_modules/@trpc/react-query/dist/internals/getClientArgs.mjs
function getClientArgs(queryKey, opts, infiniteParams) {
  var _a2;
  const path = queryKey[0];
  let input = (_a2 = queryKey[1]) == null ? void 0 : _a2.input;
  if (infiniteParams) {
    input = {
      ...input ?? {},
      ...infiniteParams.pageParam ? {
        cursor: infiniteParams.pageParam
      } : {},
      direction: infiniteParams.direction
    };
  }
  return [
    path.join("."),
    input,
    opts == null ? void 0 : opts.trpc
  ];
}

// node_modules/@trpc/react-query/dist/internals/trpcResult.mjs
var React2 = __toESM(require_react(), 1);
function createTRPCOptionsResult(value) {
  const path = value.path.join(".");
  return {
    path
  };
}
function useHookResult(value) {
  const result = createTRPCOptionsResult(value);
  return React2.useMemo(() => result, [
    result
  ]);
}
async function buildQueryFromAsyncIterable(asyncIterable, queryClient, queryKey) {
  const queryCache = queryClient.getQueryCache();
  const query = queryCache.build(queryClient, {
    queryKey
  });
  query.setState({
    data: [],
    status: "success"
  });
  const aggregate = [];
  for await (const value of asyncIterable) {
    aggregate.push(value);
    query.setState({
      data: [
        ...aggregate
      ]
    });
  }
  return aggregate;
}

// node_modules/@trpc/react-query/dist/utils/createUtilityFunctions.mjs
function createUtilityFunctions(opts) {
  const { client, queryClient } = opts;
  const untypedClient = client instanceof TRPCUntypedClient ? client : getUntypedClient(client);
  return {
    infiniteQueryOptions: (path, queryKey, opts2) => {
      var _a2;
      const inputIsSkipToken = ((_a2 = queryKey[1]) == null ? void 0 : _a2.input) === skipToken;
      const queryFn = async (queryFnContext) => {
        var _a3;
        const actualOpts = {
          ...opts2,
          trpc: {
            ...opts2 == null ? void 0 : opts2.trpc,
            ...((_a3 = opts2 == null ? void 0 : opts2.trpc) == null ? void 0 : _a3.abortOnUnmount) ? {
              signal: queryFnContext.signal
            } : {
              signal: null
            }
          }
        };
        const result = await untypedClient.query(...getClientArgs(queryKey, actualOpts, {
          direction: queryFnContext.direction,
          pageParam: queryFnContext.pageParam
        }));
        return result;
      };
      return Object.assign(infiniteQueryOptions({
        ...opts2,
        initialData: opts2 == null ? void 0 : opts2.initialData,
        queryKey,
        queryFn: inputIsSkipToken ? skipToken : queryFn,
        initialPageParam: (opts2 == null ? void 0 : opts2.initialCursor) ?? null
      }), {
        trpc: createTRPCOptionsResult({
          path
        })
      });
    },
    queryOptions: (path, queryKey, opts2) => {
      var _a2;
      const inputIsSkipToken = ((_a2 = queryKey[1]) == null ? void 0 : _a2.input) === skipToken;
      const queryFn = async (queryFnContext) => {
        var _a3;
        const actualOpts = {
          ...opts2,
          trpc: {
            ...opts2 == null ? void 0 : opts2.trpc,
            ...((_a3 = opts2 == null ? void 0 : opts2.trpc) == null ? void 0 : _a3.abortOnUnmount) ? {
              signal: queryFnContext.signal
            } : {
              signal: null
            }
          }
        };
        const result = await untypedClient.query(...getClientArgs(queryKey, actualOpts));
        if (isAsyncIterable(result)) {
          return buildQueryFromAsyncIterable(result, queryClient, queryKey);
        }
        return result;
      };
      return Object.assign(queryOptions({
        ...opts2,
        initialData: opts2 == null ? void 0 : opts2.initialData,
        queryKey,
        queryFn: inputIsSkipToken ? skipToken : queryFn
      }), {
        trpc: createTRPCOptionsResult({
          path
        })
      });
    },
    fetchQuery: (queryKey, opts2) => {
      return queryClient.fetchQuery({
        ...opts2,
        queryKey,
        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts2))
      });
    },
    fetchInfiniteQuery: (queryKey, opts2) => {
      return queryClient.fetchInfiniteQuery({
        ...opts2,
        queryKey,
        queryFn: ({ pageParam, direction }) => {
          return untypedClient.query(...getClientArgs(queryKey, opts2, {
            pageParam,
            direction
          }));
        },
        initialPageParam: (opts2 == null ? void 0 : opts2.initialCursor) ?? null
      });
    },
    prefetchQuery: (queryKey, opts2) => {
      return queryClient.prefetchQuery({
        ...opts2,
        queryKey,
        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts2))
      });
    },
    prefetchInfiniteQuery: (queryKey, opts2) => {
      return queryClient.prefetchInfiniteQuery({
        ...opts2,
        queryKey,
        queryFn: ({ pageParam, direction }) => {
          return untypedClient.query(...getClientArgs(queryKey, opts2, {
            pageParam,
            direction
          }));
        },
        initialPageParam: (opts2 == null ? void 0 : opts2.initialCursor) ?? null
      });
    },
    ensureQueryData: (queryKey, opts2) => {
      return queryClient.ensureQueryData({
        ...opts2,
        queryKey,
        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts2))
      });
    },
    invalidateQueries: (queryKey, filters, options) => {
      return queryClient.invalidateQueries({
        ...filters,
        queryKey
      }, options);
    },
    resetQueries: (queryKey, filters, options) => {
      return queryClient.resetQueries({
        ...filters,
        queryKey
      }, options);
    },
    refetchQueries: (queryKey, filters, options) => {
      return queryClient.refetchQueries({
        ...filters,
        queryKey
      }, options);
    },
    cancelQuery: (queryKey, options) => {
      return queryClient.cancelQueries({
        queryKey
      }, options);
    },
    setQueryData: (queryKey, updater, options) => {
      return queryClient.setQueryData(queryKey, updater, options);
    },
    // eslint-disable-next-line max-params
    setQueriesData: (queryKey, filters, updater, options) => {
      return queryClient.setQueriesData({
        ...filters,
        queryKey
      }, updater, options);
    },
    getQueryData: (queryKey) => {
      return queryClient.getQueryData(queryKey);
    },
    setInfiniteQueryData: (queryKey, updater, options) => {
      return queryClient.setQueryData(queryKey, updater, options);
    },
    getInfiniteQueryData: (queryKey) => {
      return queryClient.getQueryData(queryKey);
    },
    setMutationDefaults: (mutationKey, options) => {
      const path = mutationKey[0];
      const canonicalMutationFn = (input) => {
        return untypedClient.mutation(...getClientArgs([
          path,
          {
            input
          }
        ], opts));
      };
      return queryClient.setMutationDefaults(mutationKey, typeof options === "function" ? options({
        canonicalMutationFn
      }) : options);
    },
    getMutationDefaults: (mutationKey) => {
      return queryClient.getMutationDefaults(mutationKey);
    },
    isMutating: (filters) => {
      return queryClient.isMutating({
        ...filters,
        exact: true
      });
    }
  };
}

// node_modules/@trpc/react-query/dist/shared/proxy/useQueriesProxy.mjs
function createUseQueries(client) {
  const untypedClient = client instanceof TRPCUntypedClient ? client : getUntypedClient(client);
  return createRecursiveProxy((opts) => {
    const arrayPath = opts.path;
    const dotPath = arrayPath.join(".");
    const [input, _opts] = opts.args;
    const options = {
      queryKey: getQueryKeyInternal(arrayPath, input, "query"),
      queryFn: () => {
        return untypedClient.query(dotPath, input, _opts == null ? void 0 : _opts.trpc);
      },
      ..._opts
    };
    return options;
  });
}

// node_modules/@trpc/react-query/dist/shared/hooks/createHooksInternal.mjs
var trackResult = (result, onTrackResult) => {
  const trackedResult = new Proxy(result, {
    get(target, prop) {
      onTrackResult(prop);
      return target[prop];
    }
  });
  return trackedResult;
};
function createRootHooks(config) {
  var _a2, _b;
  const mutationSuccessOverride = ((_b = (_a2 = config == null ? void 0 : config.overrides) == null ? void 0 : _a2.useMutation) == null ? void 0 : _b.onSuccess) ?? ((options) => options.originalFn());
  const Context = (config == null ? void 0 : config.context) ?? TRPCContext;
  const createClient = createTRPCClient;
  const TRPCProvider = (props) => {
    const { abortOnUnmount = false, queryClient, ssrContext } = props;
    const [ssrState, setSSRState] = React3.useState(props.ssrState ?? false);
    const client = props.client instanceof TRPCUntypedClient ? props.client : getUntypedClient(props.client);
    const fns = React3.useMemo(() => createUtilityFunctions({
      client,
      queryClient
    }), [
      client,
      queryClient
    ]);
    const contextValue = React3.useMemo(() => ({
      abortOnUnmount,
      queryClient,
      client,
      ssrContext: ssrContext ?? null,
      ssrState,
      ...fns
    }), [
      abortOnUnmount,
      client,
      fns,
      queryClient,
      ssrContext,
      ssrState
    ]);
    React3.useEffect(() => {
      setSSRState((state) => state ? "mounted" : false);
    }, []);
    return React3.createElement(Context.Provider, {
      value: contextValue
    }, props.children);
  };
  function useContext2() {
    const context = React3.useContext(Context);
    if (!context) {
      throw new Error("Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?");
    }
    return context;
  }
  function useSSRQueryOptionsIfNeeded(queryKey, opts) {
    var _a3;
    const { queryClient, ssrState } = useContext2();
    return ssrState && ssrState !== "mounted" && ((_a3 = queryClient.getQueryCache().find({
      queryKey
    })) == null ? void 0 : _a3.state.status) === "error" ? {
      retryOnMount: false,
      ...opts
    } : opts;
  }
  function useQuery$1(path, input, opts) {
    var _a3, _b2;
    const context = useContext2();
    const { abortOnUnmount, client, ssrState, queryClient, prefetchQuery } = context;
    const queryKey = getQueryKeyInternal(path, input, "query");
    const defaultOpts = queryClient.getQueryDefaults(queryKey);
    const isInputSkipToken = input === skipToken;
    if (typeof window === "undefined" && ssrState === "prepass" && ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.ssr) !== false && ((opts == null ? void 0 : opts.enabled) ?? (defaultOpts == null ? void 0 : defaultOpts.enabled)) !== false && !isInputSkipToken && !queryClient.getQueryCache().find({
      queryKey
    })) {
      void prefetchQuery(queryKey, opts);
    }
    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {
      ...defaultOpts,
      ...opts
    });
    const shouldAbortOnUnmount = ((_b2 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _b2.abortOnUnmount) ?? (config == null ? void 0 : config.abortOnUnmount) ?? abortOnUnmount;
    const hook = useQuery({
      ...ssrOpts,
      queryKey,
      queryFn: isInputSkipToken ? input : async (queryFunctionContext) => {
        const actualOpts = {
          ...ssrOpts,
          trpc: {
            ...ssrOpts == null ? void 0 : ssrOpts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {
              signal: null
            }
          }
        };
        const result = await client.query(...getClientArgs(queryKey, actualOpts));
        if (isAsyncIterable(result)) {
          return buildQueryFromAsyncIterable(result, queryClient, queryKey);
        }
        return result;
      }
    }, queryClient);
    hook.trpc = useHookResult({
      path
    });
    return hook;
  }
  function usePrefetchQuery$1(path, input, opts) {
    var _a3;
    const context = useContext2();
    const queryKey = getQueryKeyInternal(path, input, "query");
    const isInputSkipToken = input === skipToken;
    const shouldAbortOnUnmount = ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.abortOnUnmount) ?? (config == null ? void 0 : config.abortOnUnmount) ?? context.abortOnUnmount;
    usePrefetchQuery({
      ...opts,
      queryKey,
      queryFn: isInputSkipToken ? input : (queryFunctionContext) => {
        const actualOpts = {
          trpc: {
            ...opts == null ? void 0 : opts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {}
          }
        };
        return context.client.query(...getClientArgs(queryKey, actualOpts));
      }
    });
  }
  function useSuspenseQuery$1(path, input, opts) {
    var _a3;
    const context = useContext2();
    const queryKey = getQueryKeyInternal(path, input, "query");
    const shouldAbortOnUnmount = ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.abortOnUnmount) ?? (config == null ? void 0 : config.abortOnUnmount) ?? context.abortOnUnmount;
    const hook = useSuspenseQuery({
      ...opts,
      queryKey,
      queryFn: (queryFunctionContext) => {
        const actualOpts = {
          ...opts,
          trpc: {
            ...opts == null ? void 0 : opts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {
              signal: null
            }
          }
        };
        return context.client.query(...getClientArgs(queryKey, actualOpts));
      }
    }, context.queryClient);
    hook.trpc = useHookResult({
      path
    });
    return [
      hook.data,
      hook
    ];
  }
  function useMutation$1(path, opts) {
    const { client, queryClient } = useContext2();
    const mutationKey = getMutationKeyInternal(path);
    const defaultOpts = queryClient.defaultMutationOptions(queryClient.getMutationDefaults(mutationKey));
    const hook = useMutation({
      ...opts,
      mutationKey,
      mutationFn: (input) => {
        return client.mutation(...getClientArgs([
          path,
          {
            input
          }
        ], opts));
      },
      onSuccess(...args) {
        const originalFn = () => {
          var _a3, _b2;
          return ((_a3 = opts == null ? void 0 : opts.onSuccess) == null ? void 0 : _a3.call(opts, ...args)) ?? ((_b2 = defaultOpts == null ? void 0 : defaultOpts.onSuccess) == null ? void 0 : _b2.call(defaultOpts, ...args));
        };
        return mutationSuccessOverride({
          originalFn,
          queryClient,
          meta: (opts == null ? void 0 : opts.meta) ?? (defaultOpts == null ? void 0 : defaultOpts.meta) ?? {}
        });
      }
    }, queryClient);
    hook.trpc = useHookResult({
      path
    });
    return hook;
  }
  const initialStateIdle = {
    data: void 0,
    error: null,
    status: "idle"
  };
  const initialStateConnecting = {
    data: void 0,
    error: null,
    status: "connecting"
  };
  function useSubscription(path, input, opts) {
    const enabled = (opts == null ? void 0 : opts.enabled) ?? input !== skipToken;
    const queryKey = hashKey(getQueryKeyInternal(path, input, "any"));
    const { client } = useContext2();
    const optsRef = React3.useRef(opts);
    React3.useEffect(() => {
      optsRef.current = opts;
    });
    const [trackedProps] = React3.useState(/* @__PURE__ */ new Set([]));
    const addTrackedProp = React3.useCallback((key) => {
      trackedProps.add(key);
    }, [
      trackedProps
    ]);
    const currentSubscriptionRef = React3.useRef(null);
    const updateState = React3.useCallback((callback) => {
      const prev = resultRef.current;
      const next = resultRef.current = callback(prev);
      let shouldUpdate = false;
      for (const key of trackedProps) {
        if (prev[key] !== next[key]) {
          shouldUpdate = true;
          break;
        }
      }
      if (shouldUpdate) {
        setState(trackResult(next, addTrackedProp));
      }
    }, [
      addTrackedProp,
      trackedProps
    ]);
    const reset = React3.useCallback(() => {
      var _a3;
      (_a3 = currentSubscriptionRef.current) == null ? void 0 : _a3.unsubscribe();
      if (!enabled) {
        updateState(() => ({
          ...initialStateIdle,
          reset
        }));
        return;
      }
      updateState(() => ({
        ...initialStateConnecting,
        reset
      }));
      const subscription = client.subscription(path.join("."), input ?? void 0, {
        onStarted: () => {
          var _a4, _b2;
          (_b2 = (_a4 = optsRef.current).onStarted) == null ? void 0 : _b2.call(_a4);
          updateState((prev) => ({
            ...prev,
            status: "pending",
            error: null
          }));
        },
        onData: (data) => {
          var _a4, _b2;
          (_b2 = (_a4 = optsRef.current).onData) == null ? void 0 : _b2.call(_a4, data);
          updateState((prev) => ({
            ...prev,
            status: "pending",
            data,
            error: null
          }));
        },
        onError: (error) => {
          var _a4, _b2;
          (_b2 = (_a4 = optsRef.current).onError) == null ? void 0 : _b2.call(_a4, error);
          updateState((prev) => ({
            ...prev,
            status: "error",
            error
          }));
        },
        onConnectionStateChange: (result) => {
          updateState((prev) => {
            switch (result.state) {
              case "idle":
                return {
                  ...prev,
                  status: result.state,
                  error: null,
                  data: void 0
                };
              case "connecting":
                return {
                  ...prev,
                  error: result.error,
                  status: result.state
                };
              case "pending":
                return prev;
            }
          });
        },
        onComplete: () => {
          var _a4, _b2;
          (_b2 = (_a4 = optsRef.current).onComplete) == null ? void 0 : _b2.call(_a4);
          updateState((prev) => ({
            ...prev,
            status: "idle",
            error: null,
            data: void 0
          }));
        }
      });
      currentSubscriptionRef.current = subscription;
    }, [
      client,
      queryKey,
      enabled,
      updateState
    ]);
    React3.useEffect(() => {
      reset();
      return () => {
        var _a3;
        (_a3 = currentSubscriptionRef.current) == null ? void 0 : _a3.unsubscribe();
      };
    }, [
      reset
    ]);
    const resultRef = React3.useRef(enabled ? {
      ...initialStateConnecting,
      reset
    } : {
      ...initialStateIdle,
      reset
    });
    const [state, setState] = React3.useState(trackResult(resultRef.current, addTrackedProp));
    return state;
  }
  function useInfiniteQuery$1(path, input, opts) {
    var _a3, _b2;
    const { client, ssrState, prefetchInfiniteQuery, queryClient, abortOnUnmount } = useContext2();
    const queryKey = getQueryKeyInternal(path, input, "infinite");
    const defaultOpts = queryClient.getQueryDefaults(queryKey);
    const isInputSkipToken = input === skipToken;
    if (typeof window === "undefined" && ssrState === "prepass" && ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.ssr) !== false && ((opts == null ? void 0 : opts.enabled) ?? (defaultOpts == null ? void 0 : defaultOpts.enabled)) !== false && !isInputSkipToken && !queryClient.getQueryCache().find({
      queryKey
    })) {
      void prefetchInfiniteQuery(queryKey, {
        ...defaultOpts,
        ...opts
      });
    }
    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {
      ...defaultOpts,
      ...opts
    });
    const shouldAbortOnUnmount = ((_b2 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _b2.abortOnUnmount) ?? abortOnUnmount;
    const hook = useInfiniteQuery({
      ...ssrOpts,
      initialPageParam: opts.initialCursor ?? null,
      persister: opts.persister,
      queryKey,
      queryFn: isInputSkipToken ? input : (queryFunctionContext) => {
        const actualOpts = {
          ...ssrOpts,
          trpc: {
            ...ssrOpts == null ? void 0 : ssrOpts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {
              signal: null
            }
          }
        };
        return client.query(...getClientArgs(queryKey, actualOpts, {
          pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,
          direction: queryFunctionContext.direction
        }));
      }
    }, queryClient);
    hook.trpc = useHookResult({
      path
    });
    return hook;
  }
  function usePrefetchInfiniteQuery$1(path, input, opts) {
    var _a3;
    const context = useContext2();
    const queryKey = getQueryKeyInternal(path, input, "infinite");
    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);
    const isInputSkipToken = input === skipToken;
    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {
      ...defaultOpts,
      ...opts
    });
    const shouldAbortOnUnmount = ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.abortOnUnmount) ?? context.abortOnUnmount;
    usePrefetchInfiniteQuery({
      ...opts,
      initialPageParam: opts.initialCursor ?? null,
      queryKey,
      queryFn: isInputSkipToken ? input : (queryFunctionContext) => {
        const actualOpts = {
          ...ssrOpts,
          trpc: {
            ...ssrOpts == null ? void 0 : ssrOpts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {}
          }
        };
        return context.client.query(...getClientArgs(queryKey, actualOpts, {
          pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,
          direction: queryFunctionContext.direction
        }));
      }
    });
  }
  function useSuspenseInfiniteQuery$1(path, input, opts) {
    var _a3;
    const context = useContext2();
    const queryKey = getQueryKeyInternal(path, input, "infinite");
    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);
    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {
      ...defaultOpts,
      ...opts
    });
    const shouldAbortOnUnmount = ((_a3 = opts == null ? void 0 : opts.trpc) == null ? void 0 : _a3.abortOnUnmount) ?? context.abortOnUnmount;
    const hook = useSuspenseInfiniteQuery({
      ...opts,
      initialPageParam: opts.initialCursor ?? null,
      queryKey,
      queryFn: (queryFunctionContext) => {
        const actualOpts = {
          ...ssrOpts,
          trpc: {
            ...ssrOpts == null ? void 0 : ssrOpts.trpc,
            ...shouldAbortOnUnmount ? {
              signal: queryFunctionContext.signal
            } : {}
          }
        };
        return context.client.query(...getClientArgs(queryKey, actualOpts, {
          pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,
          direction: queryFunctionContext.direction
        }));
      }
    }, context.queryClient);
    hook.trpc = useHookResult({
      path
    });
    return [
      hook.data,
      hook
    ];
  }
  const useQueries$1 = (queriesCallback, options) => {
    var _a3;
    const { ssrState, queryClient, prefetchQuery, client } = useContext2();
    const proxy = createUseQueries(client);
    const queries = queriesCallback(proxy);
    if (typeof window === "undefined" && ssrState === "prepass") {
      for (const query of queries) {
        const queryOption = query;
        if (((_a3 = queryOption.trpc) == null ? void 0 : _a3.ssr) !== false && !queryClient.getQueryCache().find({
          queryKey: queryOption.queryKey
        })) {
          void prefetchQuery(queryOption.queryKey, queryOption);
        }
      }
    }
    return useQueries({
      queries: queries.map((query) => ({
        ...query,
        queryKey: query.queryKey
      })),
      combine: options == null ? void 0 : options.combine
    }, queryClient);
  };
  const useSuspenseQueries$1 = (queriesCallback) => {
    const { queryClient, client } = useContext2();
    const proxy = createUseQueries(client);
    const queries = queriesCallback(proxy);
    const hook = useSuspenseQueries({
      queries: queries.map((query) => ({
        ...query,
        queryFn: query.queryFn,
        queryKey: query.queryKey
      }))
    }, queryClient);
    return [
      hook.map((h) => h.data),
      hook
    ];
  };
  return {
    Provider: TRPCProvider,
    createClient,
    useContext: useContext2,
    useUtils: useContext2,
    useQuery: useQuery$1,
    usePrefetchQuery: usePrefetchQuery$1,
    useSuspenseQuery: useSuspenseQuery$1,
    useQueries: useQueries$1,
    useSuspenseQueries: useSuspenseQueries$1,
    useMutation: useMutation$1,
    useSubscription,
    useInfiniteQuery: useInfiniteQuery$1,
    usePrefetchInfiniteQuery: usePrefetchInfiniteQuery$1,
    useSuspenseInfiniteQuery: useSuspenseInfiniteQuery$1
  };
}

// node_modules/@trpc/react-query/dist/createTRPCReact.mjs
function createHooksInternal(trpc) {
  const proxy = createReactDecoration(trpc);
  return createFlatProxy((key) => {
    if (key === "useContext" || key === "useUtils") {
      return () => {
        const context = trpc.useUtils();
        return React4.useMemo(() => {
          return createReactQueryUtils(context);
        }, [
          context
        ]);
      };
    }
    if (trpc.hasOwnProperty(key)) {
      return trpc[key];
    }
    return proxy[key];
  });
}
function createTRPCReact(opts) {
  const hooks = createRootHooks(opts);
  const proxy = createHooksInternal(hooks);
  return proxy;
}

// node_modules/@trpc/react-query/dist/createTRPCQueryUtils.mjs
var import_react = __toESM(require_react(), 1);
function createTRPCQueryUtils(opts) {
  const utils = createUtilityFunctions(opts);
  return createQueryUtilsProxy(utils);
}
export {
  TRPCClientError,
  TRPCUntypedClient,
  clientCallTypeToProcedureType,
  createTRPCClient,
  createTRPCClientProxy,
  createTRPCClient as createTRPCProxyClient,
  createTRPCQueryUtils,
  createTRPCReact,
  createTRPCUntypedClient,
  createWSClient,
  getFetch,
  getMutationKey,
  getQueryKey,
  getUntypedClient,
  httpBatchLink,
  httpBatchStreamLink,
  httpLink,
  httpSubscriptionLink,
  isFormData,
  isNonJsonSerializable,
  isOctetType,
  loggerLink,
  retryLink,
  splitLink,
  unstable_httpBatchStreamLink,
  unstable_httpSubscriptionLink,
  wsLink
};
/*! Bundled license information:

@trpc/react-query/dist/shared/hooks/createHooksInternal.mjs:
  (* istanbul ignore next -- @preserve *)
*/
//# sourceMappingURL=@trpc_react-query.js.map
